using System.Collections.Generic;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.Command; // 添加扩展方法所在的命名空间
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Services.Interfaces;

namespace Zishan.SS200.Cmd.Commands.CommandSpec.Robot.TAxis
{
    /// <summary>
    /// AR1 - T轴Smooth端移动到工艺腔室A
    /// </summary>
    public class AR1Command : BaseCommandSpec<EnuRobotCmd>
    {
        /// <summary>
        /// 状态管理实例
        /// </summary>
        private readonly S200MockStatus _mockStatus = S200MockStatus.Instance;

        /// <summary>
        /// 构造函数
        /// </summary>
        public AR1Command() : base(
            EnuMcuDeviceType.Robot,
            EnuRobotCmd.AR1,
            EnuRobotCmd.AR1.ToString(),
            "T-axis Smooth end move to chamber A",
            "T轴Smooth端移动到工艺腔室A",

            30000) // 根据复杂的流程，将超时延长到30秒
        {
        }

        /// <summary>
        /// 参数验证
        /// </summary>
        /// <param name="parameters">参数列表</param>
        /// <returns>是否有效</returns>
        public override bool ValidateParameters(List<ushort> parameters)
        {
            // 当前命令不需要参数
            return true;
            //return parameters == null || parameters.Count == 0;
        }

        /// <summary>
        /// 执行前处理 - 根据AR1命令逻辑图实现前置条件检查
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="parameters">参数列表</param>
        /// <returns>是否继续执行命令</returns>
        public override async Task<bool> BeforeExecuteAsync(IS200McuCmdService cmdService, List<ushort> parameters)
        {
            _logger.Info("开始执行AR1命令前置检查");

            // 1. 检查机器人状态 - MRS1~MRS3
            if (!_mockStatus.CheckRobotStatus(cmdService))
            {
                return false;
            }

            // 2. 检查T轴位置状态 - 需要确定当前是否在RS1或其他位置
            bool isAtRS1 = await _mockStatus.IsRobotAtPositionAsync(cmdService, "RS1");
            if (isAtRS1)
            {
                _logger.Info("T轴当前已在RS1位置，命令完成");
                return false; // 已经在目标位置，不需要执行命令
            }

            // 3. 检查R轴位置状态
            bool isAtRS18 = await _mockStatus.IsRobotAtPositionAsync(cmdService, "RS18");
            if (!isAtRS18)
            {
                _logger.Error("R轴不在零位(RS18)，无法执行T轴移动");
                return false;
            }

            // 4. 检查滑出后传感器安装状态 - SPS11
            bool sps11Status = await _mockStatus.GetSensorStatusAsync(cmdService, "SPS11");

            // 5. 根据SPS11状态走不同流程分支
            if (sps11Status)
            {
                // SPS11=Y，检查Shuttle滑出传感器状态
                bool di19Status = await _mockStatus.GetShuttleSensorStatusAsync(cmdService, 19);
                bool di20Status = await _mockStatus.GetShuttleSensorStatusAsync(cmdService, 20);

                if (di19Status || di20Status)
                {
                    string alarmCode = _mockStatus.GetShuttleAlarmCodeForSensorStatus(di19Status, di20Status);
                    _logger.Error($"Shuttle传感器状态异常，报警{alarmCode}");
                    return false;
                }
            }

            // 通过所有前置检查，可以执行命令
            _logger.Info("AR1命令前置检查通过，准备执行T轴移动到工艺腔室A");
            return await base.BeforeExecuteAsync(cmdService, parameters);
        }

        /// <summary>
        /// 执行后处理 - 根据AR1命令逻辑图实现后置处理逻辑
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="result">命令执行结果</param>
        /// <param name="parameters">参数列表</param>
        /// <returns>处理后的结果</returns>
        public override async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> AfterExecuteAsync(
            IS200McuCmdService cmdService,
            (string Response, ushort RunInfo, ushort ReturnInfo) result,
            List<ushort> parameters)
        {
            if (result.ReturnInfo == 0)
            {
                _logger.Info("T轴Smooth端移动命令成功发送，开始执行后续流程");

                // 根据SPS11状态执行不同的后续流程
                bool sps11Status = await _mockStatus.GetSensorStatusAsync(cmdService, "SPS11");

                if (sps11Status)
                {
                    // SPS11=Y 的流程分支

                    // 1. Z轴先移动到RPS23位置
                    _logger.Info("执行AR39命令，Z轴移动到RPS23位置");
                    // 调用扩展方法执行Z轴移动，设置steps参数为30000，其他参数使用默认值
                    var ar39Result = await cmdService.ExecuteRobotZAxisCommandAsync(30000);
                    if (ar39Result.ReturnInfo != 0)
                    {
                        _logger.Error($"Z轴移动到RPS23位置失败: {ar39Result.Response}");
                        return (result.Response + " - Z轴移动失败", result.RunInfo, 0x0101);
                    }

                    // 2. T轴移动到RP1位置
                    _logger.Info("执行AR10命令，T轴移动到RP1位置");
                    // 调用扩展方法执行T轴移动，设置steps参数为指定步进值，同时自定义startSloope参数
                    var ar10Result = await cmdService.ExecuteRobotTAxisCommandAsync(40000, 12000, 12000, 600);
                    if (ar10Result.ReturnInfo != 0)
                    {
                        _logger.Error($"T轴移动到RP1位置失败: {ar10Result.Response}");
                        return (result.Response + " - T轴移动到RP1位置失败", result.RunInfo, 0x0102);
                    }

                    // 3. 再次检查Shuttle传感器状态
                    bool di19Status = await _mockStatus.GetShuttleSensorStatusAsync(cmdService, 19);
                    bool di20Status = await _mockStatus.GetShuttleSensorStatusAsync(cmdService, 20);

                    // 根据传感器状态决定最终结果
                    if (di19Status || di20Status)
                    {
                        string alarmCode = _mockStatus.GetShuttleAlarmCodeForSensorStatus(di19Status, di20Status);
                        _logger.Error($"T轴移动完成后Shuttle传感器状态异常，报警{alarmCode}");
                        return (result.Response + $" - 传感器状态异常，报警{alarmCode}", result.RunInfo, 0x0103);
                    }
                }
                else
                {
                    // SPS11=N 的流程分支

                    // 1. Z轴先移动到RPS23位置
                    _logger.Info("执行AR39命令，Z轴移动到RPS23位置");
                    // 使用扩展方法执行命令，传递命令代码和步进值
                    var ar39Result = await cmdService.ExecuteRobotZAxisCommandAsync(30000);
                    if (ar39Result.ReturnInfo != 0)
                    {
                        _logger.Error($"Z轴移动到RPS23位置失败: {ar39Result.Response}");
                        return (result.Response + " - Z轴移动失败", result.RunInfo, 0x0101);
                    }

                    // 2. T轴移动到RP1位置
                    _logger.Info("执行AR10命令，T轴移动到RP1位置");
                    // 使用扩展方法执行命令，传递命令代码和自定义参数
                    var ar10Result = await cmdService.ExecuteRobotTAxisCommandAsync(
                        40000,
                        10000,  // 自定义起始斜率
                        10000,  // 自定义终止斜率
                        600     // 自定义运行电流
                    );
                    if (ar10Result.ReturnInfo != 0)
                    {
                        _logger.Error($"T轴移动到RP1位置失败: {ar10Result.Response}");
                        return (result.Response + " - T轴移动到RP1位置失败", result.RunInfo, 0x0102);
                    }
                }

                _logger.Info("AR1命令流程执行完成");
                return (result.Response + " - 命令流程执行完成", result.RunInfo, 0);
            }
            else
            {
                _logger.Error($"T轴移动失败，错误代码: 0x{result.ReturnInfo:X4}");

                // 根据错误代码提供更详细的错误信息
                string errorDetail = _mockStatus.GetRobotErrorDetail(result.ReturnInfo);
                if (!string.IsNullOrEmpty(errorDetail))
                {
                    result.Response = $"{result.Response} - {errorDetail}";
                }
            }

            return await base.AfterExecuteAsync(cmdService, result, parameters);
        }
    }
}