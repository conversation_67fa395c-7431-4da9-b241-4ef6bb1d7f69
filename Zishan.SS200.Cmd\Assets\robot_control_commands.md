# 半导体设备机器人控制命令表

本文档整理了半导体设备中机器人控制系统使用的命令，按功能分类并提供中英文对照。

## 1. T轴操作命令 (T-Axis Operation Commands)

T轴主要控制机器人的水平旋转运动，用于在不同工作位置之间切换。

| 命令代码 | 英文描述 | 中文描述 |
|----------|----------|----------|
| AR1 | T-axis Smooth end move to chamber A | T轴Smooth端移动到工艺腔室A |
| AR2 | T-axis Smooth end move to chamber B | T轴Smooth端移动到工艺腔室B |
| AR3 | T-axis Smooth end move to cooling chamber | T轴Smooth端移动到冷却腔 |
| AR4 | T-axis Smooth end move to cassette | T轴Smooth端移动到晶圆盒 |
| AR5 | T-axis Nose end move to chamber A | T轴Nose端移动到工艺腔室A |
| AR6 | T-axis Nose end move to chamber B | T轴Nose端移动到工艺腔室B |
| AR7 | T-axis Nose end move to cooling chamber | T轴Nose端移动到冷却腔 |
| AR8 | T-axis Nose end move to cassette | T轴Nose端移动到晶圆盒 |
| AR9 | T-axis return to zero position | T轴归零 |
| AR10 | T-axis move to specified position | T轴移动到指定位置 |

## 2. R轴操作命令 (R-Axis Operation Commands)

R轴控制机器人的径向伸缩运动，用于伸入或收回机械臂到工作位置。

| 命令代码 | 英文描述 | 中文描述 |
|----------|----------|----------|
| AR11 | R-axis Smooth end move to chamber A | R轴Smooth端移动到工艺腔室A |
| AR12 | R-axis Smooth end move to chamber B | R轴Smooth端移动到工艺腔室B |
| AR13 | R-axis Smooth end move to cooling chamber | R轴Smooth端移动到冷却腔 |
| AR14 | R-axis Smooth end move to cassette | R轴Smooth端移动到晶圆盒 |
| AR15 | R-axis Nose end move to chamber A | R轴Nose端移动到工艺腔室A |
| AR16 | R-axis Nose end move to chamber B | R轴Nose端移动到工艺腔室B |
| AR17 | R-axis Nose end move to cooling chamber | R轴Nose端移动到冷却腔 |
| AR18 | R-axis Nose end move to cassette | R轴Nose端移动到晶圆盒 |
| AR19 | R-axis return to zero position | R轴归零 |
| AR20 | R-axis move to specified position | R轴移动到指定位置 |

## 3. Z轴操作命令 (Z-Axis Operation Commands)

Z轴控制机器人的垂直升降运动，用于调整高度以便抓取或放置晶圆。

### 3.1 Z轴取晶圆高度设置 (Z-axis Get Wafer Height Settings)

| 命令代码 | 英文描述 | 中文描述 |
|----------|----------|----------|
| AR21 | Z-axis get wafer height at Smooth end from chamber A | Z轴在Smooth端位置取工艺腔室A晶圆高度 |
| AR22 | Z-axis get wafer height at Smooth end from chamber B | Z轴在Smooth端位置取工艺腔室B晶圆高度 |
| AR23 | Z-axis get wafer height at Smooth end from cooling TOP | Z轴在Smooth端位置取冷却TOP晶圆高度 |
| AR24 | Z-axis get wafer height at Smooth end from cooling BOTTOM | Z轴在Smooth端位置取冷却BOTTOM晶圆高度 |
| AR25 | Z-axis get wafer height at Nose end from chamber A | Z轴在Nose端位置取工艺腔室A晶圆高度 |
| AR26 | Z-axis get wafer height at Nose end from chamber B | Z轴在Nose端位置取工艺腔室B晶圆高度 |
| AR27 | Z-axis get wafer height at Nose end from cooling TOP | Z轴在Nose端位置取冷却TOP晶圆高度 |
| AR28 | Z-axis get wafer height at Nose end from cooling BOTTOM | Z轴在Nose端位置取冷却BOTTOM晶圆高度 |

### 3.2 Z轴放晶圆高度设置 (Z-axis Put Wafer Height Settings)

| 命令代码 | 英文描述 | 中文描述 |
|----------|----------|----------|
| AR29 | Z-axis put wafer height at Smooth end to cooling TOP | Z轴在Smooth端位置放冷却TOP晶圆 |
| AR30 | Z-axis put wafer height at Smooth end to cooling BOTTOM | Z轴在Smooth端位置放冷却BOTTOM晶圆 |
| AR31 | Z-axis put wafer height at Nose end to cooling TOP | Z轴在Nose端位置放冷却TOP晶圆 |
| AR32 | Z-axis put wafer height at Nose end to cooling BOTTOM | Z轴在Nose端位置放冷却BOTTOM晶圆 |

### 3.3 Z轴位置控制 (Z-axis Position Control)

| 命令代码 | 英文描述 | 中文描述 |
|----------|----------|----------|
| AR33 | Z-axis servo position | Z轴伺服位置 |
| AR34 | Z-axis Smooth end get position from slot | Z轴Smooth端位置取插槽 |
| AR35 | Z-axis Nose end get position from slot | Z轴Nose端位置取插槽 |
| AR36 | Z-axis Smooth end put position to slot | Z轴Smooth端位置放插槽 |
| AR37 | Z-axis Nose end put position to slot | Z轴Nose端位置放插槽 |
| AR38 | Z-axis move to pin search position | Z轴到插销搜索位置 |
| AR39 | Z-axis move to specified position | Z轴移动到指定位置 |

## 4. 特殊位置操作命令 (Special Position Operation Commands)

这些命令控制机器人的特定位置操作，如晶圆盒位置调整、腔室位置调整和插销搜索。

| 命令代码 | 英文描述 | 中文描述 |
|----------|----------|----------|
| AR40 | Cassette position increment adjustment | 晶圆盒位置增量调整 |
| AR41 | Chamber position increment adjustment | 腔室位置增量调整 |
| AR42 | Pin search | 插销搜索 |
| AR43 | Get wafer from cassette | 从晶圆盒取晶圆 |

## 5. 晶圆取放操作命令 (Wafer Handling Operation Commands)

GW(Get Wafer)和PW(Put Wafer)命令用于控制机器人从特定位置取晶圆或放晶圆到特定位置。

### 5.1 取晶圆命令 (Get Wafer Commands)

| 命令代码 | 英文描述 | 中文描述 |
|----------|----------|----------|
| AR44 | Get wafer from Smooth end position to paddle | 从Smooth端位置取晶圆到挡板 |
| AR45 | Get wafer from Nose end position to paddle | 从Nose端位置取晶圆到挡板 |
| AR47 | Get wafer from cassette to Nose end paddle | 从晶圆盒取晶圆到Nose端挡板 |
| AR49 | Get wafer from chamber A | 从工艺腔室A取晶圆 |
| AR51 | Get wafer from chamber B | 从工艺腔室B取晶圆 |
| AR53 | Get wafer from cooling TOP | 从冷却TOP取晶圆 |
| AR55 | Get wafer from cooling BOTTOM | 从冷却BOTTOM取晶圆 |

### 5.2 放晶圆命令 (Put Wafer Commands)

| 命令代码 | 英文描述 | 中文描述 |
|----------|----------|----------|
| AR46 | Put wafer from Smooth end paddle | 从Smooth端挡板放晶圆 |
| AR48 | Put wafer from Nose end paddle | 从Nose端挡板放晶圆 |
| AR50 | Put wafer to chamber A | 放晶圆到工艺腔室A |
| AR52 | Put wafer to chamber B | 放晶圆到工艺腔室B |
| AR54 | Put wafer to cooling TOP | 放晶圆到冷却TOP |
| AR56 | Put wafer to cooling BOTTOM | 放晶圆到冷却BOTTOM |
| AR59 | Put wafer from Smooth end paddle to cassette | 从Smooth端挡板放晶圆到晶圆盒 |
| AR70 | Put wafer from Nose end paddle to cassette | 从Nose端挡板放晶圆到晶圆盒 |

## 6. 晶圆状态交换操作命令 (Wafer Status Exchange Commands)

这些命令用于在不同位置之间交换晶圆状态信息，确保机器人系统能够正确追踪晶圆位置。

### 6.1 晶圆盒状态交换 (Cassette Status Exchange)

| 命令代码 | 英文描述 | 中文描述 |
|----------|----------|----------|
| AR57 | Cassette to Smooth end paddle status exchange | 晶圆盒到Smooth端挡板状态交换 |
| AR58 | Smooth end paddle to cassette status exchange | Smooth端挡板到晶圆盒状态交换 |
| AR68 | Nose end paddle to cassette status exchange | Nose端挡板到晶圆盒状态交换 |
| AR69 | Cassette to Nose end paddle status exchange | 晶圆盒到Nose端挡板状态交换 |

### 6.2 工艺腔室状态交换 (Process Chamber Status Exchange)

| 命令代码 | 英文描述 | 中文描述 |
|----------|----------|----------|
| AR60 | Smooth end paddle to chamber A status exchange | Smooth端挡板到工艺腔室A状态交换 |
| AR61 | Chamber A to Smooth end paddle status exchange | 工艺腔室A到Smooth端挡板状态交换 |
| AR62 | Smooth end paddle to chamber B status exchange | Smooth端挡板到工艺腔室B状态交换 |
| AR63 | Chamber B to Smooth end paddle status exchange | 工艺腔室B到Smooth端挡板状态交换 |
| AR71 | Nose end paddle to chamber A status exchange | Nose端挡板到工艺腔室A状态交换 |
| AR72 | Chamber A to Nose end paddle status exchange | 工艺腔室A到Nose端挡板状态交换 |
| AR73 | Nose end paddle to chamber B status exchange | Nose端挡板到工艺腔室B状态交换 |
| AR74 | Chamber B to Nose end paddle status exchange | 工艺腔室B到Nose端挡板状态交换 |

### 6.3 冷却腔状态交换 (Cooling Chamber Status Exchange)

| 命令代码 | 英文描述 | 中文描述 |
|----------|----------|----------|
| AR64 | Smooth end paddle to cooling TOP status exchange | Smooth端挡板到冷却TOP状态交换 |
| AR65 | Cooling TOP to Smooth end paddle status exchange | 冷却TOP到Smooth端挡板状态交换 |
| AR66 | Smooth end paddle to cooling BOTTOM status exchange | Smooth端挡板到冷却BOTTOM状态交换 |
| AR67 | Cooling BOTTOM to Smooth end paddle status exchange | 冷却BOTTOM到Smooth端挡板状态交换 |
| AR75 | Nose end paddle to cooling TOP status exchange | Nose端挡板到冷却TOP状态交换 |
| AR76 | Cooling TOP to Nose end paddle status exchange | 冷却TOP到Nose端挡板状态交换 |
| AR77 | Nose end paddle to cooling BOTTOM status exchange | Nose端挡板到冷却BOTTOM状态交换 |
| AR78 | Cooling BOTTOM to Nose end paddle status exchange | 冷却BOTTOM到Nose端挡板状态交换 |

## 7. 晶圆状态比较操作命令 (Wafer Status Comparison Commands)

这些命令用于比较不同位置之间的晶圆状态，确保晶圆正确传输。

| 命令代码 | 英文描述 | 中文描述 |
|----------|----------|----------|
| AR79 | Cassette and Smooth paddle status compare | 晶圆盒与Smooth端挡板状态比较 |
| AR80 | Smooth paddle and cassette status compare | Smooth端挡板与晶圆盒状态比较 |
| AR81 | Cassette and Nose paddle status compare | 晶圆盒与Nose端挡板状态比较 |
| AR82 | Nose paddle and cassette status compare | Nose端挡板与晶圆盒状态比较 |

## 8. 特定路径操作命令 (Specific Path Operation Commands)

这些命令定义了机器人在特定位置之间移动的路径，确保晶圆安全传输。

### 8.1 工艺腔室A路径 (Chamber A Path)

| 命令代码 | 英文描述 | 中文描述 |
|----------|----------|----------|
| AR83 | Get wafer from chamber A to Smooth end paddle path | 工艺腔室A到Smooth端挡板路径 |
| AR84 | Get wafer from chamber A to Nose end paddle path | 工艺腔室A到Nose端挡板路径 |
| AR87 | Put wafer from Smooth end paddle to chamber A path | Smooth端挡板到工艺腔室A路径 |
| AR88 | Put wafer from Nose end paddle to chamber A path | Nose端挡板到工艺腔室A路径 |

### 8.2 工艺腔室B路径 (Chamber B Path)

| 命令代码 | 英文描述 | 中文描述 |
|----------|----------|----------|
| AR85 | Get wafer from chamber B to Smooth end paddle path | 工艺腔室B到Smooth端挡板路径 |
| AR86 | Get wafer from chamber B to Nose end paddle path | 工艺腔室B到Nose端挡板路径 |
| AR89 | Put wafer from Smooth end paddle to chamber B path | Smooth端挡板到工艺腔室B路径 |
| AR90 | Put wafer from Nose end paddle to chamber B path | Nose端挡板到工艺腔室B路径 |

### 8.3 冷却TOP路径 (Cooling TOP Path)

| 命令代码 | 英文描述 | 中文描述 |
|----------|----------|----------|
| AR91 | Get wafer from cooling TOP to Smooth end paddle path | 冷却TOP到Smooth端挡板路径 |
| AR92 | Get wafer from cooling TOP to Nose end paddle path | 冷却TOP到Nose端挡板路径 |
| AR95 | Put wafer from Smooth end paddle to cooling TOP path | Smooth端挡板到冷却TOP路径 |
| AR96 | Put wafer from Nose end paddle to cooling TOP path | Nose端挡板到冷却TOP路径 |

### 8.4 冷却BOTTOM路径 (Cooling BOTTOM Path)

| 命令代码 | 英文描述 | 中文描述 |
|----------|----------|----------|
| AR93 | Get wafer from cooling BOTTOM to Smooth end paddle path | 冷却BOTTOM到Smooth端挡板路径 |
| AR94 | Get wafer from cooling BOTTOM to Nose end paddle path | 冷却BOTTOM到Nose端挡板路径 |
| AR97 | Put wafer from Smooth end paddle to cooling BOTTOM path | Smooth端挡板到冷却BOTTOM路径 |
| AR98 | Put wafer from Nose end paddle to cooling BOTTOM path | Nose端挡板到冷却BOTTOM路径 |

## 10. I/O信号代码 (I/O Signal Codes)

### 10.1 传感器和控制信号 (Sensor and Control Signals)

| 序号 | 信号描述 | 中文解释 | 代码 | 状态说明 |
|------|----------|----------|------|----------|
| 1 | Paddle sensor 1 left | 左挡板传感器1 | RDI1 | 0为无wafer 1为有wafer |
| 2 | Paddle sensor 2 right | 右挡板传感器2 | RDI2 | 0为无wafer 1为有wafer |
| 3 | Pin search 1 | 定位销检测1 | RDI3 | 0为未侦测到 1为已侦测到 |
| 4 | Pin search 2 | 定位销检测2 | RDI4 | 0为未侦测到 1为已侦测到 |
| 5 | Wafer slide out sensor 1 | 晶圆滑出传感器1 | RDI5 | 0为无wafer 1为有wafer |
| 6 | Wafer slide out sensor 2 | 晶圆滑出传感器2 | RDI6 | 0为无wafer 1为有wafer |
| 7 | T-axis position control | T轴位置控制 | RTC | - |
| 8 | R-axis position control | R轴位置控制 | RRC | - |
| 9 | Z-axis position control | Z轴位置控制 | RZC | - |
| 10 | T-axis position feedback | T轴位置反馈 | RTF | - |
| 11 | R-axis position feedback | R轴位置反馈 | RRF | - |
| 12 | Z-axis position feedback | Z轴位置反馈 | RZF | - |

### 10.2 信号说明 (Signal Description)

1. Paddle传感器 (RDI1, RDI2)
   - 用于检测机械臂挡板上是否有晶圆
   - 0: 无晶圆
   - 1: 有晶圆

2. Pin搜索传感器 (RDI3, RDI4)
   - 用于检测定位销
   - 0: 未侦测到定位销
   - 1: 已侦测到定位销

3. Wafer滑出传感器 (RDI5, RDI6)
   - 用于检测晶圆是否滑出
   - 0: 无晶圆
   - 1: 有晶圆

4. 轴控制信号 (RTC, RRC, RZC)
   - T轴、R轴、Z轴的位置控制信号
   - 用于控制各轴的运动

5. 轴反馈信号 (RTF, RRF, RZF)
   - T轴、R轴、Z轴的位置反馈信号
   - 用于监测各轴的实际位置 

## 11. 机器人位置值定义 (Robot Position Value Definitions)

### 11.1 位置参数表 (Position Parameters)

| 序号 | 英文描述 | 中文解释 | 位置值代码 |
|------|----------|----------|------------|
| 1 | T-axis smooth to CHA | T轴Smooth端到工艺腔室A | RP1 |
| 2 | T-axis smooth to CHB | T轴Smooth端到工艺腔室B | RP2 |
| 3 | T-axis smooth to cooling chamber | T轴Smooth端到冷却腔 | RP3 |
| 4 | T-axis smooth to cassette | T轴Smooth端到晶圆盒 | RP4 |
| 5 | T-axis nose to CHA | T轴Nose端到工艺腔室A | RP5 |
| 6 | T-axis nose to CHB | T轴Nose端到工艺腔室B | RP6 |
| 7 | T-axis nose to cooling chamber | T轴Nose端到冷却腔 | RP7 |
| 8 | T-axis nose to cassette | T轴Nose端到晶圆盒 | RP8 |
| 9 | T-axis zero | T轴零位 | RP9 |
| 10 | R-axis smooth extend and face to CHA | R轴Smooth端伸展并面向工艺腔室A | RP10 |
| 11 | R-axis smooth extend and face to CHB | R轴Smooth端伸展并面向工艺腔室B | RP11 |
| 12 | R-axis nose extend and face to CHA | R轴Nose端伸展并面向工艺腔室A | RP12 |
| 13 | R-axis nose extend and face to CHB | R轴Nose端伸展并面向工艺腔室B | RP13 |
| 14 | R-axis smooth face to cooling chamber and extend | R轴Smooth端面向冷却腔并伸展 | RP14 |
| 15 | R-axis nose extend and face to cooling chamber | R轴Nose端伸展并面向冷却腔 | RP15 |
| 16 | R-axis smooth face to cassette and extend | R轴Smooth端面向晶圆盒并伸展 | RP16 |
| 17 | R-axis nose face to cassette and extend | R轴Nose端面向晶圆盒并伸展 | RP17 |
| 18 | R-axis zero position | R轴零位 | RP18 |
| 19 | Z-axis height at smooth to CHA | Z轴在Smooth端到工艺腔室A的高度 | RP19 |
| 20 | Z-axis height at smooth to CHB | Z轴在Smooth端到工艺腔室B的高度 | RP20 |
| 21 | Z-axis height at smooth to CT | Z轴在Smooth端到冷却腔的高度 | RP21 |
| 22 | Z-axis height at smooth to CB | Z轴在Smooth端到冷却底部的高度 | RP22 |
| 23 | Z-axis height at nose to CHA | Z轴在Nose端到工艺腔室A的高度 | RP23 |
| 24 | Z-axis height at nose to CHB | Z轴在Nose端到工艺腔室B的高度 | RP24 |
| 25 | Z-axis height at nose to CT get | Z轴在Nose端到冷却顶部取片高度 | RP25 |
| 26 | Z-axis height at nose to CB get | Z轴在Nose端到冷却底部取片高度 | RP26 |
| 27 | Z-axis zero position | Z轴零位 | RP27 |
| 28 | Z-axis height to pin search | Z轴到插销检测高度 | RP28 |

### 11.2 位置参数说明 (Position Parameter Description)

1. T轴位置值 (RP1-RP9)
   - 定义了T轴旋转到各个工作位置的角度值
   - 包括Smooth端和Nose端到各个工作站的位置
   - RP9为T轴回零位置

2. R轴位置值 (RP10-RP18)
   - 定义了R轴伸缩到各个工作位置的距离值
   - 包括Smooth端和Nose端伸展时的位置
   - RP18为R轴回零位置

3. Z轴位置值 (RP19-RP28)
   - 定义了Z轴升降到各个工作高度的数值
   - 包括取放晶圆时的各个高度位置
   - RP27为Z轴回零位置
   - RP28为特殊的插销检测高度 

## 12. 机器人状态码定义 (Robot Status Code Definitions)

### 12.1 状态码表 (Status Code Table)

| item | status | status code |
|------|--------|-------------|
| 1 | Idle | MRS1 |
| 2 | Busy | MRS2 | MRS1~MRS3不能同时出现 |
| 3 | Alarm | MRS3 |
| 4 | T-axis smooth to CHA | RS1 |
| 5 | T-axis smooth to CHB | RS2 |
| 6 | T-axis smooth to cooling chamber | RS3 |
| 7 | T-axis smooth to cassette | RS4 |
| 8 | T-axis nose to CHA | RS5 |
| 9 | T-axis nose to CHB | RS6 |
| 10 | T-axis nose to cooling chamber | RS7 |
| 11 | T-axis nose to cassette | RS8 |
| 12 | T-axis zero | RS9 |
| 13 | R-axis smooth CHA extend | RS10 |
| 14 | R-axis smooth CHB extend | RS11 |
| 15 | R-axis smooth cooling chamber extend | RS12 |
| 16 | R-axis smooth cassette extend | RS13 |
| 17 | R-axis nose CHA extend | RS14 |
| 18 | R-axis nose CHB extend | RS15 |
| 19 | R-axis nose cooling chamber extend | RS16 |
| 20 | R-axis nose cassette extend | RS17 |
| 21 | R-axis zero position | RS18 |
| 22 | T-axis and Z-axis height at smooth to CHA | RS19 |
| 23 | T-axis and Z-axis height at smooth to CHB | RS20 |
| 24 | T-axis and Z-axis height at smooth to CT set | RS21 |
| 25 | T-axis and Z-axis height at smooth to CB set | RS22 |
| 26 | T-axis and Z-axis height at nose to CHA | RS23 |
| 27 | T-axis and Z-axis height at nose to CHB | RS24 |
| 28 | T-axis and Z-axis height at nose to CT set | RS25 |
| 29 | T-axis and Z-axis height at nose to CB set | RS26 |
| 30 | Z-axis zero position | RS27 |
| 31 | T-axis and Z-axis height pin search | RS28 |
| 32 | T-axis and Z-axis height at smooth to CT put | RS29 |
| 33 | T-axis and Z-axis height at smooth to CB put | RS30 |
| 34 | T-axis and Z-axis height at nose to CT put | RS31 |
| 35 | T-axis and Z-axis height at nose to CB put | RS32 |
| 36 | T-axis and Z-axis height smooth to shuttle 1 slot x get | RS33 A XX<br>RS33 B XX<br>RS33 C XX | 8'<br>6'<br>4' |
| 37 | T-axis and Z-axis height nose to shuttle 1 slot x get | RS34 A XX<br>RS34 B XX<br>RS34 C XX | 8'<br>6'<br>4' |
| 38 | T-axis and Z-axis height smooth to shuttle 1 slot x put | RS35 A XX<br>RS35 B XX<br>RS35 C XX | 8'<br>6'<br>4' |
| 39 | T-axis and Z-axis height nose to shuttle 1 slot x put | RS36 A XX<br>RS36 B XX<br>RS36 C XX | 8'<br>6'<br>4' |
| 40 | T-axis and Z-axis height smooth to shuttle 2 slot x get | RS37 A XX<br>RS37 B XX<br>RS37 C XX | 8'<br>6'<br>4' |
| 41 | T-axis and Z-axis height nose to shuttle 2 slot x get | RS38 A XX<br>RS38 B XX<br>RS38 C XX | 8'<br>6'<br>4' |
| 42 | T-axis and Z-axis height smooth to shuttle 2 slot x put | RS39 A XX<br>RS39 B XX<br>RS39 C XX | 8'<br>6'<br>4' |
| 43 | T-axis and Z-axis height nose to shuttle 2 slot x put | RS40 A XX<br>RS40 B XX<br>RS40 C XX | 8'<br>6'<br>4' |
| 44 | S1 pin search p1 | RS41 |
| 45 | S1 pin search p2 | RS42 |
| 46 | S1 pin search p3 | RS43 |
| 47 | S1 pin search p4 | RS44 |
| 48 | S2 pin search p1 | RS45 |
| 49 | S2 pin search p2 | RS46 |
| 50 | S2 pin search p3 | RS47 |
| 51 | S2 pin search p4 | RS48 |
| 52 | smooth Paddle P1 status | RS49 | RS49=0<br>RS49=1 | 0=standby<br>1=occupied |
| 53 | smooth Paddle P2 status | RS50 | RS50=0<br>RS50=1 | 0=standby<br>1=occupied |
| 54 | smooth Paddle P3 status | RS51 | RS51=0<br>RS51=1 | 0=standby<br>1=occupied |
| 55 | smooth Paddle P4 status | RS52 | RS52=0<br>RS52=1 | 0=standby<br>1=occupied |
| 56 | smooth paddle status | RS53 | RS49=0 and RS50=0<br>RS49=1 or/and RS50=1 | 0=standby<br>1=occupied |
| 57 | nose paddle status | RS54 | RS51=0 and RS52=0<br>RS51=1 or/and RS52=1 | 0=standby<br>1=occupied |
| 58 | pin search status | RS55 | RS55=0<br>RS55=1 | 0=active<br>1=invalid |
| 59 | pin search data effective | RS56 |

### 12.6 状态码说明 (Status Code Description)

1. 基本状态 (MRS1-MRS3)
   - 机器人的基本运行状态
   - 三种状态互斥，不能同时出现

2. 轴位置状态 (RS1-RS18)
   - T轴和R轴的各个位置状态
   - 包括工作位置和零位状态

3. 组合位置状态 (RS19-RS32)
   - T轴和Z轴的组合位置状态
   - 用于协调两轴的联动

4. Shuttle位置状态 (RS33-RS40)
   - 支持不同尺寸晶圆(8"/6"/4")
   - 包括取片和放片的高度位置

5. 挡板状态 (RS49-RS54)
   - 监控Smooth端和Nose端挡板的使用状态
   - 通过组合状态判断整体占用情况 

## 13. 机器人动作代码定义 (Robot Action Code Definitions)

### 13.1 动作代码表 (Action Code Table)

| item | contents | action code |
|------|----------|-------------|
| 1 | T-axis smooth to CHA | AR1 |
| 2 | T-axis smooth to CHB | AR2 |
| 3 | T-axis smooth to cooling chamber | AR3 |
| 4 | T-axis smooth to cassette | AR4 |
| 5 | T-axis nose to CHA | AR5 |
| 6 | T-axis nose to CHB | AR6 |
| 7 | T-axis nose to cooling chamber | AR7 |
| 8 | T-axis nose to cassette | AR8 |
| 9 | Zero T-axis | AR9 |
| 10 | T-axis positon to xxx | AR10 |
| 11 | R-axis smooth CHA extend | AR11 |
| 12 | R-axis smooth CHB extend | AR12 |
| 13 | R-axis smooth cooling chamber extend | AR13 |
| 14 | R-axis smooth cassette extend | AR14 |
| 15 | R-axis nose CHA extend | AR15 |
| 16 | R-axis nose CHB extend | AR16 |
| 17 | R-axis nose cooling chamber extend | AR17 |
| 18 | R-axis nose cassette extend | AR18 |
| 19 | R-axis zero position | AR19 |
| 20 | R-axis position to xxx | AR20 |
| 21 | Move Z-axis height at smooth to CHA get | AR21 |
| 22 | Move Z-axis height at smooth to CHB get | AR22 |
| 23 | Move Z-axis height at smooth to CT get | AR23 |
| 24 | Move Z-axis height at smooth to CB get | AR24 |
| 25 | Move Z-axis height at nose to CHA get | AR25 |
| 26 | Move Z-axis height at nose to CHB get | AR26 |
| 27 | Move Z-axis height at nose to CT get | AR27 |
| 28 | Move Z-axis height at nose to CB get | AR28 |
| 29 | Move Z-axis height at smooth to CT put | AR29 |
| 30 | Move Z-axis height at smooth to CB put | AR30 |
| 31 | Move Z-axis height at nose to CT put | AR31 |
| 32 | Move Z-axis height at nose to CB put | AR32 |
| 33 | Move Z-axis zero position | AR33 |
| 34 | Move Z-axis height smooth get slot xx | AR34 |
| 35 | Move Z-axis height nose get slot xx | AR35 |
| 36 | Move Z-axis height smooth put slot xx | AR36 |
| 37 | Move Z-axis height nose put slot xx | AR37 |
| 38 | Move Z-axis height to pin search | AR38 |
| 39 | Z-axis positon to xxx | AR39 |
| 40 | Z-axis cassette positon delta | AR40 |
| 41 | Z-axis chamber positon delta | AR41 |
| 42 | pin search | AR42 |
| 43 | GW XX | AR43 |
| 44 | GW XX to smooth paddle | AR44 |
| 45 | GW XX to nose paddle | AR45 |
| 46 | PW XX from smooth paddle | AR46 |
| 47 | PW XX from nose paddle | AR47 |
| 48 | PW XX | AR48 |
| 49 | GW from CHA | AR49 |
| 50 | PW from CHA | AR50 |
| 51 | GW from CHB | AR51 |
| 52 | PW from CHB | AR52 |
| 53 | GW from CT | AR53 |
| 54 | PW from CT | AR54 |
| 55 | GW from CB | AR55 |
| 56 | PW from CB | AR56 |
| 57 | wafer status exchange cassette to smooth paddle slot xx | AR57 |
| 58 | wafer status exchange smooth paddle to cassette slot xx | AR58 |
| 59 | spare | AR59 |
| 60 | wafer status exchange smooth paddle to chambe A slot xx | AR60 |
| 61 | wafer status exchange chamber A to smooth paddle  slot xx | AR61 |
| 62 | wafer status exchange smooth paddle to chambe B slot xx | AR62 |
| 63 | wafer status exchange chamber B to smooth paddle  slot xx | AR63 |
| 64 | wafer status exchange smooth paddle to CT slotxx | AR64 |
| 65 | wafer status exchange CT to smooth paddle slot xx | AR65 |
| 66 | wafer status exchange smooth paddle to CB slotxx | AR66 |
| 67 | wafer status exchange CB to smooth paddle slot xx | AR67 |
| 68 | wafer status exchange cassette to nose paddle slot xx | AR68 |
| 69 | wafer status exchange nose paddle to cassette slot xx | AR69 |
| 70 | wafer status exchange nose paddle to chambe A slot xx | AR70 |
| 71 | wafer status exchange chamber A to nose paddle  slot xx | AR71 |
| 72 | wafer status exchange nose paddle to chambe B slot xx | AR72 |
| 73 | wafer status exchange chamber B to nose paddle  slot xx | AR73 |
| 74 | wafer status exchange nose paddle to CT slotxx | AR74 |
| 75 | wafer status exchange nose paddle to CT slotxx | AR75 |
| 76 | wafer status exchange CT to nose paddle slot xx | AR76 |
| 77 | wafer status exchange nose paddle to CB slotxx | AR77 |
| 78 | wafer status exchange CB to nose paddle slot xx | AR78 |
| 79 | wafer status compare cassette to smooth paddle confirm | AR79 |
| 80 | wafer status compare smooth paddle confirm to cassette | AR80 |
| 81 | wafer status compare cassette to nose paddle confirm | AR81 |
| 82 | wafer status compare nose paddle confirm to cassette | AR82 |
| 83 | GW CHA to smooth paddle | AR83 |
| 84 | GW CHA to nose paddle | AR84 |
| 85 | GW CHB to smooth paddle | AR85 |
| 86 | GW CHB to nose paddle | AR86 |
| 87 | PW CHA from smooth paddle | AR87 |
| 88 | PW CHA from nose paddle | AR88 |
| 89 | PW CHB from smooth paddle | AR89 |
| 90 | PW CHB from nose paddle | AR90 |
| 91 | GW CT to smooth paddle | AR91 |
| 92 | GW CT to nose paddle | AR92 |
| 93 | GW CB to smooth paddle | AR93 |
| 94 | GW CB to nose paddle | AR94 |
| 95 | PW CT from smooth paddle | AR95 |
| 96 | PW CT from nose paddle | AR96 |
| 97 | PW CB from smooth paddle | AR97 |

## 14. 机器人参数设置 (Robot Parameter Settings)

### 14.1 速度和时间参数 (Speed and Time Parameters)

| 参数代码 | 参数描述 | 中文解释 | 单位 | 取值范围 |
|----------|----------|----------|------|----------|
| RPS1 | Robot rotate speed | 机器人旋转速度 | - | - |
| RPS2 | Robot extend speed | 机器人伸展速度 | - | - |
| RPS3 | Robot up down speed | 机器人上下速度 | - | - |
| RPS4 | Robot move slowly | 机器人慢速移动 | Y/N | N |
| RPS5 | T-axis move speed slowly | T轴慢速移动 | % | 25% ~ 100% |
| RPS6 | R-axis move speed slowly | R轴慢速移动 | % | 25% ~ 100% |
| RPS7 | Z-axis move speed slowly | Z轴慢速移动 | % | 25% ~ 100% |
| RPS8 | Robot rotate max time | 机器人最大旋转时间 | - | - |
| RPS9 | Robot extend max time | 机器人最大伸展时间 | - | - |
| RPS10 | Robot up down max time | 机器人最大上下时间 | - | - |

### 14.2 偏差和校准参数 (Deviation and Calibration Parameters)

| 参数代码 | 参数描述 | 中文解释 | 单位 | 取值范围 |
|----------|----------|----------|------|----------|
| RPS11 | Deviation step for T-axis home | T轴回零偏差步进 | - | - |
| RPS12 | Robot home T-axis max time | 机器人T轴回零最大时间 | - | - |
| RPS13 | Robot home R-axis max time | 机器人R轴回零最大时间 | - | - |
| RPS14 | Robot home Z-axis max time | 机器人Z轴回零最大时间 | - | - |
| RPS15 | T-axis deviation for R-axis zero | R轴回零时T轴偏差 | - | - |
| RPS16 | Z-axis step deviation for loadlock vacuum | 真空装载锁Z轴步进偏差 | - | - |
| RPS17 | Z-axis step deviation for shuttle no vacuum | 无真空ShuttleZ轴步进偏差 | - | - |

### 14.3 晶圆尺寸和位置参数 (Wafer Size and Position Parameters)

| 参数代码 | 参数描述 | 中文解释 | 单位 | 取值范围 |
|----------|----------|----------|------|----------|
| RPS18 | Zpin wafer 4"/5" | 4"/5"晶圆定位销 | - | - |
| RPS19 | Zpin wafer 6" | 6"晶圆定位销 | - | - |
| RPS20 | Zpin wafer 8" | 8"晶圆定位销 | - | - |
| RPS21 | Z-axis get/put wafer delta cassette 4" | 4"晶圆盒取放Z轴偏差 | - | - |
| RPS22 | Z-axis get/put wafer delta cassette 6" | 6"晶圆盒取放Z轴偏差 | - | - |
| RPS23 | Z-axis get/put wafer delta cassette 8" | 8"晶圆盒取放Z轴偏差 | - | - |
| RPS24 | Z-axis get/put wafer delta cooling chamber | 冷却腔取放Z轴偏差 | - | - |

### 14.4 系统检查参数 (System Check Parameters)

| 参数代码 | 参数描述 | 中文解释 | 单位 | 取值范围 |
|----------|----------|----------|------|----------|
| RPS25 | Max delta value for pin search | 插销检测最大偏差值 | - | - |
| RPS26 | Wafer actually status check | 晶圆实际状态检查 | Y/N | Y |
| RPS27 | Z-axis height for robot rotation | 机器人旋转Z轴高度 | - | - |
| RPS28 | Lowest step for pin search | 插销检测最低步进 | - | - |
| RPS29 | Chamber pressure review | 腔室压力检查 | Y/N | Y |

### 14.5 参数说明 (Parameter Description)

1. 速度控制参数
   - 包括各轴的移动速度设置
   - 慢速模式可调范围为25%~100%
   - 设有最大运行时间限制

2. 回零和偏差参数
   - 各轴回零操作的相关设置
   - 包括最大时间和偏差步进值
   - 真空和非真空状态下的偏差补偿

3. 晶圆处理参数
   - 支持4"/5"/6"/8"不同尺寸晶圆
   - 各尺寸晶圆的取放位置偏差补偿
   - 冷却腔特殊位置补偿

4. 安全检查参数
   - 晶圆状态实时检查
   - 插销检测参数设置
   - 腔室压力监控 

## 15. 机器人报警代码 (Robot Alarm Codes)

### 15.1 系统状态报警 (System Status Alarms)

| 报警代码 | 英文描述 | 中文解释 |
|----------|----------|----------|
| RA1 | Robot system status is busy, command reject | 机器人系统忙，指令被拒绝 |
| RA2 | Robot system status is alarm, command reject | 机器人系统报警，指令被拒绝 |
| RA3 | Robot R-axis not at home position, T-axis motion error | R轴未在原位，T轴运动错误 |
| RA4 | Robot T-axis not in right position, PLS confirm to R-axis motion | T轴未在正确位置，请确认R轴运动 |
| RA5 | Robot rotation time out | 机器人旋转超时 |
| RA6 | Robot extension time out | 机器人伸展超时 |
| RA7 | Robot lift time out | 机器人升降超时 |

### 15.2 设备状态报警 (Equipment Status Alarms)

| 报警代码 | 英文描述 | 中文解释 |
|----------|----------|----------|
| RA8 | CHA slit door not at open status, robot can not extend | 工艺腔室A门未打开，机器人无法伸展 |
| RA9 | CHB slit door not at open status, robot can not extend | 工艺腔室B门未打开，机器人无法伸展 |
| RA10 | Robot Z-axis not in right position, PLS confirm to T-axis motion | Z轴未在正确位置，请确认T轴运动 |
| RA11 | Robot R-axis not at home position, Z-axis can not move to right position | R轴未在原位，Z轴无法移动到正确位置 |
| RA12 | Robot T-axis not in right position, Z-axis can not move to right position | T轴未在正确位置，Z轴无法移动到正确位置 |

### 15.3 定位和检测报警 (Position and Detection Alarms)

| 报警代码 | 英文描述 | 中文解释 |
|----------|----------|----------|
| RA13 | Shuttle position status not at right position, can not do pin search | Shuttle位置不正确，无法进行插销检测 |
| RA14 | Paddle hit to pin ball, pin search failure | 挡板撞到定位销，插销检测失败 |
| RA15 | Pin ball status failure, pin search failure | 定位销状态错误，插销检测失败 |
| RA16 | Can not find pin ball, pin search failure | 找不到定位销，插销检测失败 |
| RA17 | Paddle status occupied or wafer status inconsistent, command reject | 挡板被占用或晶圆状态不一致，指令被拒绝 |
| RA18 | There is(are) wafer(s) on paddle, and pin search data invalid, command reject | 挡板上有晶圆且插销检测数据无效，指令被拒绝 |

### 15.4 晶圆检测报警 (Wafer Detection Alarms)

| 报警代码 | 英文描述 | 中文解释 |
|----------|----------|----------|
| RA19 | BL slide out sensor detector wafer slide out | 左侧晶圆滑出检测器检测到晶圆滑出 |
| RA20 | BR slide out sensor detector wafer slide out | 右侧晶圆滑出检测器检测到晶圆滑出 |
| RA21 | BL and BR slide out sensor detector wafer slide out | 左右两侧晶圆滑出检测器检测到晶圆滑出 |
| RA22 | Pin search value delta out of setpoint | 插销检测值超出设定范围 |

### 15.5 设备状态报警 (Equipment Status Alarms)

| 报警代码 | 英文描述 | 中文解释 |
|----------|----------|----------|
| RA23 | Shuttle 1 exist status is disable, command reject | Shuttle1存在状态禁用，指令被拒绝 |
| RA24 | Shuttle 2 exist status is disable, command reject | Shuttle2存在状态禁用，指令被拒绝 |
| RA25 | Shuttle position status not at right position, can not move wafer | Shuttle位置不正确，无法移动晶圆 |
| RA26-RA29 | Chamber trigger and exist status alarms | 腔室触发和存在状态报警 |
| RA30-RA33 | Chamber run status alarms | 腔室运行状态报警 |
| RA34-RA35 | Cooling chamber status alarms | 冷却腔状态报警 |

### 15.6 晶圆丢失报警 (Wafer Loss Alarms)

| 报警代码 | 英文描述 | 中文解释 |
|----------|----------|----------|
| RA36-RA38 | Cassette slot related alarms | 晶圆盒槽位相关报警 |
| RA39-RA43 | Paddle wafer loss alarms | 挡板晶圆丢失报警 |
| RA44-RA49 | Wafer put failure alarms | 晶圆放置失败报警 |
| RA50-RA53 | Wafer status inconsistency alarms | 晶圆状态不一致报警 |
| RA54-RA57 | Chamber wafer existence alarms | 腔室晶圆存在报警 |
| RA58-RA61 | Wafer status abnormal alarms | 晶圆状态异常报警 |

### 15.7 报警说明 (Alarm Description)

1. 系统状态报警
   - 涉及机器人基本运动状态
   - 包括轴位置和运动超时报警
   - 指令执行条件检查报警

2. 设备状态报警
   - 工艺腔室门状态检查
   - 各轴位置联动检查
   - Shuttle和冷却腔状态检查

3. 晶圆处理报警
   - 晶圆检测和定位报警
   - 晶圆传输和放置失败报警
   - 晶圆状态异常和丢失报警

4. 安全联锁报警
   - 防止错误操作的联锁报警
   - 设备状态不满足条件的报警
   - 晶圆安全保护相关报警 

## 16. Shuttle状态和配置 (Shuttle Status and Configuration)

### 16.1 基本状态 (Basic Status)

| 状态代码 | 英文描述 | 中文解释 | 备注 |
|----------|----------|----------|------|
| MS01 | Idle | 空闲 | DEMO/系统准备完成 |
| MS02 | Busy | 忙碌 | ACTION动作进行中(S1 S10) |
| MS03 | Alarm | 报警 | - |

### 16.2 传输位置配置 (Transfer Position Configuration)

#### 16.2.1 系统配置位置 (System Config Position)

| 状态代码 | 英文描述 | 中文解释 | 位置条件 |
|----------|----------|----------|----------|
| SS01 | Shuttle up / shuttle 1 outer position | Shuttle1上升/外部位置 | D11>=0 D14=0 D15=0 D16=0 |
| SS02 | Shuttle up / shuttle 2 outer position | Shuttle2上升/外部位置 | D11>=0 D14=1 D15=0 D16=1 |
| SS03 | Shuttle down / shuttle 1 outer position / cassette next retract | Shuttle1下降/外部位置/晶圆盒下次缩回 | D11>=0 D14=0 D15=1 D16=0 |
| SS04 | Shuttle down / shuttle 2 outer position / cassette next retract | Shuttle2下降/外部位置/晶圆盒下次缩回 | D11>=0 D14=1 D15=1 D16=1 |
| SS05 | Shuttle up down between / shuttle 1 outer position / cassette next retract | Shuttle1中间位置/外部位置/晶圆盒下次缩回 | D11>=0 D14=0 D15=0 D16=1 |
| SS06 | Shuttle up down between and shuttle 2 outer position / cassette next retract | Shuttle2中间位置/外部位置/晶圆盒下次缩回 | D11>=0 D14=1 D15=1 D16=0 |
| SS07 | Shuttle down and rotation between position | Shuttle下降和旋转中间位置 | D11>=0 D14=0 D15=0 D16=1 |

#### 16.2.2 晶圆盒门操作 (Cassette Door Operation)

| 状态代码 | 英文描述 | 中文解释 | 位置条件 |
|----------|----------|----------|----------|
| SS08 | Cassette door open / cassette next extend | 晶圆盒门打开/晶圆盒下次伸出 | D11=1 D12=0 D13=0 D14=1 D15=1 |
| SS09 | Cassette door close / cassette next retract | 晶圆盒门关闭/晶圆盒下次缩回 | D11=0 D12=1 D13=1 D14=0 D15=0 |
| SS10 | Cassette door between up and down position | 晶圆盒门中间位置 | D11=1 D12=0 |

### 16.3 阀门状态 (Valve Status)

| 状态代码 | 英文描述 | 中文解释 | 条件 |
|----------|----------|----------|------|
| SS14 | Shuttle ISO valve open | Shuttle隔离阀打开 | D122=0 D123=1 |
| SS15 | Shuttle ISO valve close | Shuttle隔离阀关闭 | D122=1 D123=0 |
| SS16 | Shuttle XV valve open | ShuttleXV阀打开 | D124=0 D125=1 |
| SS17 | Shuttle XV valve close | ShuttleXV阀关闭 | D124=1 D125=0 |
| SS18-SS19 | Shuttle backfill valve control | Shuttle回填阀控制 | D07=0/1 |
| SS20-SS23 | Loadlock valve control | 装载锁阀门控制 | D09=0/1, D010=0/1 |

### 16.4 槽位状态 (Slot Status)

#### 16.4.1 基本槽位状态 (Basic Slot Status)

| 状态代码 | 英文描述 | 中文解释 | 状态值 |
|----------|----------|----------|--------|
| LS01+1 | Lot on shuttle 1 cassette 1 | Shuttle1晶圆盒1上有晶圆 | D16=1 |
| LS01+0 | No lot on shuttle 1 cassette 1 | Shuttle1晶圆盒1上无晶圆 | D16=0 |
| LS02+1 | Lot on shuttle 1 cassette 2 | Shuttle1晶圆盒2上有晶圆 | D17=1 |
| LS02+0 | No lot on shuttle 1 cassette 2 | Shuttle1晶圆盒2上无晶圆 | D17=0 |

#### 16.4.2 晶圆状态 (Wafer Status)

| 状态代码 | 英文描述 | 中文解释 | 状态说明 |
|----------|----------|----------|----------|
| LSS1_xx - LSS4_xx | Shuttle cassette slot status | Shuttle晶圆盒槽位状态 | xx 1: 有晶圆<br>xx 0: 无晶圆<br>xx ?: 未知 |
| LSS5_xx - LSS8_xx | Paddle slot status | 挡板槽位状态 | xx 1: 有晶圆<br>xx 0: 无晶圆<br>xx ?: 未知 |
| LSS9_xx - LSS16_xx | Chamber wafer status | 腔室晶圆状态 | xx=1: 有晶圆<br>xx=0: 无晶圆 |

### 16.5 配置说明 (Configuration Description)

1. 位置配置
   - Shuttle上升/下降位置
   - 外部位置和中间位置
   - 晶圆盒伸出/缩回位置

2. 阀门控制
   - 隔离阀和XV阀控制
   - 回填阀和装载锁阀门控制
   - 各阀门状态互锁关系

3. 槽位管理
   - Shuttle槽位状态监控
   - 挡板和腔室晶圆状态监控
   - 状态检查和异常处理 

## 17. 系统I/O信号定义 (System I/O Signal Definitions)

### 17.1 Shuttle系统 (Shuttle System)

#### 17.1.1 输入信号 (Input Signals)

| 信号名称 | I/O类型 | 信号类型 | 说明 |
|----------|---------|----------|------|
| Cassette door up sensor | DI | optic NPM | 向上运动中，未到达极限=0 |
| Cassette door down sensor | DI | optic NPM | 向下运动中，未到达极限=0 |
| Cassette next extend sensor | DI | optic NPM | 到达极限=1 |
| Cassette next retract sensor | DI | optic NPM | 到达极限=1 |
| Cassette next home switch | DI | switch | home信号=0，无cassette信号=1 |
| Present sensor cassette 1 | DI | switch | 有cassette存在信号=1，无cassette存在信号=0 |
| Present sensor cassette 2 | DI | switch | 有cassette存在信号=1，无cassette存在信号=0 |
| Pin search sensor 1 | DI | switch | - |
| Pin search sensor 2 | DI | switch | - |
| Light curtain | DI | - | - |
| Shuttle rotate sensor 1 | DI | optic NPM | - |
| Shuttle rotate sensor 2 | DI | optic NPM | - |
| Shuttle up sensor | DI | optic NPM | - |
| Shuttle down sensor | DI | optic NPM | - |
| Wafer slide out sensor 1-4 | DI | photoelectric sensor | - |
| Shuttle dry pump foreline switch | DI | switch | - |
| Shuttle ISO valve open/close sensor | DI | NPM | - |

#### 17.1.2 输出信号 (Output Signals)

| 信号名称 | I/O类型 | 信号类型 | 说明 |
|----------|---------|----------|------|
| Cassette door cylinder up/down | DO | FV | - |
| Cassette door action enable | DO | FV | - |
| Cassette next cylinder extend/retract | DO | FV | - |
| Shuttle vacuum ISO valve | DO | FV | - |
| Shuttle backfill valve | DO | FV | - |
| Shuttle assy rotation CW/CCW | DO | DC motor | - |
| Shuttle motor up/down | DO | DCmotor | Change to step motor, RS485 |

### 17.2 装载锁系统 (Loadlock System)

#### 17.2.1 输入信号 (Input Signals)

| 信号名称 | I/O类型 | 信号类型 | 备注 |
|----------|---------|----------|------|
| Paddle sensor 1/2 | DI | photoelectric sensor | D123/D124 |
| RF power sensor | DI | NPM | D125 |
| CV close sensor | DI | switch | D126 |
| Loadlock thermo state | DI | switch | - |
| CHA pressure gauge | AI | gauge | A12 |
| Loadlock LPCVD gauge | AI | gauge | A13 |
| Loadlock convection gauge | AI | gauge | A14 |

#### 17.2.2 输出信号 (Output Signals)

| 信号名称 | I/O类型 | 信号类型 | 备注 |
|----------|---------|----------|------|
| TV close valve | DO | FV | - |
| Loadlock bleed valve | DO | FV | - |
| Loadlock backfill valve | DO | FV | - |

### 17.3 工艺腔室系统 (Process Chamber System)

#### 17.3.1 输入信号 (Input Signals)

| 信号名称 | I/O类型 | 信号类型 | 备注 |
|----------|---------|----------|------|
| Plasma sensors (1-4) | DI | DI | - |
| Chamber thermo state | DI | switch | - |
| Slit door sensors | DI | optic NPM | - |
| Lift pin sensors | DI | optic NPM | - |
| ISO valve status sensors | DI | optic NPM | - |
| Throttle valve sensors | DI | optic NPM | - |
| Chamber pressure | AI | gauge | - |

#### 17.3.2 输出信号 (Output Signals)

| 信号名称 | I/O类型 | 信号类型 | 备注 |
|----------|---------|----------|------|
| Gas valves (1-4) | DO | FV | - |
| Slit door control | DO | FV | - |
| Lift pin control | DO | FV | - |
| Throttle valve control | DO | DO | - |
| Temperature controller | RS485 | RS485 | - |
| RF generator | TCP/IP | TCP/IP | - |

### 17.4 信号说明 (Signal Description)

1. 数字输入信号 (DI)
   - 光电传感器信号
   - 开关量信号
   - 限位开关信号

2. 数字输出信号 (DO)
   - 电磁阀控制
   - 电机控制
   - 执行器控制

3. 模拟信号 (AI/AO)
   - 压力表读数
   - 温度读数
   - 流量控制

4. 通信信号
   - RS485通信
   - TCP/IP通信
   - 控制器接口

5. 特殊说明
   - 光电传感器通常用于位置检测
   - 开关量信号用于状态检测
   - 模拟信号用于过程控制 