{"MoveMotor": {"CMDIndex": 0, "Prompt": "移动电机", "ConfigPara": [{"Robot Rotation Speed": 40000}, {"Robot Extension Speed": 8333}, {"Robot Up Down Speed": 10000}, {"Robot Move Slowly": 0}, {"T-Axis Move Speed Slowly": 25}, {"R-Axis Move Speed Slowly": 25}, {"Z-Axis Move Speed Slowly": 25}, {"Robot Rotate Max Time": 10000}, {"Robot Extend Max Time": 10000}, {"Robot Up Down Max Time": 10000}, {"Deviation Step for T-Axis home": 10}, {"Robot Home T-Axis Max Time": 20000}, {"Robot Home R-Axis Max Time": 20000}, {"Robot Home Z-Axis Max Time": 20000}, {"T-Axis Deviation for R-Axis Zero": 10}], "RunPara": [{"MoveAxis": 1}, {"AbsPosition_H": 0}, {"AbsPosition_L": 0}, {"Start slope": 25000}, {"Stop slope": 25000}, {"RunCurrent": 1000}], "TimeOut": 3000}, "AlarmReset": {"CMDIndex": 1, "Prompt": "报警清除", "ConfigPara": [], "RunPara": [{"MoveAxis": 1}], "TimeOut": 1000}, "MotorStop": {"CMDIndex": 2, "Prompt": "停止电机", "ConfigPara": [], "RunPara": [{"MoveAxis": 1}], "TimeOut": 1000}, "Move_T_Axis": {"CMDIndex": 3, "Prompt": "移动 TAxis(旋转轴) 电机", "ConfigPara": [{"Robot Rotation Speed": 40000}, {"Robot Extension Speed": 8333}, {"Robot Up Down Speed": 10000}, {"Robot Move Slowly": 1}, {"T-Axis Move Speed Slowly": 25}, {"R-Axis Move Speed Slowly": 25}, {"Z-Axis Move Speed Slowly": 25}, {"Robot Rotate Max Time": 10000}, {"Robot Extend Max Time": 10000}, {"Robot Up Down Max Time": 10000}, {"Deviation Step for T-Axis home": 10}, {"Robot Home T-Axis Max Time": 20000}, {"Robot Home R-Axis Max Time": 20000}, {"Robot Home Z-Axis Max Time": 20000}, {"T-Axis Deviation for R-Axis Zero": 10}], "RunPara": [{"T-Axis Move Position H": 0}, {"T-Axis Move Position L": 0}, {"Start slope": 25000}, {"Stop slope": 25000}, {"Run Current": 1000}], "TimeOut": 10000}, "Move_R_Axis": {"CMDIndex": 4, "Prompt": "移动 RAxis(伸缩轴) 电机", "ConfigPara": [{"Robot Rotation Speed": 40000}, {"Robot Extension Speed": 8333}, {"Robot Up Down Speed": 10000}, {"Robot Move Slowly": 1}, {"T-Axis Move Speed Slowly": 25}, {"R-Axis Move Speed Slowly": 25}, {"Z-Axis Move Speed Slowly": 25}, {"Robot Rotate Max Time": 10000}, {"Robot Extend Max Time": 10000}, {"Robot Up Down Max Time": 10000}, {"Deviation Step for T-Axis home": 10}, {"Robot Home T-Axis Max Time": 20000}, {"Robot Home R-Axis Max Time": 20000}, {"Robot Home Z-Axis Max Time": 20000}, {"T-Axis Deviation for R-Axis Zero": 10}], "RunPara": [{"R-Axis Move Position H": 0}, {"R-Axis Move Position L": 0}, {"Start slope": 6000}, {"Stop slope": 6000}, {"Run Current": 600}], "TimeOut": 10000}, "Move_Z_Axis": {"CMDIndex": 5, "Prompt": "移动 ZAxis(升降轴) 电机", "ConfigPara": [{"Robot Rotation Speed": 40000}, {"Robot Extension Speed": 8333}, {"Robot Up Down Speed": 10000}, {"Robot Move Slowly": 1}, {"T-Axis Move Speed Slowly": 25}, {"R-Axis Move Speed Slowly": 25}, {"Z-Axis Move Speed Slowly": 25}, {"Robot Rotate Max Time": 10000}, {"Robot Extend Max Time": 10000}, {"Robot Up Down Max Time": 10000}, {"Deviation Step for T-Axis home": 10}, {"Robot Home T-Axis Max Time": 20000}, {"Robot Home R-Axis Max Time": 20000}, {"Robot Home Z-Axis Max Time": 20000}, {"T-Axis Deviation for R-Axis Zero": 10}], "RunPara": [{"Z-Axis Move Position H": 0}, {"Z-Axis Move Position L": 0}, {"Start slope": 8000}, {"Stop slope": 8000}, {"Run Current": 500}], "TimeOut": 10000}, "PinSearch": {"CMDIndex": 6, "Prompt": "Pin <PERSON>", "ConfigPara": [{"Robot Rotation Speed": 40000}, {"Robot Extension Speed": 7000}, {"Robot Up Down Speed": 10000}, {"Robot Move Slowly": 0}, {"T-Axis Move Speed Slowly": 25}, {"R-Axis Move Speed Slowly": 25}, {"Z-Axis Move Speed Slowly": 25}, {"Robot Rotate Max Time": 10000}, {"Robot Extend Max Time": 10000}, {"Robot Up Down Max Time": 10000}, {"Deviation Step for T-Axis home": 10}, {"Robot Home T-Axis Max Time": 20000}, {"Robot Home R-Axis Max Time": 20000}, {"Robot Home Z-Axis Max Time": 20000}, {"T-Axis Deviation for R-Axis Zero": 10}], "RunPara": [{"Prepressing Position H": 0}, {"Prepressing Position L": 0}, {"Pin Search Speed": 100}, {"Start slope": 8000}, {"Stop slope": 8000}, {"Run Current": 500}], "TimeOut": 30000}}