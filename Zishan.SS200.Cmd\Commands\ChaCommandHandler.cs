using System;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Services;
using log4net;
using Zishan.SS200.Cmd.Enums.McuCmdIndex;

namespace Zishan.SS200.Cmd.Commands
{
    /// <summary>
    /// Cha设备命令处理器
    /// </summary>
    public class ChaCommandHandler : BaseDeviceCommandHandler<McuDevice, EnuChaCmdIndex>
    {
        public ChaCommandHandler(IModbusClientService modbusClientService, ILog logger)
            : base(modbusClientService, logger)
        {
        }

        public override bool ValidateParameters(EnuChaCmdIndex command, ushort[] parameters)
        {
            // 根据具体命令添加参数验证逻辑
            switch (command)
            {
                case EnuChaCmdIndex.OD_SD:
                case EnuChaCmdIndex.CD_SD:
                case EnuChaCmdIndex.PU:
                case EnuChaCmdIndex.PD:
                case EnuChaCmdIndex.OV_TV:
                case EnuChaCmdIndex.CV_TV:
                case EnuChaCmdIndex.OC:
                case EnuChaCmdIndex.CC:
                case EnuChaCmdIndex.OV_CM:
                case EnuChaCmdIndex.CV_CM:
                case EnuChaCmdIndex.OV_C1:
                case EnuChaCmdIndex.CV_C1:
                    // 这些命令不需要参数
                    return parameters == null || parameters.Length == 0;

                default:
                    return false;
            }
        }
    }
}