using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.Services.Interfaces;

namespace Zishan.SS200.Cmd.Commands.CommandSpec
{
    /// <summary>
    /// 命令规格接口，用于描述单个命令的详细信息和执行前后的逻辑
    /// </summary>
    public interface ICommandSpec
    {
        /// <summary>
        /// 命令代码，例如 "AR1", "S1 SD" 等
        /// </summary>
        string CommandCode { get; }

        /// <summary>
        /// 命令名称
        /// </summary>
        string CommandName { get; }

        /// <summary>
        /// 命令描述（英文）
        /// </summary>
        string Description { get; }

        /// <summary>
        /// 命令描述（中文）
        /// </summary>
        string DescriptionCn { get; }

        /// <summary>
        /// 设备类型
        /// </summary>
        EnuMcuDeviceType DeviceType { get; }

        /// <summary>
        /// 命令索引
        /// </summary>
        Enum CommandIndex { get; }

        /// <summary>
        /// 超时时间（毫秒）
        /// </summary>
        int DefaultTimeout { get; }

        /// <summary>
        /// 参数验证
        /// </summary>
        /// <param name="parameters">参数列表</param>
        /// <returns>是否有效</returns>
        bool ValidateParameters(List<ushort> parameters);

        /// <summary>
        /// 执行前处理
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="parameters">参数列表</param>
        /// <returns>是否继续执行命令</returns>
        Task<bool> BeforeExecuteAsync(IS200McuCmdService cmdService, List<ushort> parameters);

        /// <summary>
        /// 执行后处理
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="result">命令执行结果</param>
        /// <param name="parameters">参数列表</param>
        /// <returns>处理后的结果</returns>
        Task<(string Response, ushort RunInfo, ushort ReturnInfo)> AfterExecuteAsync(
            IS200McuCmdService cmdService,
            (string Response, ushort RunInfo, ushort ReturnInfo) result,
            List<ushort> parameters);

        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="parameters">参数列表</param>
        /// <param name="timeout">超时时间</param>
        /// <returns>执行结果</returns>
        Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteAsync(
            IS200McuCmdService cmdService,
            List<ushort> parameters = null,
            int? timeout = null);
    }
}