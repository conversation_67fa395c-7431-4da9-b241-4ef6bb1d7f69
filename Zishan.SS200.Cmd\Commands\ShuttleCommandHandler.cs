using System;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.Services;
using log4net;
using Zishan.SS200.Cmd.Enums.McuCmdIndex;

namespace Zishan.SS200.Cmd.Commands
{
    /// <summary>
    /// Shuttle设备命令处理器
    /// </summary>
    public class ShuttleCommandHandler : BaseDeviceCommandHandler<McuDevice, EnuShuttleCmdIndex>
    {
        public ShuttleCommandHandler(IModbusClientService modbusClientService, ILog logger)
            : base(modbusClientService, logger)
        {
        }

        public override bool ValidateParameters(EnuShuttleCmdIndex command, ushort[] parameters)
        {
            // 根据具体命令添加参数验证逻辑
            switch (command)
            {
                case EnuShuttleCmdIndex.S1_SD:
                case EnuShuttleCmdIndex.S2_SU:
                case EnuShuttleCmdIndex.S3_SR1:
                case EnuShuttleCmdIndex.S4_SR2:
                case EnuShuttleCmdIndex.S5_CD_CD:
                case EnuShuttleCmdIndex.S6_OD_CD:
                case EnuShuttleCmdIndex.S7_TS1:
                case EnuShuttleCmdIndex.S8_TS2:
                case EnuShuttleCmdIndex.S9_SC:
                case EnuShuttleCmdIndex.S10_RC:
                case EnuShuttleCmdIndex.S11_OV_SV:
                case EnuShuttleCmdIndex.S12_CV_SV:
                case EnuShuttleCmdIndex.S13_OV_SB:
                case EnuShuttleCmdIndex.S14_CV_SB:
                case EnuShuttleCmdIndex.S15_OV_XV:
                case EnuShuttleCmdIndex.S16_CV_XV:
                case EnuShuttleCmdIndex.S17_OV_BL:
                case EnuShuttleCmdIndex.S18_CV_BL:
                case EnuShuttleCmdIndex.S19_OV_LB:
                case EnuShuttleCmdIndex.S20_CV_LB:
                    // 这些命令不需要参数
                    return parameters == null || parameters.Length == 0;

                default:
                    return false;
            }
        }
    }
}