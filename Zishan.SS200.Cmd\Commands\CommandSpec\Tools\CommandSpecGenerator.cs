using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using log4net;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Commands.CommandSpec.Tools
{
    /// <summary>
    /// 命令规格生成器工具，用于生成命令规格类文件
    /// </summary>
    public static class CommandSpecGenerator
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(CommandSpecGenerator));

        /// <summary>
        /// 命令信息类
        /// </summary>
        public class CommandInfo
        {
            /// <summary>
            /// 设备类型
            /// </summary>
            public EnuMcuDeviceType DeviceType { get; set; }

            /// <summary>
            /// 命令代码
            /// </summary>
            public string CommandCode { get; set; }

            /// <summary>
            /// 命令名称
            /// </summary>
            public string CommandName { get; set; }

            /// <summary>
            /// 命令英文描述
            /// </summary>
            public string Description { get; set; }

            /// <summary>
            /// 命令中文描述
            /// </summary>
            public string DescriptionCn { get; set; }

            /// <summary>
            /// 枚举类型名称
            /// </summary>
            public string EnumType { get; set; }

            /// <summary>
            /// 枚举值
            /// </summary>
            public string EnumValue { get; set; }

            /// <summary>
            /// 默认超时时间（毫秒）
            /// </summary>
            public int DefaultTimeout { get; set; } = 30000;
        }

        /// <summary>
        /// 生成命令规格类文件
        /// </summary>
        /// <param name="commandInfo">命令信息</param>
        /// <param name="outputDirectory">输出目录</param>
        /// <returns>生成的文件路径</returns>
        public static string GenerateCommandSpecClass(CommandInfo commandInfo, string outputDirectory)
        {
            try
            {
                if (commandInfo == null)
                {
                    throw new ArgumentNullException(nameof(commandInfo));
                }

                if (string.IsNullOrWhiteSpace(outputDirectory))
                {
                    throw new ArgumentException("输出目录不能为空", nameof(outputDirectory));
                }

                // 准备目录路径
                string deviceTypeString = commandInfo.DeviceType.ToString();
                string deviceDir = Path.Combine(outputDirectory, deviceTypeString);
                string categoryDir = !string.IsNullOrWhiteSpace(commandInfo.DescriptionCn)
                    ? Path.Combine(deviceDir, commandInfo.DescriptionCn)
                    : deviceDir;

                // 确保目录存在
                Directory.CreateDirectory(categoryDir);

                // 生成文件名
                string safeCmdCode = commandInfo.CommandCode.Replace(" ", "_");
                string fileName = $"{safeCmdCode}Command.cs";
                string filePath = Path.Combine(categoryDir, fileName);

                // 生成类文件内容
                string classContent = GenerateClassContent(commandInfo);

                // 写入文件
                File.WriteAllText(filePath, classContent, Encoding.UTF8);

                _logger.Info($"已生成命令规格类文件: {filePath}");
                return filePath;
            }
            catch (Exception ex)
            {
                _logger.Error($"生成命令规格类文件失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 批量生成命令规格类文件
        /// </summary>
        /// <param name="commandInfos">命令信息列表</param>
        /// <param name="outputDirectory">输出目录</param>
        /// <returns>生成的文件路径列表</returns>
        public static List<string> GenerateCommandSpecClasses(IEnumerable<CommandInfo> commandInfos, string outputDirectory)
        {
            var filePaths = new List<string>();

            foreach (var commandInfo in commandInfos)
            {
                try
                {
                    string filePath = GenerateCommandSpecClass(commandInfo, outputDirectory);
                    filePaths.Add(filePath);
                }
                catch (Exception ex)
                {
                    _logger.Error($"生成命令规格类文件 {commandInfo.CommandCode} 失败: {ex.Message}", ex);
                }
            }

            return filePaths;
        }

        /// <summary>
        /// 生成类文件内容
        /// </summary>
        /// <param name="commandInfo">命令信息</param>
        /// <returns>类文件内容</returns>
        private static string GenerateClassContent(CommandInfo commandInfo)
        {
            string className = commandInfo.CommandCode.Replace(" ", "_") + "Command";
            string deviceTypeString = commandInfo.DeviceType.ToString();

            return $@"using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using log4net;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Services.Interfaces;

namespace Zishan.SS200.Cmd.Commands.CommandSpec.{deviceTypeString}{(!string.IsNullOrWhiteSpace(commandInfo.DescriptionCn) ? "." + commandInfo.DescriptionCn : "")}
{{
    /// <summary>
    /// {commandInfo.CommandCode} - {commandInfo.Description}
    /// </summary>
    public class {className} : BaseCommandSpec<{commandInfo.EnumType}>
    {{
        /// <summary>
        /// 构造函数
        /// </summary>
        public {className}() : base(
            EnuMcuDeviceType.{deviceTypeString},
            {commandInfo.EnumType}.{commandInfo.EnumValue},
            ""{commandInfo.CommandCode}"",
            ""{commandInfo.CommandName}"",
            ""{commandInfo.Description}"",
            {commandInfo.DefaultTimeout})
        {{
        }}

        /// <summary>
        /// 参数验证
        /// </summary>
        /// <param name=""parameters"">参数列表</param>
        /// <returns>是否有效</returns>
        public override bool ValidateParameters(List<ushort> parameters)
        {{
            // TODO: 添加特定的参数验证逻辑
            return base.ValidateParameters(parameters);
        }}

        /// <summary>
        /// 执行前处理
        /// </summary>
        /// <param name=""cmdService"">命令服务</param>
        /// <param name=""parameters"">参数列表</param>
        /// <returns>是否继续执行命令</returns>
        public override async Task<bool> BeforeExecuteAsync(IS200McuCmdService cmdService, List<ushort> parameters)
        {{
            // TODO: 添加执行前的特定逻辑，例如检查设备状态、准备环境等

            // 可以调用基类的实现
            return await base.BeforeExecuteAsync(cmdService, parameters);
        }}

        /// <summary>
        /// 执行后处理
        /// </summary>
        /// <param name=""cmdService"">命令服务</param>
        /// <param name=""result"">命令执行结果</param>
        /// <param name=""parameters"">参数列表</param>
        /// <returns>处理后的结果</returns>
        public override async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> AfterExecuteAsync(
            IS200McuCmdService cmdService,
            (string Response, ushort RunInfo, ushort ReturnInfo) result,
            List<ushort> parameters)
        {{
            // TODO: 添加执行后的特定逻辑，例如处理结果、更新状态等

            // 可以调用基类的实现
            return await base.AfterExecuteAsync(cmdService, result, parameters);
        }}
    }}
}}";
        }
    }
}