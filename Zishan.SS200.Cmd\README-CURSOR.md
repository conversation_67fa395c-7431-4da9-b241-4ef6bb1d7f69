# Cursor 配置说明

本项目使用 Cursor 编辑器进行开发，以下是相关配置文件的说明。

## 配置文件概览

- `.cursorignore`: 指定 Cursor 应忽略的文件和目录
- `.cursorsettings`: Cursor 编辑器的基本设置
- `.cursorrc`: Cursor AI 助手的行为配置
- `.editorconfig`: 代码风格和格式化规则
- `.cursor/`: 存放 Cursor 相关的规则和模板

## .cursorignore

该文件定义了 Cursor 应忽略的文件和目录，类似于 `.gitignore`。主要忽略以下内容：

- 编译输出目录（bin/, obj/）
- IDE 配置文件（.vs/, .idea/）
- 包管理器目录（packages/, node_modules/）
- 临时文件和备份文件
- 大型媒体文件和图像
- 文档目录中的大型文件，但保留文本文件

## .cursorsettings

该文件配置 Cursor 编辑器的基本行为，包括：

- AI 模型设置：使用 Claude 3.7 Sonnet 模型
- 编辑器设置：字体、缩进、格式化规则等
- 终端设置：字体、滚动行为等
- 文件设置：自动保存、文件关联等
- 搜索设置：搜索排除规则
- 工作区设置：主题、图标等
- 扩展推荐：推荐安装的扩展

## .cursorrc

该文件配置 Cursor AI 助手的行为，包括：

- 模板定义：预定义的代码模板（ViewModel、Service、Model、View）
- 命令定义：快捷命令（构建、运行、测试、清理）
- 代码片段：常用代码片段（属性、命令、构造函数等）
- AI 规则：应用的规则文件
- AI 角色设置：AI 助手的行为指导

## .editorconfig

该文件定义了代码风格和格式化规则，确保团队成员使用一致的代码风格，包括：

- 缩进样式和大小
- 换行符类型
- C# 代码风格规则
- 命名约定
- 不同文件类型的特定设置

## .cursor/ 目录

该目录包含 Cursor 的详细规则和模板：

### 规则文件

- `rules.json`: 定义文件类型的格式化和检查规则
- `rules/`: 包含各种规则文件
  - `c-sharpcn.mdc`: C# 编码规范
  - `wpf-mvvm.mdc`: WPF 和 MVVM 开发规范
  - `modbus-communication.mdc`: Modbus 通信规范

### 通用规则

- `common/general.mdc`: 项目通用开发规范
- `common/document.mdc`: 文档编写规范
- `common/git.mdc`: Git 提交规范
- `common/gitflow.mdc`: Git 工作流规范

### 模板文件

- `templates/viewmodel.cs`: ViewModel 类模板
- `templates/service.cs`: 服务类模板
- `templates/model.cs`: 模型类模板
- `templates/view.xaml`: XAML 视图模板

## 使用方法

### 使用模板

可以通过 Cursor AI 助手创建新文件时使用预定义模板：

```
创建一个新的 ViewModel 类
```

### 使用代码片段

在 C# 文件中输入以下前缀触发代码片段：

- `prop`: 创建带通知的属性
- `cmd`: 创建命令
- `acmd`: 创建异步命令
- `ctor`: 创建构造函数

### 使用命令

可以通过 Cursor AI 助手执行预定义命令：

```
执行构建命令
```

## 自定义配置

如需修改配置，可以编辑相应的配置文件：

- 添加新的模板：在 `.cursor/templates/` 目录添加模板文件，并在 `.cursorrc` 中注册
- 添加新的规则：在 `.cursor/rules/` 目录添加规则文件，并在 `.cursorrc` 中启用
- 添加新的代码片段：在 `.cursorrc` 的 `snippets` 部分添加
