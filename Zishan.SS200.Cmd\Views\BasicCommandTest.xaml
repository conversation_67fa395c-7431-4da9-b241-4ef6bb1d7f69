<UserControl
    x:Class="Zishan.SS200.Cmd.Views.BasicCommandTest"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:behavior="clr-namespace:Zishan.SS200.Cmd.Behaviors"
    xmlns:common="clr-namespace:Zishan.SS200.Cmd.Common"
    xmlns:converters="clr-namespace:Zishan.SS200.Cmd.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dvm="clr-namespace:Zishan.SS200.Cmd.ViewModels.DesignViewModels"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:local="clr-namespace:Zishan.SS200.Cmd.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    xmlns:userControls="clr-namespace:Zishan.SS200.Cmd.UserControls"
    xmlns:viewModels="clr-namespace:Zishan.SS200.Cmd.ViewModels"
    xmlns:visualBasic="clr-namespace:Microsoft.VisualBasic;assembly=Microsoft.VisualBasic.Core"
    xmlns:wu="https://github.com/Monika1313/Wu"
    d:Background="LightBlue"
    d:DataContext="{x:Static dvm:BasicCommandTestDesignViewModel.Instance}"
    d:DesignHeight="1290"
    d:DesignWidth="2146"
    prism:ViewModelLocator.AutoWireViewModel="True"
    mc:Ignorable="d">
    <UserControl.Resources>
        <converters:BooleanToColorConverter x:Key="BooleanToColorConverter" />
        <converters:BooleanToStringConverter x:Key="BooleanToStringConverter" />
        <converters:LoopCountDisplayConverter x:Key="LoopCountDisplayConverter" />
    </UserControl.Resources>
    <Grid>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="5" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <!--  顶部区域：标题和全局控制按钮  -->
            <Grid Grid.Row="0" Margin="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <TextBlock
                    Grid.Column="0"
                    VerticalAlignment="Center"
                    FontSize="20"
                    FontWeight="Bold"
                    Text="TRZ各轴基本测试" />

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button
                        Margin="5"
                        d:Visibility="Visible"
                        Command="{Binding ExecuteAllTestsCommand}"
                        Content="执行全部测试"
                        Style="{StaticResource ButtonPrimary}"
                        Visibility="{Binding Source={x:Static common:Golbal.IsDevDebug}, Converter={StaticResource BooleanToVisibilityConverter}}" />
                    <Button
                        Margin="5"
                        Command="{Binding StopTestCommand}"
                        Content="停止测试"
                        Style="{StaticResource ButtonWarning}" />
                    <CheckBox
                        Margin="10,0"
                        VerticalAlignment="Center"
                        Content="已连接Robot"
                        IsChecked="{Binding IsRobotConnected, Mode=OneWay}"
                        IsEnabled="False" />
                    <CheckBox
                        Margin="10,0"
                        VerticalAlignment="Center"
                        Content="安全模式"
                        IsChecked="{Binding IsSafetyModeEnabled, Mode=TwoWay}"
                        ToolTip="启用后，系统将强制执行T轴和R轴的操作顺序安全检查" />
                </StackPanel>
            </Grid>

            <!--  轴状态指示区域  -->
            <Grid Grid.Row="1" Margin="10,0,10,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <TextBlock
                    Grid.Column="0"
                    Margin="0,0,10,0"
                    VerticalAlignment="Center"
                    FontWeight="Bold"
                    Text="轴状态:" />

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Border
                        Margin="0,0,10,0"
                        Padding="5,3"
                        Background="{Binding IsRAxisAtZero, Converter={StaticResource BooleanToColorConverter}, ConverterParameter='Green,Red'}"
                        BorderBrush="Gray"
                        BorderThickness="1"
                        CornerRadius="3">
                        <TextBlock
                            FontWeight="Bold"
                            Foreground="White"
                            Text="{Binding IsRAxisAtZero, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='R轴已归零,R轴未归零'}" />
                    </Border>

                    <Border
                        Margin="0,0,10,0"
                        Padding="5,3"
                        Background="LightBlue"
                        BorderBrush="Gray"
                        BorderThickness="1"
                        CornerRadius="3">
                        <TextBlock FontWeight="Bold" Text="{Binding CurrentTAxisLocation, StringFormat='T轴当前位置: {0}'}" />
                    </Border>

                    <Border
                        Margin="0,0,10,0"
                        Padding="5,3"
                        Background="LightBlue"
                        BorderBrush="Gray"
                        BorderThickness="1"
                        CornerRadius="3">
                        <TextBlock FontWeight="Bold" Text="{Binding CurrentActiveEndType, StringFormat='当前操作机械臂: {0}'}" />
                    </Border>
                </StackPanel>
            </Grid>

            <!--  中间区域：TabControl分别显示Nose端和Smooth端  -->
            <TabControl Grid.Row="2" Margin="10,5">
                <TabItem Header="Nose端">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="*" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <!--  T轴测试  -->
                        <GroupBox
                            Grid.Row="0"
                            Margin="5"
                            Header="T轴测试">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveTAxisToLocationCommand}"
                                        CommandParameter="Nose,Cassette"
                                        Content="移动到Cassette"
                                        Style="{StaticResource ButtonInfo}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveTAxisToLocationCommand}"
                                        CommandParameter="Nose,ChamberA"
                                        Content="移动到ChamberA"
                                        Style="{StaticResource ButtonInfo}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveTAxisToLocationCommand}"
                                        CommandParameter="Nose,ChamberB"
                                        Content="移动到ChamberB"
                                        Style="{StaticResource ButtonInfo}" />
                                </StackPanel>

                                <StackPanel Grid.Column="1">
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveTAxisToLocationCommand}"
                                        CommandParameter="Nose,CoolingChamber"
                                        Content="移动到冷却腔区域"
                                        Style="{StaticResource ButtonInfo}" />
                                    <TextBox
                                        Margin="5"
                                        hc:InfoElement.Title="T轴位置:"
                                        hc:InfoElement.TitlePlacement="Left"
                                        hc:TitleElement.HorizontalAlignment="Left"
                                        wu:TextBoxExtensions.SelectAllWhenGotFocus="True"
                                        Style="{StaticResource TextBoxExtend}"
                                        Text="{Binding TAxisPosition, UpdateSourceTrigger=PropertyChanged}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveTAxisToPositionCommand}"
                                        CommandParameter="Nose"
                                        Content="移动到指定位置"
                                        Style="{StaticResource ButtonInfo}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding ZeroTAxisCommand}"
                                        CommandParameter="Nose"
                                        Content="T轴归零"
                                        Style="{StaticResource ButtonPrimary}" />
                                </StackPanel>
                            </Grid>
                        </GroupBox>

                        <!--  R轴测试  -->
                        <GroupBox
                            Grid.Row="1"
                            Margin="5"
                            Header="R轴测试">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveRAxisToLocationCommand}"
                                        CommandParameter="Nose,Cassette"
                                        Content="移动到Cassette"
                                        Style="{StaticResource ButtonInfo}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveRAxisToLocationCommand}"
                                        CommandParameter="Nose,ChamberA"
                                        Content="移动到ChamberA"
                                        Style="{StaticResource ButtonInfo}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveRAxisToLocationCommand}"
                                        CommandParameter="Nose,ChamberB"
                                        Content="移动到ChamberB"
                                        Style="{StaticResource ButtonInfo}" />
                                </StackPanel>

                                <StackPanel Grid.Column="1">
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveRAxisToLocationCommand}"
                                        CommandParameter="Nose,CoolingChamber"
                                        Content="移动到冷却腔区域"
                                        Style="{StaticResource ButtonInfo}" />
                                    <TextBox
                                        Margin="5"
                                        hc:InfoElement.Title="R轴位置:"
                                        hc:InfoElement.TitlePlacement="Left"
                                        hc:TitleElement.HorizontalAlignment="Left"
                                        wu:TextBoxExtensions.SelectAllWhenGotFocus="True"
                                        Style="{StaticResource TextBoxExtend}"
                                        Text="{Binding RAxisPosition, UpdateSourceTrigger=PropertyChanged}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveRAxisToPositionCommand}"
                                        CommandParameter="Nose"
                                        Content="移动到指定位置"
                                        Style="{StaticResource ButtonInfo}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding ZeroRAxisCommand}"
                                        CommandParameter="Nose"
                                        Content="R轴归零"
                                        Style="{StaticResource ButtonPrimary}" />
                                </StackPanel>
                            </Grid>
                        </GroupBox>

                        <!--  Z轴测试  -->
                        <GroupBox
                            Grid.Row="2"
                            Margin="5"
                            Header="Z轴测试">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <ComboBox
                                            Grid.Column="0"
                                            Margin="5"
                                            DisplayMemberPath="DisplayName"
                                            ItemsSource="{Binding LocationTypes}"
                                            SelectedItem="{Binding SelectedLocationType}" />
                                        <TextBox
                                            Grid.Column="1"
                                            Width="80"
                                            Margin="5"
                                            hc:InfoElement.Title="Slot:"
                                            hc:InfoElement.TitlePlacement="Left"
                                            hc:TitleElement.HorizontalAlignment="Left"
                                            wu:TextBoxExtensions.SelectAllWhenGotFocus="True"
                                            Style="{StaticResource TextBoxExtend}"
                                            Text="{Binding SlotNumber, UpdateSourceTrigger=PropertyChanged}" />
                                    </Grid>
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveZAxisToGetPositionCommand}"
                                        CommandParameter="Nose"
                                        Content="移动到Get位置【工艺腔一致】"
                                        Style="{StaticResource ButtonInfo}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveZAxisToPutPositionCommand}"
                                        CommandParameter="Nose"
                                        Content="移动到Put位置【工艺腔一致】"
                                        Style="{StaticResource ButtonInfo}" />
                                </StackPanel>

                                <StackPanel Grid.Column="1">
                                    <TextBox
                                        Margin="5"
                                        hc:InfoElement.Title="Z轴位置:"
                                        hc:InfoElement.TitlePlacement="Left"
                                        hc:TitleElement.HorizontalAlignment="Left"
                                        wu:TextBoxExtensions.SelectAllWhenGotFocus="True"
                                        Style="{StaticResource TextBoxExtend}"
                                        Text="{Binding ZAxisPosition, UpdateSourceTrigger=PropertyChanged}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveZAxisToPositionCommand}"
                                        CommandParameter="Nose"
                                        Content="移动到指定位置"
                                        Style="{StaticResource ButtonInfo}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding ZeroZAxisCommand}"
                                        CommandParameter="Nose"
                                        Content="Z轴归零"
                                        Style="{StaticResource ButtonPrimary}" />
                                </StackPanel>
                            </Grid>
                        </GroupBox>
                    </Grid>
                </TabItem>

                <TabItem Header="Smooth端">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="*" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <!--  T轴测试  -->
                        <GroupBox
                            Grid.Row="0"
                            Margin="5"
                            Header="T轴测试">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveTAxisToLocationCommand}"
                                        CommandParameter="Smooth,Cassette"
                                        Content="移动到Cassette"
                                        Style="{StaticResource ButtonInfo}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveTAxisToLocationCommand}"
                                        CommandParameter="Smooth,ChamberA"
                                        Content="移动到ChamberA"
                                        Style="{StaticResource ButtonInfo}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveTAxisToLocationCommand}"
                                        CommandParameter="Smooth,ChamberB"
                                        Content="移动到ChamberB"
                                        Style="{StaticResource ButtonInfo}" />
                                </StackPanel>

                                <StackPanel Grid.Column="1">
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveTAxisToLocationCommand}"
                                        CommandParameter="Smooth,CoolingChamber"
                                        Content="移动到冷却腔区域"
                                        Style="{StaticResource ButtonInfo}" />
                                    <TextBox
                                        Margin="5"
                                        hc:InfoElement.Title="T轴位置:"
                                        hc:InfoElement.TitlePlacement="Left"
                                        hc:TitleElement.HorizontalAlignment="Left"
                                        wu:TextBoxExtensions.SelectAllWhenGotFocus="True"
                                        Style="{StaticResource TextBoxExtend}"
                                        Text="{Binding TAxisPosition, UpdateSourceTrigger=PropertyChanged}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveTAxisToPositionCommand}"
                                        CommandParameter="Smooth"
                                        Content="移动到指定位置"
                                        Style="{StaticResource ButtonInfo}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding ZeroTAxisCommand}"
                                        CommandParameter="Smooth"
                                        Content="T轴归零"
                                        Style="{StaticResource ButtonPrimary}" />
                                </StackPanel>
                            </Grid>
                        </GroupBox>

                        <!--  R轴测试  -->
                        <GroupBox
                            Grid.Row="1"
                            Margin="5"
                            Header="R轴测试">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveRAxisToLocationCommand}"
                                        CommandParameter="Smooth,Cassette"
                                        Content="移动到Cassette"
                                        Style="{StaticResource ButtonInfo}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveRAxisToLocationCommand}"
                                        CommandParameter="Smooth,ChamberA"
                                        Content="移动到ChamberA"
                                        Style="{StaticResource ButtonInfo}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveRAxisToLocationCommand}"
                                        CommandParameter="Smooth,ChamberB"
                                        Content="移动到ChamberB"
                                        Style="{StaticResource ButtonInfo}" />
                                </StackPanel>

                                <StackPanel Grid.Column="1">
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveRAxisToLocationCommand}"
                                        CommandParameter="Smooth,CoolingChamber"
                                        Content="移动到冷却腔区域"
                                        Style="{StaticResource ButtonInfo}" />
                                    <TextBox
                                        Margin="5"
                                        hc:InfoElement.Title="R轴位置:"
                                        hc:InfoElement.TitlePlacement="Left"
                                        hc:TitleElement.HorizontalAlignment="Left"
                                        wu:TextBoxExtensions.SelectAllWhenGotFocus="True"
                                        Style="{StaticResource TextBoxExtend}"
                                        Text="{Binding RAxisPosition, UpdateSourceTrigger=PropertyChanged}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveRAxisToPositionCommand}"
                                        CommandParameter="Smooth"
                                        Content="移动到指定位置"
                                        Style="{StaticResource ButtonInfo}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding ZeroRAxisCommand}"
                                        CommandParameter="Smooth"
                                        Content="R轴归零"
                                        Style="{StaticResource ButtonPrimary}" />
                                </StackPanel>
                            </Grid>
                        </GroupBox>

                        <!--  Z轴测试  -->
                        <GroupBox
                            Grid.Row="2"
                            Margin="5"
                            Header="Z轴测试">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <ComboBox
                                            Grid.Column="0"
                                            Margin="5"
                                            DisplayMemberPath="DisplayName"
                                            ItemsSource="{Binding LocationTypes}"
                                            SelectedItem="{Binding SelectedLocationType}" />

                                        <TextBox
                                            Grid.Column="1"
                                            Width="80"
                                            Margin="5"
                                            hc:InfoElement.Title="Slot:"
                                            hc:InfoElement.TitlePlacement="Left"
                                            hc:TitleElement.HorizontalAlignment="Left"
                                            wu:TextBoxExtensions.SelectAllWhenGotFocus="True"
                                            Style="{StaticResource TextBoxExtend}"
                                            Text="{Binding SlotNumber, UpdateSourceTrigger=PropertyChanged}" />
                                    </Grid>
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveZAxisToGetPositionCommand}"
                                        CommandParameter="Smooth"
                                        Content="移动到Get位置【工艺腔一致】"
                                        Style="{StaticResource ButtonInfo}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveZAxisToPutPositionCommand}"
                                        CommandParameter="Smooth"
                                        Content="移动到Put位置【工艺腔一致】"
                                        Style="{StaticResource ButtonInfo}" />
                                </StackPanel>

                                <StackPanel Grid.Column="1">

                                    <TextBox
                                        Margin="5"
                                        hc:InfoElement.Title="Z轴位置:"
                                        hc:InfoElement.TitlePlacement="Left"
                                        hc:TitleElement.HorizontalAlignment="Left"
                                        wu:TextBoxExtensions.SelectAllWhenGotFocus="True"
                                        Style="{StaticResource TextBoxExtend}"
                                        Text="{Binding ZAxisPosition, UpdateSourceTrigger=PropertyChanged}" />

                                    <Button
                                        Margin="5"
                                        Command="{Binding MoveZAxisToPositionCommand}"
                                        CommandParameter="Smooth"
                                        Content="移动到指定位置"
                                        Style="{StaticResource ButtonInfo}" />
                                    <Button
                                        Margin="5"
                                        Command="{Binding ZeroZAxisCommand}"
                                        CommandParameter="Smooth"
                                        Content="Z轴归零"
                                        Style="{StaticResource ButtonPrimary}" />
                                </StackPanel>
                            </Grid>
                        </GroupBox>
                    </Grid>
                </TabItem>
            </TabControl>

            <!--  分隔线  -->
            <GridSplitter
                Grid.Row="3"
                Height="5"
                Margin="10,0"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Center" />

            <!--  手动搬运命令布局 - 单行显示  -->
            <GroupBox
                Grid.Row="4"
                Grid.Column="0"
                Grid.ColumnSpan="3"
                Margin="10,5"
                Header="手动搬运命令">
                <WrapPanel
                    Margin="5"
                    Orientation="Horizontal"
                    ToolTip="安全提醒：在手动模式非运行状态下才可以执行手动命令">

                    <!--  循环执行次数设置  -->
                    <TextBox
                        MinWidth="120"
                        MaxWidth="160"
                        Margin="5"
                        VerticalAlignment="Center"
                        hc:InfoElement.Placeholder="请输入循环次数，-1=无限循环"
                        hc:InfoElement.Title="公共循环次数:"
                        hc:InfoElement.TitlePlacement="Left"
                        hc:TitleElement.HorizontalAlignment="Left"
                        hc:TitleElement.TitleWidth="85"
                        wu:TextBoxExtensions.SelectAllWhenGotFocus="True"
                        Style="{StaticResource TextBoxExtend}"
                        Text="{Binding LoopCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                        ToolTip="设置循环执行次数：-1代表无限循环，默认1执行一次" />

                    <!--  剩余循环次数显示  -->
                    <Border
                        MinWidth="60"
                        Margin="5,0"
                        Padding="8,3"
                        Background="LightBlue"
                        BorderBrush="Gray"
                        BorderThickness="1"
                        CornerRadius="3"
                        ToolTip="当前剩余的循环执行次数">
                        <TextBlock
                            VerticalAlignment="Center"
                            d:Text="剩余: 1次"
                            FontWeight="Bold"
                            Text="{Binding RemainingLoopCount, Converter={StaticResource LoopCountDisplayConverter}}" />
                    </Border>

                    <!--  停止循环按钮  -->
                    <Button
                        Margin="5,0,5,0"
                        Command="{Binding StopLoopCommand}"
                        Content="公共停止循环"
                        Style="{StaticResource ButtonWarning}"
                        ToolTip="停止当前正在执行的循环操作" />

                    <!--  紧急停止:直接停止电机按钮  -->
                    <Button
                        Margin="5,0,20,0"
                        d:Visibility="Visible"
                        Command="{Binding EmergencyStopCommand}"
                        Content="🚨 紧急停止"
                        Style="{StaticResource ButtonDanger}"
                        ToolTip="🚨 紧急停止按钮直接发出3轴停止电机命令"
                        Visibility="{Binding Source={x:Static common:Golbal.IsDevDebug}, Converter={StaticResource BooleanToVisibilityConverter}}" />

                    <!--  Pin Search测试按钮  -->
                    <Button
                        Margin="5,0"
                        d:Visibility="Visible"
                        Command="{Binding PinSearchTestCommand}"
                        Content="Pin Search测试"
                        Style="{StaticResource ButtonPrimary}"
                        ToolTip="执行Pin Search测试，循环次数由左侧文本框控制" />

                    <!--  分隔符  -->
                    <!--<Separator
                        Width="2"
                        Height="30"
                        Margin="10,0"
                        Background="LightGray"
                        Style="{StaticResource SeparatorVertical}" />-->

                    <!--  From单元位置  -->
                    <ComboBox
                        Width="120"
                        Margin="5,0"
                        hc:InfoElement.Necessary="True"
                        hc:InfoElement.Placeholder="请选择From单元位置："
                        hc:InfoElement.Symbol="*"
                        hc:InfoElement.Title="From单元位置："
                        Cursor="Hand"
                        DisplayMemberPath="ChamberName"
                        ItemsSource="{Binding FromChamber}"
                        SelectedItem="{Binding SelectedFromChamber}"
                        Style="{StaticResource ComboBoxExtend.Small}" />

                    <!--  From SLOT  -->
                    <ComboBox
                        Width="80"
                        Margin="5,0"
                        d:ItemsSource="{d:SampleData ItemCount=5}"
                        hc:InfoElement.Necessary="True"
                        hc:InfoElement.Placeholder="请选择SLOT："
                        hc:InfoElement.Symbol="*"
                        hc:InfoElement.Title="From SLOT："
                        DisplayMemberPath="WaferNo"
                        ItemsSource="{Binding FromAvailableWafers}"
                        SelectedValue="{Binding SelectedFromSlot, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged, FallbackValue=1, TargetNullValue=1}"
                        SelectedValuePath="WaferNo"
                        Style="{StaticResource ComboBoxExtend.Small}" />

                    <!--  To单元位置  -->
                    <ComboBox
                        Width="120"
                        Margin="5,0"
                        hc:InfoElement.Necessary="True"
                        hc:InfoElement.Placeholder="请选择To单元位置："
                        hc:InfoElement.Symbol="*"
                        hc:InfoElement.Title="To单元位置："
                        Cursor="Hand"
                        DisplayMemberPath="ChamberName"
                        ItemsSource="{Binding ToChamber}"
                        SelectedItem="{Binding SelectedToChamber}"
                        SelectedValuePath="ChamberName"
                        Style="{StaticResource ComboBoxExtend.Small}" />

                    <!--  To SLOT  -->
                    <ComboBox
                        Width="80"
                        Margin="5,0"
                        d:ItemsSource="{d:SampleData ItemCount=5}"
                        hc:InfoElement.Necessary="True"
                        hc:InfoElement.Placeholder="请选择SLOT："
                        hc:InfoElement.Symbol="*"
                        hc:InfoElement.Title="To SLOT："
                        DisplayMemberPath="WaferNo"
                        ItemsSource="{Binding ToAvailableWafers}"
                        SelectedValue="{Binding SelectedToSlot, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged, FallbackValue=1, TargetNullValue=1}"
                        SelectedValuePath="WaferNo"
                        Style="{StaticResource ComboBoxExtend.Small}" />

                    <!--  机械臂位置  -->
                    <ComboBox
                        Width="100"
                        Margin="5,0"
                        hc:InfoElement.Necessary="True"
                        hc:InfoElement.Placeholder="请选择机械臂："
                        hc:InfoElement.Symbol="*"
                        hc:InfoElement.Title="机械臂位置："
                        Cursor="Hand"
                        ItemsSource="{Binding ByArmFetchSide}"
                        SelectedIndex="0"
                        SelectedItem="{Binding SelectedByArmFetchSide}"
                        Style="{StaticResource ComboBoxExtend.Small}" />

                    <!--  搬运按钮  -->
                    <Button
                        Margin="5,0,10,0"
                        d:Visibility="Visible"
                        Command="{Binding TrasferWaferCommand}"
                        Content="搬运"
                        Style="{StaticResource ButtonPrimary}"
                        ToolTip="执行晶圆搬运操作，循环次数由左侧循环次数文本框控制" />

                    <!--  分隔符  -->
                    <!--<Separator
                        Width="2"
                        Height="30"
                        Margin="10,0"
                        Background="LightGray"
                        Style="{StaticResource SeparatorVertical}" />-->

                    <!--  搬运测试按钮  -->
                    <Button
                        Margin="5,0"
                        d:Visibility="Visible"
                        Command="{Binding TrasferWaferTestCommand}"
                        Content="搬运测试"
                        Style="{StaticResource ButtonPrimary}"
                        ToolTip="开发调试模式下的搬运测试功能"
                        Visibility="{Binding Source={x:Static common:Golbal.IsDevDebug}, Converter={StaticResource BooleanToVisibilityConverter}}" />

                    <!--  获取RTZ位置按钮  -->
                    <Button
                        Margin="5,0"
                        Command="{Binding GetCurrentRTZPositionCommand}"
                        Content="获取当前RTZ位置"
                        Style="{StaticResource ButtonInfo}"
                        ToolTip="自动获取当前RTZ轴位置并填充到对应的输入框中" />
                </WrapPanel>
            </GroupBox>

            <!--  底部区域：Robot命令实例  -->
            <GroupBox
                Grid.Row="5"
                Grid.Column="0"
                Grid.ColumnSpan="3"
                Margin="10,25,10,5"
                Header="Robot命令实例">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition />
                        <ColumnDefinition Width="auto" />
                        <ColumnDefinition Width="3*" />
                    </Grid.ColumnDefinitions>

                    <ComboBox
                        Grid.Row="0"
                        Grid.Column="5"
                        hc:InfoElement.Necessary="True"
                        hc:InfoElement.Placeholder="请选择机命令："
                        hc:InfoElement.Symbol="*"
                        hc:InfoElement.Title="命令列表选择："
                        Cursor="Hand"
                        ItemsSource="{Binding RobotCommandList}"
                        SelectedItem="{Binding SelectedRobotCommand, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                        Style="{StaticResource ComboBoxExtend.Small}">
                        <!--<i:Interaction.Triggers>
                            <i:EventTrigger EventName="SelectionChanged">
                                <i:InvokeCommandAction Command="{Binding ExecuteSelectedRobotCmdCommand}" />
                            </i:EventTrigger>
                        </i:Interaction.Triggers>-->
                    </ComboBox>

                    <Grid
                        Grid.Row="0"
                        Grid.Column="6"
                        Cursor="Hand"
                        ToolTip="安全提醒：在手动模式非运行状态下才可以执行手动命令">

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <Button
                            Margin="10,0"
                            d:Visibility="Visible"
                            Command="{Binding ExecuteSelectedRobotCmdCommand}"
                            Content="命令执行"
                            IsEnabled="{Binding Source={x:Static common:Golbal.IsDevDebug}}"
                            Style="{StaticResource ButtonPrimary}" />
                    </Grid>

                    <TextBox
                        Grid.Column="7"
                        Grid.ColumnSpan="99"
                        Margin="10,0"
                        VerticalAlignment="Stretch"
                        hc:InfoElement.Placeholder="执行状态"
                        hc:InfoElement.Title="执行状态"
                        hc:InfoElement.TitlePlacement="Left"
                        hc:TitleElement.HorizontalAlignment="Right"
                        hc:TitleElement.TitleWidth="80"
                        Style="{StaticResource TextBoxExtend}"
                        Text="{Binding CmdResult}"
                        ToolTip="命令执行状态结果" />

                    <GroupBox
                        Grid.Row="1"
                        Grid.ColumnSpan="99"
                        Margin="5"
                        Header="自定义命令测试">
                        <Border CornerRadius="4">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <StackPanel
                                    Grid.Column="1"
                                    HorizontalAlignment="Center"
                                    Orientation="Horizontal">
                                    <CheckBox
                                        Margin="5"
                                        VerticalAlignment="Center"
                                        Content="是否允许三轴回原点"
                                        IsChecked="{Binding IsTRZAxisReturnZeroed, Mode=TwoWay}"
                                        Style="{StaticResource CheckBoxBaseStyle}"
                                        ToolTip="搬运、PinSearch是否一开始执行三轴回原点" />
                                    <ToggleButton
                                        Margin="5"
                                        HorizontalAlignment="Center"
                                        d:Visibility="Visible"
                                        Command="{Binding DevInterLockVerifyCommand}"
                                        Content="InterLock数据校验"
                                        Style="{StaticResource ToggleButtonInfo}"
                                        Visibility="{Binding Source={x:Static common:Golbal.IsDevDebug}, Converter={StaticResource BooleanToVisibilityConverter}}" />

                                    <ToggleButton
                                        Margin="5"
                                        HorizontalAlignment="Center"
                                        d:Visibility="Visible"
                                        Command="{Binding DevTestCommand}"
                                        Content="InterLock数据访问测试"
                                        Style="{StaticResource ToggleButtonInfo}"
                                        Visibility="{Binding Source={x:Static common:Golbal.IsDevDebug}, Converter={StaticResource BooleanToVisibilityConverter}}" />
                                </StackPanel>
                            </Grid>
                        </Border>
                    </GroupBox>
                </Grid>
            </GroupBox>
        </Grid>
    </Grid>
</UserControl>