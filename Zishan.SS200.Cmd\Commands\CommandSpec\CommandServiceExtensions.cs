using log4net;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Services.Interfaces;

namespace Zishan.SS200.Cmd.Commands.CommandSpec
{
    /// <summary>
    /// 命令服务扩展类，为S200McuCmdService提供便捷的命令执行方法
    /// </summary>
    public static class CommandServiceExtensions
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(CommandServiceExtensions));

        /// <summary>
        /// 使用命令代码执行命令
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="commandCode">命令代码</param>
        /// <param name="paramList">参数列表</param>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <returns>执行结果</returns>
        public static async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteCommandAsync(
            this IS200McuCmdService cmdService,
            string commandCode,
            List<ushort> paramList = null,
            int? timeout = null)
        {
            try
            {
                // 获取命令规格
                if (!CommandSpecManager.Instance.TryGetCommandSpec(commandCode, out var commandSpec))
                {
                    string errorMsg = $"找不到命令规格: {commandCode}";
                    _logger.Error(errorMsg);
                    return (errorMsg, 0, 0);
                }

                // 执行命令
                return await commandSpec.ExecuteAsync(cmdService, paramList, timeout);
            }
            catch (Exception ex)
            {
                string errorMsg = $"执行命令 {commandCode} 时发生错误: {ex.Message}";
                _logger.Error(errorMsg, ex);
                return (errorMsg, 0, 0);
            }
        }

        /// <summary>
        /// 使用命令代码执行命令，并以JSON格式返回结果
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="commandCode">命令代码</param>
        /// <param name="parameters">参数列表</param>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <returns>JSON格式的执行结果</returns>
        public static async Task<string> ExecuteCommandJsonAsync(
            this IS200McuCmdService cmdService,
            string commandCode,
            List<ushort> parameters = null,
            int? timeout = null)
        {
            try
            {
                // 执行命令
                var (response, runInfo, returnInfo) = await ExecuteCommandAsync(cmdService, commandCode, parameters, timeout);

                // 构建JSON结果
                return System.Text.Json.JsonSerializer.Serialize(new
                {
                    Success = !response.StartsWith("Error:") && !response.StartsWith("Failed:"),
                    Response = response,
                    RunInfo = runInfo,
                    ReturnInfo = returnInfo,
                    Timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.Error($"执行命令 {commandCode} 并返回JSON结果时发生错误: {ex.Message}", ex);

                // 返回错误的JSON
                return System.Text.Json.JsonSerializer.Serialize(new
                {
                    Success = false,
                    Response = $"Error: {ex.Message}",
                    RunInfo = 0,
                    ReturnInfo = 0,
                    Timestamp = DateTime.Now
                });
            }
        }

        /// <summary>
        /// 获取所有命令规格
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <returns>命令规格列表</returns>
        public static IEnumerable<ICommandSpec> GetAllCommandSpecs(this IS200McuCmdService cmdService)
        {
            return CommandSpecManager.Instance.GetAllCommandSpecs();
        }

        /// <summary>
        /// 获取指定设备的所有命令规格
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="deviceType">设备类型</param>
        /// <returns>命令规格列表</returns>
        public static IEnumerable<ICommandSpec> GetDeviceCommandSpecs(this IS200McuCmdService cmdService, EnuMcuDeviceType deviceType)
        {
            return CommandSpecManager.Instance.GetDeviceCommandSpecs(deviceType);
        }

        /// <summary>
        /// 获取指定设备的所有命令规格 (兼容接口方法)
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="deviceType">设备类型字符串</param>
        /// <returns>命令规格列表</returns>
        public static IEnumerable<ICommandSpec> GetDeviceCommandSpecs(this IS200McuCmdService cmdService, string deviceType)
        {
            return CommandSpecManager.Instance.GetDeviceCommandSpecs(deviceType);
        }

        /// <summary>
        /// 执行机器人命令
        /// </summary>
        /// <param name="cmdService">命令服务实例</param>
        /// <param name="commandCode">命令代码</param>
        /// <param name="dynamicParameters">动态参数列表</param>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <returns>命令执行结果</returns>
        public static Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteRobotCommandAsync(
            this IS200McuCmdService cmdService,
            string commandCode,
            List<ushort> dynamicParameters = null,
            int? timeout = null)
        {
            // 查找命令规格
            if (CommandSpecManager.Instance.TryGetCommandSpec(commandCode, out var commandSpec))
            {
                if (commandSpec.DeviceType != EnuMcuDeviceType.Robot)
                {
                    throw new InvalidOperationException($"命令 {commandCode} 不是机器人命令");
                }

                // 执行机器人命令
                return commandSpec.ExecuteAsync(cmdService, dynamicParameters, timeout);
            }
            else
            {
                throw new InvalidOperationException($"找不到机器人命令: {commandCode}");
            }
        }

        /// <summary>
        /// 执行Shuttle命令
        /// </summary>
        /// <param name="cmdService">命令服务实例</param>
        /// <param name="commandCode">命令代码</param>
        /// <param name="dynamicParameters">动态参数列表</param>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <returns>命令执行结果</returns>
        public static Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteShuttleCommandAsync(
            this IS200McuCmdService cmdService,
            string commandCode,
            List<ushort> dynamicParameters = null,
            int? timeout = null)
        {
            // 查找命令规格
            if (CommandSpecManager.Instance.TryGetCommandSpec(commandCode, out var commandSpec))
            {
                if (commandSpec.DeviceType != EnuMcuDeviceType.Shuttle)
                {
                    throw new InvalidOperationException($"命令 {commandCode} 不是Shuttle命令");
                }

                // 执行Shuttle命令
                return commandSpec.ExecuteAsync(cmdService, dynamicParameters, timeout);
            }
            else
            {
                throw new InvalidOperationException($"找不到Shuttle命令: {commandCode}");
            }
        }

        /// <summary>
        /// 执行工艺腔室A命令
        /// </summary>
        /// <param name="cmdService">命令服务实例</param>
        /// <param name="commandCode">命令代码</param>
        /// <param name="dynamicParameters">动态参数列表</param>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <returns>命令执行结果</returns>
        public static Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteChaCommandAsync(
            this IS200McuCmdService cmdService,
            string commandCode,
            List<ushort> dynamicParameters = null,
            int? timeout = null)
        {
            // 查找命令规格
            if (CommandSpecManager.Instance.TryGetCommandSpec(commandCode, out var commandSpec))
            {
                if (commandSpec.DeviceType != EnuMcuDeviceType.ChamberA)
                {
                    throw new InvalidOperationException($"命令 {commandCode} 不是工艺腔室A命令");
                }

                // 执行工艺腔室A命令
                return commandSpec.ExecuteAsync(cmdService, dynamicParameters, timeout);
            }
            else
            {
                throw new InvalidOperationException($"找不到工艺腔室A命令: {commandCode}");
            }
        }

        /// <summary>
        /// 执行工艺腔室B命令
        /// </summary>
        /// <param name="cmdService">命令服务实例</param>
        /// <param name="commandCode">命令代码</param>
        /// <param name="dynamicParameters">动态参数列表</param>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <returns>命令执行结果</returns>
        public static Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteChbCommandAsync(
            this IS200McuCmdService cmdService,
            string commandCode,
            List<ushort> dynamicParameters = null,
            int? timeout = null)
        {
            // 查找命令规格
            if (CommandSpecManager.Instance.TryGetCommandSpec(commandCode, out var commandSpec))
            {
                if (commandSpec.DeviceType != EnuMcuDeviceType.ChamberB)
                {
                    throw new InvalidOperationException($"命令 {commandCode} 不是工艺腔室B命令");
                }

                // 执行工艺腔室B命令
                return commandSpec.ExecuteAsync(cmdService, dynamicParameters, timeout);
            }
            else
            {
                throw new InvalidOperationException($"找不到工艺腔室B命令: {commandCode}");
            }
        }
    }
}