using System;
using System.Collections.Generic;
using Zishan.SS200.Cmd.Common;

namespace Zishan.SS200.Cmd.Config
{
    /// <summary>
    /// 延迟配置类 - 统一管理所有Task.Delay时间，支持调试和生产模式切换
    /// </summary>
    public static class DelayConfig
    {
        #region 基础配置

        /// <summary>
        /// 是否启用调试延迟模式（较长延迟便于观察）
        /// </summary>
        public static bool IsDebugDelayMode => Golbal.IsDevDebug;

        /// <summary>
        /// 最小延迟时间（毫秒）
        /// </summary>
        public const int MinDelayMs = 30;

        /// <summary>
        /// 最大延迟时间（毫秒）
        /// </summary>
        public const int MaxDelayMs = 2000;

        #endregion 基础配置

        #region UI相关延迟

        /// <summary>
        /// UI更新延迟 - 腔体选择更新等UI操作
        /// 调试模式：50ms，生产模式：30ms
        /// </summary>
        public static int UIUpdateDelay => IsDebugDelayMode ? 50 : 30;

        /// <summary>
        /// UI中间状态显示延迟 - 搬运过程中的状态显示
        /// 调试模式：100ms，生产模式：50ms
        /// </summary>
        public static int UIDisplayDelay => IsDebugDelayMode ? 100 : 50;

        /// <summary>
        /// UI动画演示延迟
        /// 调试模式：300ms，生产模式：100ms
        /// </summary>
        public static int UIAnimationDelay => IsDebugDelayMode ? 300 : 100;

        #endregion UI相关延迟

        #region 状态检查延迟

        /// <summary>
        /// 主循环状态检查延迟 - 最关键的性能优化点
        /// 调试模式：50ms，生产模式：30ms
        /// </summary>
        public static int StatusCheckDelay => IsDebugDelayMode ? 50 : 30;

        /// <summary>
        /// 快速状态检查延迟 - 用于需要快速响应的状态检查
        /// 调试模式：30ms，生产模式：30ms
        /// </summary>
        public static int FastStatusCheckDelay => IsDebugDelayMode ? 30 : 30;

        /// <summary>
        /// 工艺完成等待延迟 - Cooling等工艺完成检查
        /// 调试模式：100ms，生产模式：30ms
        /// </summary>
        public static int ProcessWaitDelay => IsDebugDelayMode ? 100 : 30;

        #endregion 状态检查延迟

        #region PLC通信延迟

        /// <summary>
        /// PLC命令等待延迟 - PLC命令发送后的等待时间
        /// 调试模式：200ms，生产模式：100ms
        /// </summary>
        public static int PLCCommandDelay => IsDebugDelayMode ? 200 : 100;

        /// <summary>
        /// PLC响应轮询延迟 - 等待PLC响应的轮询间隔
        /// 调试模式：50ms，生产模式：30ms
        /// </summary>
        public static int PLCPollingDelay => IsDebugDelayMode ? 50 : 30;

        /// <summary>
        /// PLC自动点击等待延迟
        /// 调试模式：100ms，生产模式：50ms
        /// </summary>
        public static int PLCAutoClickDelay => IsDebugDelayMode ? 100 : 50;

        #endregion PLC通信延迟

        #region 搬运操作延迟

        /// <summary>
        /// 搬运显示延迟 - 搬运操作完成后的显示延迟
        /// 调试模式：100ms，生产模式：50ms
        /// </summary>
        public static int TransferDisplayDelay => IsDebugDelayMode ? 100 : 50;

        /// <summary>
        /// PinSearch操作间隔延迟
        /// 调试模式：50ms，生产模式：30ms
        /// </summary>
        public static int PinSearchDelay => IsDebugDelayMode ? 50 : 30;

        #endregion 搬运操作延迟

        #region 系统控制延迟

        /// <summary>
        /// 暂停状态等待延迟 - 系统暂停时的等待间隔
        /// 适度减少以提高响应性
        /// </summary>
        public static int PauseStateDelay => 200;

        /// <summary>
        /// 检测等待延迟 - 一般性检测操作的等待时间
        /// 调试模式：100ms，生产模式：50ms
        /// </summary>
        public static int DetectionDelay => IsDebugDelayMode ? 100 : 50;

        /// <summary>
        /// 错误重试延迟 - 操作失败后的重试间隔
        /// 调试模式：500ms，生产模式：200ms
        /// </summary>
        public static int ErrorRetryDelay => IsDebugDelayMode ? 500 : 200;

        #endregion 系统控制延迟

        #region 动态延迟方法

        /// <summary>
        /// 获取优化的延迟时间
        /// </summary>
        /// <param name="originalDelay">原始延迟时间</param>
        /// <param name="optimizationFactor">优化因子（0.1-1.0）</param>
        /// <returns>优化后的延迟时间</returns>
        public static int GetOptimizedDelay(int originalDelay, double optimizationFactor = 0.2)
        {
            if (IsDebugDelayMode)
            {
                return originalDelay; // 调试模式保持原延迟
            }

            var optimizedDelay = (int)(originalDelay * optimizationFactor);
            return Math.Max(optimizedDelay, MinDelayMs);
        }

        /// <summary>
        /// 根据循环次数获取自适应延迟
        /// </summary>
        /// <param name="baseDelay">基础延迟</param>
        /// <param name="loopCount">当前循环次数</param>
        /// <param name="maxReduction">最大减少比例（0.0-1.0）</param>
        /// <returns>自适应延迟时间</returns>
        public static int GetAdaptiveDelay(int baseDelay, int loopCount, double maxReduction = 0.5)
        {
            if (IsDebugDelayMode || loopCount <= 1)
            {
                return baseDelay;
            }

            // 随着循环次数增加，逐渐减少延迟
            var reductionFactor = Math.Min(loopCount * 0.05, maxReduction);
            var adaptiveDelay = (int)(baseDelay * (1 - reductionFactor));

            return Math.Max(adaptiveDelay, MinDelayMs);
        }

        /// <summary>
        /// 条件性延迟 - 仅在满足条件时执行延迟
        /// </summary>
        /// <param name="delayMs">延迟时间</param>
        /// <param name="condition">延迟条件</param>
        /// <returns>实际延迟时间（0或delayMs）</returns>
        public static int GetConditionalDelay(int delayMs, bool condition)
        {
            return condition ? Math.Max(delayMs, MinDelayMs) : 0;
        }

        /// <summary>
        /// 获取基于性能的延迟时间
        /// </summary>
        /// <param name="baseDelay">基础延迟</param>
        /// <param name="cpuUsage">CPU使用率（0.0-1.0）</param>
        /// <param name="memoryUsage">内存使用率（0.0-1.0）</param>
        /// <returns>基于性能调整的延迟时间</returns>
        public static int GetPerformanceBasedDelay(int baseDelay, double cpuUsage = 0.0, double memoryUsage = 0.0)
        {
            if (IsDebugDelayMode)
            {
                return baseDelay;
            }

            // 如果系统负载高，增加延迟以减少压力
            var loadFactor = Math.Max(cpuUsage, memoryUsage);
            if (loadFactor > 0.8) // 高负载
            {
                return (int)(baseDelay * 1.5);
            }
            else if (loadFactor > 0.6) // 中等负载
            {
                return baseDelay;
            }
            else // 低负载
            {
                return Math.Max((int)(baseDelay * 0.5), MinDelayMs);
            }
        }

        #endregion 动态延迟方法

        #region 延迟配置验证

        /// <summary>
        /// 验证延迟配置的合理性
        /// </summary>
        /// <returns>验证结果</returns>
        public static DelayConfigValidationResult ValidateConfig()
        {
            var result = new DelayConfigValidationResult();

            // 检查各项延迟配置是否在合理范围内
            CheckDelayRange(nameof(UIUpdateDelay), UIUpdateDelay, result);
            CheckDelayRange(nameof(StatusCheckDelay), StatusCheckDelay, result);
            CheckDelayRange(nameof(PLCCommandDelay), PLCCommandDelay, result);
            CheckDelayRange(nameof(TransferDisplayDelay), TransferDisplayDelay, result);

            return result;
        }

        private static void CheckDelayRange(string configName, int delayValue, DelayConfigValidationResult result)
        {
            if (delayValue < MinDelayMs)
            {
                result.Warnings.Add($"{configName} ({delayValue}ms) 低于最小延迟 {MinDelayMs}ms");
            }
            else if (delayValue > MaxDelayMs)
            {
                result.Warnings.Add($"{configName} ({delayValue}ms) 超过最大延迟 {MaxDelayMs}ms");
            }
            else
            {
                result.ValidConfigs.Add($"{configName}: {delayValue}ms");
            }
        }

        #endregion 延迟配置验证

        #region 延迟统计

        /// <summary>
        /// 获取延迟配置统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public static DelayConfigStats GetStats()
        {
            return new DelayConfigStats
            {
                IsDebugMode = IsDebugDelayMode,
                UIUpdateDelay = UIUpdateDelay,
                StatusCheckDelay = StatusCheckDelay,
                PLCCommandDelay = PLCCommandDelay,
                TransferDisplayDelay = TransferDisplayDelay,
                TotalEstimatedDelayPerLoop = UIUpdateDelay + StatusCheckDelay * 5 + PLCCommandDelay + TransferDisplayDelay,
                OptimizationRatio = IsDebugDelayMode ? 0 : 0.7 // 生产模式约70%优化
            };
        }

        #endregion 延迟统计
    }

    /// <summary>
    /// 延迟配置验证结果
    /// </summary>
    public class DelayConfigValidationResult
    {
        public List<string> ValidConfigs { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public bool IsValid => Warnings.Count == 0;
    }

    /// <summary>
    /// 延迟配置统计信息
    /// </summary>
    public class DelayConfigStats
    {
        public bool IsDebugMode { get; set; }
        public int UIUpdateDelay { get; set; }
        public int StatusCheckDelay { get; set; }
        public int PLCCommandDelay { get; set; }
        public int TransferDisplayDelay { get; set; }
        public int TotalEstimatedDelayPerLoop { get; set; }
        public double OptimizationRatio { get; set; }

        public override string ToString()
        {
            return $"模式: {(IsDebugMode ? "调试" : "生产")}, " +
                   $"预估每循环延迟: {TotalEstimatedDelayPerLoop}ms, " +
                   $"优化比例: {OptimizationRatio:P0}";
        }
    }
}