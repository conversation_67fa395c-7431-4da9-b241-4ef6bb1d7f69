using System.Collections.Generic;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.Command;
using Zishan.SS200.Cmd.Services.Interfaces;

namespace Zishan.SS200.Cmd.Commands.CommandSpec.Shuttle.Movement
{
    /// <summary>
    /// S1 SD - Shuttle下降
    /// </summary>
    public class S1_SDCommand : BaseCommandSpec<EnuShuttleCmd>
    {
        /// <summary>
        /// 状态管理实例
        /// </summary>
        private readonly S200MockStatus _mockStatus = S200MockStatus.Instance;

        /// <summary>
        /// 构造函数
        /// </summary>
        public S1_SDCommand() : base(
            EnuMcuDeviceType.Shuttle,
            EnuShuttleCmd.S1_SD,
             EnuShuttleCmd.S1_SD.ToString(),
            "Shuttle down",
            "Shuttle下降",
            22000)  // 从配置文件中获取的超时时间
        {
        }

        /// <summary>
        /// 参数验证
        /// </summary>
        /// <param name="parameters">参数列表</param>
        /// <returns>是否有效</returns>
        public override bool ValidateParameters(List<ushort> parameters)
        {
            // S1 SD命令不需要参数
            return parameters == null || parameters.Count == 0;
        }

        /// <summary>
        /// 执行前处理
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="parameters">参数列表</param>
        /// <returns>是否继续执行命令</returns>
        public override async Task<bool> BeforeExecuteAsync(IS200McuCmdService cmdService, List<ushort> parameters)
        {
            // 检查Shuttle是否处于空闲状态
            if (!_mockStatus.CheckShuttleStatus(cmdService))
            {
                return false;
            }

            // 检查Shuttle是否已经处于下降状态
            // 这里可以添加更多的安全检查逻辑

            // 执行前记录日志
            _logger.Info("准备执行Shuttle下降命令");
            return await base.BeforeExecuteAsync(cmdService, parameters);
        }

        /// <summary>
        /// 执行后处理
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="result">命令执行结果</param>
        /// <param name="parameters">参数列表</param>
        /// <returns>处理后的结果</returns>
        public override async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> AfterExecuteAsync(
            IS200McuCmdService cmdService,
            (string Response, ushort RunInfo, ushort ReturnInfo) result,
            List<ushort> parameters)
        {
            if (result.ReturnInfo == 0)
            {
                _logger.Info("Shuttle已成功下降");

                // 可以在这里添加后续操作，例如更新UI状态、触发下一步流程等
            }
            else
            {
                _logger.Error($"Shuttle下降失败，错误代码: 0x{result.ReturnInfo:X4}");

                // 根据错误代码提供更详细的错误信息
                string errorDetail = _mockStatus.GetShuttleErrorDetail(result.ReturnInfo);
                if (!string.IsNullOrEmpty(errorDetail))
                {
                    result.Response = $"{result.Response} - {errorDetail}";
                }
            }

            return await base.AfterExecuteAsync(cmdService, result, parameters);
        }
    }
}