using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace Zishan.SS200.Cmd.Examples
{
    /// <summary>
    /// UI线程优化使用示例
    /// 演示如何正确使用异步方法避免UI线程阻塞
    /// </summary>
    public partial class UIOptimizationExampleViewModel : ObservableObject
    {
        #region 示例1：剪贴板操作优化

        /// <summary>
        /// ✅ 正确示例：异步剪贴板操作
        /// </summary>
        [RelayCommand]
        private async Task CopyTextToClipboardAsync(string text)
        {
            try
            {
                // 使用异步版本，不会阻塞UI线程
                if (await TrySetClipboardTextAsync(text))
                {
                    StatusMessage = $"成功复制: {text}";
                }
                else
                {
                    StatusMessage = "复制失败";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"复制异常: {ex.Message}";
            }
        }

        /// <summary>
        /// 异步剪贴板设置方法（优化版本）
        /// </summary>
        private async Task<bool> TrySetClipboardTextAsync(string text, int maxRetries = 3, int retryDelayMs = 100)
        {
            if (string.IsNullOrEmpty(text)) return false;

            for (int attempt = 0; attempt <= maxRetries; attempt++)
            {
                try
                {
                    // ✅ 使用异步Dispatcher调用，避免死锁
                    if (Application.Current.Dispatcher.CheckAccess())
                    {
                        Clipboard.SetText(text);
                    }
                    else
                    {
                        await Application.Current.Dispatcher.InvokeAsync(() => Clipboard.SetText(text));
                    }
                    return true;
                }
                catch (Exception)
                {
                    if (attempt < maxRetries)
                    {
                        // ✅ 使用异步延迟，不阻塞UI线程
                        await Task.Delay(retryDelayMs);
                    }
                }
            }
            return false;
        }

        #endregion

        #region 示例2：长时间操作优化

        /// <summary>
        /// ✅ 正确示例：长时间操作不阻塞UI
        /// </summary>
        [RelayCommand]
        private async Task ProcessLongRunningTaskAsync()
        {
            try
            {
                IsProcessing = true;
                StatusMessage = "开始处理...";

                // 将长时间操作移到后台线程
                await Task.Run(async () =>
                {
                    // 模拟长时间处理
                    for (int i = 1; i <= 10; i++)
                    {
                        // ✅ 使用异步延迟，不阻塞UI线程
                        await Task.Delay(1000);

                        // ✅ 安全地更新UI
                        await Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            StatusMessage = $"处理进度: {i}/10";
                            Progress = i * 10;
                        });
                    }
                });

                StatusMessage = "处理完成！";
            }
            catch (Exception ex)
            {
                StatusMessage = $"处理失败: {ex.Message}";
            }
            finally
            {
                IsProcessing = false;
            }
        }

        /// <summary>
        /// ❌ 错误示例：会阻塞UI线程（仅供对比）
        /// </summary>
        [RelayCommand]
        private void ProcessLongRunningTaskBlocking()
        {
            // ❌ 不要这样做 - 会阻塞UI线程
            for (int i = 1; i <= 10; i++)
            {
                Thread.Sleep(1000);  // 阻塞UI线程
                StatusMessage = $"处理进度: {i}/10";
                Progress = i * 10;
            }
        }

        #endregion

        #region 示例3：晶圆处理操作优化

        /// <summary>
        /// ✅ 正确示例：异步晶圆处理
        /// </summary>
        [RelayCommand]
        private async Task ProcessWafersAsync()
        {
            try
            {
                IsProcessing = true;
                StatusMessage = "开始晶圆处理...";

                // 使用异步版本的晶圆处理方法
                var container = new Models.IR400.Container();
                var result = await container.ProcessWafersAsync();

                if (result)
                {
                    StatusMessage = "晶圆处理完成";
                }
                else
                {
                    StatusMessage = "晶圆处理失败";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"晶圆处理异常: {ex.Message}";
            }
            finally
            {
                IsProcessing = false;
            }
        }

        #endregion

        #region 示例4：无限循环操作优化

        private CancellationTokenSource _loopCancellationTokenSource;

        /// <summary>
        /// ✅ 正确示例：无限循环不阻塞UI
        /// </summary>
        [RelayCommand]
        private async Task StartInfiniteLoopAsync()
        {
            if (IsLooping) return;

            try
            {
                IsLooping = true;
                _loopCancellationTokenSource = new CancellationTokenSource();

                // 将循环逻辑移到后台线程
                await Task.Run(async () =>
                {
                    await ExecuteInfiniteLoopAsync(_loopCancellationTokenSource.Token);
                }, _loopCancellationTokenSource.Token);
            }
            catch (OperationCanceledException)
            {
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    StatusMessage = "循环已被取消";
                });
            }
            finally
            {
                IsLooping = false;
                _loopCancellationTokenSource?.Dispose();
                _loopCancellationTokenSource = null;
            }
        }

        /// <summary>
        /// 停止无限循环
        /// </summary>
        [RelayCommand]
        private void StopInfiniteLoop()
        {
            _loopCancellationTokenSource?.Cancel();
        }

        /// <summary>
        /// 执行无限循环逻辑（在后台线程中运行）
        /// </summary>
        private async Task ExecuteInfiniteLoopAsync(CancellationToken cancellationToken)
        {
            int loopCount = 0;

            while (!cancellationToken.IsCancellationRequested)
            {
                loopCount++;

                // 模拟一些处理工作
                await Task.Delay(1000, cancellationToken);

                // ✅ 批量更新UI，减少UI线程压力
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    StatusMessage = $"循环执行中: 第{loopCount}次";
                });

                // 每10次循环暂停一下，给UI线程更多时间
                if (loopCount % 10 == 0)
                {
                    await Task.Delay(100, cancellationToken);
                }
            }
        }

        #endregion

        #region 属性

        [ObservableProperty]
        private string _statusMessage = "就绪";

        [ObservableProperty]
        private int _progress = 0;

        [ObservableProperty]
        private bool _isProcessing = false;

        [ObservableProperty]
        private bool _isLooping = false;

        #endregion

        #region 资源释放

        /// <summary>
        /// ✅ 正确的资源释放方式
        /// </summary>
        public void Dispose()
        {
            try
            {
                // 取消所有正在进行的操作
                _loopCancellationTokenSource?.Cancel();
                _loopCancellationTokenSource?.Dispose();

                // ✅ 避免在Dispose中使用.Wait()
                // 如果必须等待异步操作完成，使用ConfigureAwait(false)
                // 但最好的做法是在应用程序关闭前提前取消操作
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常
                System.Diagnostics.Debug.WriteLine($"Dispose时发生错误: {ex.Message}");
            }
        }

        #endregion
    }

    #region 最佳实践总结

    /// <summary>
    /// UI线程优化最佳实践总结
    /// </summary>
    public static class UIOptimizationBestPractices
    {
        /// <summary>
        /// ✅ 推荐的做法
        /// </summary>
        public static class Recommended
        {
            // 1. 使用异步方法避免阻塞UI线程
            // await Task.Delay(ms) 替代 Thread.Sleep(ms)

            // 2. 使用异步Dispatcher调用
            // await Dispatcher.InvokeAsync() 替代 Dispatcher.Invoke()

            // 3. 长时间操作移到后台线程
            // await Task.Run(async () => { /* 长时间操作 */ })

            // 4. 使用CancellationToken支持取消操作
            // public async Task Method(CancellationToken cancellationToken = default)

            // 5. 批量更新UI减少频繁调用
            // 收集多个更新，一次性应用到UI
        }

        /// <summary>
        /// ❌ 应该避免的做法
        /// </summary>
        public static class Avoid
        {
            // 1. 在UI线程中使用Thread.Sleep()
            // 2. 在UI线程中使用.Wait()或.Result
            // 3. 在Dispose中使用.Wait()
            // 4. 频繁的同步Dispatcher.Invoke()调用
            // 5. 在UI线程中执行长时间运行的循环
        }
    }

    #endregion
}
