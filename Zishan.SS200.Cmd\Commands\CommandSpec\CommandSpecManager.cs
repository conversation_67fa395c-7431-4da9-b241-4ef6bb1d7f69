using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using log4net;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Commands.CommandSpec
{
    /// <summary>
    /// 命令规格管理器，负责加载、注册和查找命令规格
    /// </summary>
    public class CommandSpecManager
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(CommandSpecManager));
        private static readonly Lazy<CommandSpecManager> _instance = new Lazy<CommandSpecManager>(() => new CommandSpecManager());

        // 命令规格字典，Key为命令代码
        private readonly Dictionary<string, ICommandSpec> _commandSpecs = new Dictionary<string, ICommandSpec>(StringComparer.OrdinalIgnoreCase);

        /// <summary>
        /// 获取单例实例
        /// </summary>
        public static CommandSpecManager Instance => _instance.Value;

        /// <summary>
        /// 私有构造函数
        /// </summary>
        private CommandSpecManager()
        {
            // 初始化加载所有命令规格
            LoadAllCommandSpecs();
        }

        /// <summary>
        /// 加载所有命令规格
        /// </summary>
        private void LoadAllCommandSpecs()
        {
            try
            {
                _logger.Info("开始加载命令规格...");

                // 获取当前程序集中所有实现了ICommandSpec接口的类型
                var assembly = Assembly.GetExecutingAssembly();
                var commandSpecTypes = assembly.GetTypes()
                    .Where(t => !t.IsAbstract && typeof(ICommandSpec).IsAssignableFrom(t))
                    .ToList();

                _logger.Info($"找到 {commandSpecTypes.Count} 个命令规格类型");

                // 实例化并注册每个命令规格
                foreach (var type in commandSpecTypes)
                {
                    try
                    {
                        var commandSpec = (ICommandSpec)Activator.CreateInstance(type);
                        RegisterCommandSpec(commandSpec);
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"实例化命令规格类型 {type.Name} 失败: {ex.Message}", ex);
                    }
                }

                _logger.Info($"成功加载 {_commandSpecs.Count} 个命令规格");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载命令规格时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 注册命令规格
        /// </summary>
        /// <param name="commandSpec">命令规格</param>
        public void RegisterCommandSpec(ICommandSpec commandSpec)
        {
            if (commandSpec == null)
            {
                throw new ArgumentNullException(nameof(commandSpec));
            }

            string commandCode = commandSpec.CommandCode;
            if (string.IsNullOrWhiteSpace(commandCode))
            {
                throw new ArgumentException("命令代码不能为空", nameof(commandSpec));
            }

            if (_commandSpecs.ContainsKey(commandCode))
            {
                _logger.Warn($"命令规格 {commandCode} 已存在，将被覆盖");
            }

            _commandSpecs[commandCode] = commandSpec;
            _logger.Info($"命令规格已注册: {commandCode} - {commandSpec.Description}");
        }

        /// <summary>
        /// 获取命令规格
        /// </summary>
        /// <param name="commandCode">命令代码</param>
        /// <returns>命令规格</returns>
        public ICommandSpec GetCommandSpec(string commandCode)
        {
            if (string.IsNullOrWhiteSpace(commandCode))
            {
                throw new ArgumentException("命令代码不能为空", nameof(commandCode));
            }

            if (_commandSpecs.TryGetValue(commandCode, out var commandSpec))
            {
                return commandSpec;
            }

            throw new KeyNotFoundException($"找不到命令规格: {commandCode}");
        }

        /// <summary>
        /// 尝试获取命令规格
        /// </summary>
        /// <param name="commandCode">命令代码</param>
        /// <param name="commandSpec">输出的命令规格</param>
        /// <returns>是否找到</returns>
        public bool TryGetCommandSpec(string commandCode, out ICommandSpec commandSpec)
        {
            if (string.IsNullOrWhiteSpace(commandCode))
            {
                commandSpec = null;
                return false;
            }

            return _commandSpecs.TryGetValue(commandCode, out commandSpec);
        }

        /// <summary>
        /// 获取所有命令规格
        /// </summary>
        /// <returns>命令规格列表</returns>
        public IEnumerable<ICommandSpec> GetAllCommandSpecs()
        {
            return _commandSpecs.Values;
        }

        /// <summary>
        /// 获取指定设备的所有命令规格
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>命令规格列表</returns>
        public IEnumerable<ICommandSpec> GetDeviceCommandSpecs(EnuMcuDeviceType deviceType)
        {
            return _commandSpecs.Values.Where(c => c.DeviceType == deviceType);
        }

        /// <summary>
        /// 获取指定设备的所有命令规格 (兼容接口方法)
        /// </summary>
        /// <param name="deviceType">设备类型字符串</param>
        /// <returns>命令规格列表</returns>
        public IEnumerable<ICommandSpec> GetDeviceCommandSpecs(string deviceType)
        {
            if (string.IsNullOrWhiteSpace(deviceType))
            {
                throw new ArgumentException("设备类型不能为空", nameof(deviceType));
            }

            // 尝试将字符串转换为枚举类型
            if (Enum.TryParse<EnuMcuDeviceType>(deviceType, true, out var deviceTypeEnum))
            {
                return GetDeviceCommandSpecs(deviceTypeEnum);
            }

            // 处理特殊情况，字符串名称与枚举不完全匹配
            // EnuMcuDeviceType? mappedType = deviceType.ToLower() switch
            // {
            //     "shuttle" => EnuMcuDeviceType.Shuttle,
            //     "robot" => EnuMcuDeviceType.Robot,
            //     "cha" => EnuMcuDeviceType.ChamberA,
            //     "chb" => EnuMcuDeviceType.ChamberB,
            //     _ => null
            // };
            //
            // if (mappedType.HasValue)
            // {
            //     return GetDeviceCommandSpecs(mappedType.Value);
            // }

            // 如果无法映射，返回空列表
            return Enumerable.Empty<ICommandSpec>();
        }
    }
}