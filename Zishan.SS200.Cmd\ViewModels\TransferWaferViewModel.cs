using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using GongSolutions.Wpf.DragDrop;
using Prism.Ioc;
using Prism.Regions;
using Prism.Services.Dialogs;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using System.Windows.Threading;
using Wu.Wpf.Common;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Config.SS200.AlarmCode.Robot;
using Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.Shuttle;
using Zishan.SS200.Cmd.Constants;
using Zishan.SS200.Cmd.Constants.SubsystemConfigure.Robot;
using Zishan.SS200.Cmd.DragDropHandler;
using Zishan.SS200.Cmd.DTO;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Enums.SS200.AlarmCode.Robot;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.Robot;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Interface;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.Models.History;
using Zishan.SS200.Cmd.Models.IR400;
using Zishan.SS200.Cmd.Models.IR400.PlcStatus;
using Zishan.SS200.Cmd.Models.Process;
using Zishan.SS200.Cmd.Models.Recipe;
using Zishan.SS200.Cmd.Models.Shared;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber;
using Zishan.SS200.Cmd.Mvvm;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.UserControls;
using Zishan.SS200.Cmd.Views;
using Zishan.SS200.Cmd.Views.Dialogs;

namespace Zishan.SS200.Cmd.ViewModels
{
    /// <summary>
    /// 晶圆搬运视图模型，用于处理晶圆在不同容器之间的搬运操作
    /// </summary>
    public partial class TransferWaferViewModel : ViewModel, IDragSource, IDropTarget
    {
        #region 依赖服务和基础字段

        private readonly IContainerProvider provider;
        private readonly IDialogHostService dialogHost;
        private readonly IS200McuCmdService _mcuCmdService;

        /// <summary>
        /// MCU命令服务
        /// </summary>
        public IS200McuCmdService McuCmdService => _mcuCmdService;

        public string DialogHostName { get; set; } = "RootDialog";

        #endregion 依赖服务和基础字段

        #region 状态字段和计时器

        /// <summary>
        /// 是否UI动画演示循环
        /// </summary>
        private bool _isUIAnimationDemoLoop = false;

        /// <summary>
        /// 耗时运行开始时间
        /// </summary>
        private DateTime _AppStartDateTime = DateTime.Now;

        private readonly DispatcherTimer _timer = new DispatcherTimer();
        private readonly DispatcherTimer _readDataTimer = new DispatcherTimer();

        /// <summary>
        /// 按过停止按钮标记
        /// </summary>
        private bool _isStopFlag = false;

        /// <summary>
        /// 是否比较总数量,默认为:True
        /// </summary>
        public bool CompareTotalCount { get; } = false;

        /// <summary>
        /// 设置参数总数量，默认为:528=44*4*3
        /// </summary>
        public int CountTotalParameters { get; } = 774;

        #endregion 状态字段和计时器

        #region PLC相关字段

        private string adsAddress = string.Empty;

        /// <summary>
        /// 大批量读写PLC变量，统一存储管理
        /// </summary>
        private Dictionary<string, object> _dicVariableNames = new Dictionary<string, object>();

        // /// <summary>
        // /// 读取变量数量
        // /// </summary>
        // //private int _readCount = 0;

        /// <summary>
        /// 后台读取PLC日志
        /// </summary>
        private readonly CancellationTokenSource _ctsStartTaskReadPlcLog = new CancellationTokenSource();

        #endregion PLC相关字段

        #region Robot控制相关字段

        /// <summary>
        /// Robot联合命令 取消再往下执行标志
        /// </summary>
        public bool CancelRobotCmdNextExcute = false;

        /// <summary>
        /// Robot联合命令 暂停后下次再往下执行标志
        /// </summary>
        public bool PauseRobotCmdNextExcute = false;

        /// <summary>
        /// 路径规划  命令运行取消标志
        /// </summary>
        public CancellationTokenSource CtsPathPlanning = new CancellationTokenSource();

        private readonly Dictionary<int, ArmFetchProcesHistory> armFetchHistories = new Dictionary<int, ArmFetchProcesHistory>();

        /// <summary>
        /// 机械臂抓取历史记录
        /// </summary>
        public Dictionary<int, ArmFetchProcesHistory> ArmFetchHistories => armFetchHistories;

        #endregion Robot控制相关字段

        #region 辅助字段

        private string _strMsg;
        private List<string> _errorMsgList;

        #endregion 辅助字段

        #region 基本属性

        /// <summary>
        /// 版本标题
        /// </summary>
        [ObservableProperty]
        private string title = "Robot_IR400_2024-09-21-M1";

        /// <summary>
        /// 应用程序运行配置信息：数据库连接方式、PLC Net ID
        /// </summary>
        [ObservableProperty]
        private string appConfigInfo = $"【DBAccessType：{App.AppIniConfig.DatabaseAccessType.ToString()}】";

        #endregion 基本属性

        #region 运行状态属性

        /// <summary>
        /// 是否正在运作中，用于是否允许拖拽
        /// </summary>
        public bool IsRunning
        {
            get => _IsRunning;
            set
            {
                SetProperty(ref _IsRunning, value);
                CurRecipeName.IsRunning = value;
            }
        }
        private bool _IsRunning;

        /// <summary>
        /// 后台Wafer拖拽处理
        /// </summary>
        [ObservableProperty]
        private WaferDragDropHandler waferDragDropHandler;

        /// <summary>
        /// From可选 ID列表
        /// </summary>
        public ObservableCollection<Wafer> FromAvailableWafers { get; set; } = new ObservableCollection<Wafer>();

        /// <summary>
        /// To可选 ID列表
        /// </summary>
        public ObservableCollection<Wafer> ToAvailableWafers { get; set; } = new ObservableCollection<Wafer>();

        /// <summary>
        /// PLC信号模拟
        /// </summary>
        [ObservableProperty]
        private PLcsignalSimulation curPLcsignalSimulation;

        /// <summary>
        /// SlitDoor控制命令
        /// </summary>
        [ObservableProperty]
        private SlitDoorSignal curSlitDoorSignal;

        /// <summary>
        /// 滑片信号,侦测到，停止运行，上位机不执行下条发送命令,路径规划流程停止并且退出
        /// </summary>
        [ObservableProperty]
        private WaferDropDetection curWaferDropDetection;

        /// <summary>
        /// 是否显示确认报警
        /// </summary>
        [ObservableProperty]
        private bool isShowAlarmConfirmed;

        /// <summary>
        /// 文件配方信息列表
        /// </summary>
        [ObservableProperty]
        private List<FileRecipeInfo> fileRecipeInfoList;

        /// <summary>
        /// 文件机限信息
        /// </summary>
        [ObservableProperty]
        private AllFlowMachineLimit machineLimit;

        /// <summary>
        /// 当前选中的配方信息
        /// </summary>
        [ObservableProperty]
        private FileRecipeInfo selectFileRecipeInfo;

        /// <summary>
        /// Wafer实际使用总数，不能超过Cassette容量，比如：25
        /// </summary>
        public int WaferTotalCount
        {
            get { return _WaferTotalCount; }
            set
            {
                if (SetProperty(ref _WaferTotalCount, value))
                {
                    if (value <= Cassette.Capacity)
                    {
                        Cassette.LeftWaferAction.Capacity = value;
                        Cassette.RightWaferAction.Capacity = value;
                        Cassette.CalWaferAction();
                        OnProcessReset();
                    }
                    else
                    {
                        WaferTotalCount = Cassette.Capacity;
                        MessageBox.Show($"Wafer总数不能超过Cassette容量：{Cassette.Capacity}");
                    }
                }
            }
        }
        private int _WaferTotalCount = 25;

        /// <summary>
        /// 同步PLC左边Wafer实际使用总数
        /// </summary>
        [ObservableProperty]
        private int leftWaferRemainingRealCount;

        /// <summary>
        /// 同步PLC右边Wafer实际使用总数
        /// </summary>
        [ObservableProperty]
        private int rightWaferRemainingRealCount;

        /// <summary>
        /// 去腔体取放动作
        /// </summary>
        [ObservableProperty]
        private EnuChamberGoAction curChamberGoAction = EnuChamberGoAction.None;

        [ObservableProperty]
        private EnuChamberName toChamberAngle = EnuChamberName.Home;

        /// <summary>
        ///  运动速度，单位：秒，默认3秒
        /// </summary>
        [ObservableProperty]
        private int moveSpeed = 3;

        [ObservableProperty]
        private int slot;

        /// <summary>
        /// 后台动态判断勾选 手动模式命令选项
        /// </summary>
        public bool IsMonitoringManualOperation { get; set; } = true;

        /// <summary>
        /// Robot Nose和Smooth是否有Wafer
        /// </summary>
        public bool HasWafer => LeftRobotIRArm.HasWafer || RightRobotIRArm.HasWafer;

        public RobotArmNew LeftRobotIRArm
        {
            get => _LeftRobotIRArm;
            set
            {
                if (_LeftRobotIRArm != null)
                {
                    _LeftRobotIRArm.PropertyChanged -= RobotArmNew_PropertyChanged;
                }

                if (SetProperty(ref _LeftRobotIRArm, value) && value != null)
                {
                    _LeftRobotIRArm.PropertyChanged += RobotArmNew_PropertyChanged;
                }
            }
        }
        private RobotArmNew _LeftRobotIRArm;

        public RobotArmNew RightRobotIRArm
        {
            get => _RightRobotIRArm;
            set
            {
                if (_RightRobotIRArm != null)
                {
                    _RightRobotIRArm.PropertyChanged -= RobotArmNew_PropertyChanged;
                }

                if (SetProperty(ref _RightRobotIRArm, value) && value != null)
                {
                    _RightRobotIRArm.PropertyChanged += RobotArmNew_PropertyChanged;
                }
            }
        }
        private RobotArmNew _RightRobotIRArm;

        /// <summary>
        /// Robot R轴当前值
        /// </summary>
        [ObservableProperty]
        private double robotRAxisCurrentValue;

        /// <summary>
        /// 转换后的X坐标
        /// </summary>
        [ObservableProperty]
        private double robotCurTranslateX;

        /// <summary>
        /// 转换后的Y坐标
        /// </summary>
        [ObservableProperty]
        private double robotCurTranslateY;

        /// <summary>
        /// Robot T轴当前值
        /// </summary>
        [ObservableProperty]
        private double robotTAxisCurrentValue;

        private void RobotArmNew_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(RobotArmNew.HasWafer))
            {
                OnPropertyChanged(nameof(HasWafer));
            }
        }

        [ObservableProperty]
        private Chamber chamberA;

        [ObservableProperty]
        private Chamber chamberB;

        [ObservableProperty]
        private Chamber chamberC;

        [ObservableProperty]
        private Cassette cassette;

        [ObservableProperty]
        private CoolingChamber cooling = null!;

        [ObservableProperty]
        private List<Wafer> waferCassette = null!;

        [ObservableProperty]
        private bool isTabItemtMainSelected = true;

        [ObservableProperty]
        private bool isTabItemtSecsSelected;

        /// <summary>
        /// 从哪个腔体取片
        /// </summary>
        public ObservableCollection<BContainer> FromChamber { get; } = new ObservableCollection<BContainer>();

        /// <summary>
        /// 取片到哪个腔体
        /// </summary>
        public ObservableCollection<BContainer> ToChamber { get; } = new ObservableCollection<BContainer>();

        /// <summary>
        /// 机械手臂Nose端、Smooth端、Unknow = 2

        /// </summary>
        [ObservableProperty]
        private Array byArmFetchSide = null!;

        public BContainer SelectedFromChamber
        {
            get => _SelectedFromChamber!;
            set
            {
                SetProperty(ref _SelectedFromChamber, value);
                AutoSelectedFromChamber(value);
                IsRunCmd = CheckIsRunCmd();
            }
        }
        private BContainer _SelectedFromChamber;

        [ObservableProperty]
        private int? selectedFromSlot;

        public BContainer SelectedToChamber
        {
            get => _SelectedToChamber!;
            set
            {
                SetProperty(ref _SelectedToChamber, value);
                AutoSelectedToChamber(value);
                IsRunCmd = CheckIsRunCmd();
            }
        }
        private BContainer _SelectedToChamber;

        [ObservableProperty]
        private int? selectedToSlot;

        public EnuArmFetchSide SelectedByArmFetchSide
        {
            get => _SelectedByArmFetchSide;
            set
            {
                SetProperty(ref _SelectedByArmFetchSide, value);
                IsRunCmd = CheckIsRunCmd();
            }
        }
        private EnuArmFetchSide _SelectedByArmFetchSide;

        [ObservableProperty]
        private bool isRunCmd;

        [ObservableProperty]
        private bool excuteResult;

        [ObservableProperty]
        private string requestCmd = null!;

        /// <summary>
        /// 命名翻译，描述信息
        /// </summary>
        [ObservableProperty]
        private string requestCmdDesc = null!;

        [ObservableProperty]
        private string responseCmd;

        [ObservableProperty]
        private string responseCmdStatus;

        [ObservableProperty]
        private string errorMessageInfo = string.Empty;

        [ObservableProperty]
        private bool isReset = true;

        [ObservableProperty]
        private bool isRobotReset = true;

        /// <summary>
        /// 是否停止循环运行
        /// </summary>
        public bool IsStopLoopRunning
        {
            get => _IsStopLoopRunning;
            set
            {
                SetProperty(ref _IsStopLoopRunning, value);
                IsEnableStopCmd = !IsStopLoopRunning || IsCmdRunning;
            }
        }
        private bool _IsStopLoopRunning = true;

        /// <summary>
        /// 命令是否正在运行
        /// </summary>
        public bool IsCmdRunning
        {
            get => _IsCmdRunning;
            set
            {
                SetProperty(ref _IsCmdRunning, value);
                IsEnableStopCmd = !IsStopLoopRunning || IsCmdRunning;
                IsRunning = IsCmdRunning;
            }
        }
        private bool _IsCmdRunning;

        [ObservableProperty]
        private bool isEnableStopCmd;

        [ObservableProperty]
        private bool isEnablePauseCmd;

        /// <summary>
        /// 暂停和继续标志：通过CancellationTokenSource实现
        /// </summary>
        [ObservableProperty]
        private bool isPauseAndContinue;

        [ObservableProperty]
        private string isEnablePauseContent = "暂停";

        [ObservableProperty]
        private int loopCount = 1;

        /// <summary>
        /// 是否已经运行 整机流程
        /// </summary>
        [ObservableProperty]
        private bool isProcessAutoRunStart;

        #region 报警日志记录UI绑定展示

        [ObservableProperty]
        private ObservableCollection<LogInfo> alarmInfos = new ObservableCollection<LogInfo>();

        public int currentAlarmInfoIndex = 1;

        /// <summary>
        /// 添加报警信息
        /// </summary>
        /// <param name="alarmInfo">报警信息</param>
        /// <returns>是否添加成功</returns>
        public bool AddAlarm(string alarmInfo)
        {
            // 检查是否已存在具有相同消息的alarmInfo
            if (AlarmInfos.Any(a => a.Message == alarmInfo))
            {
                return false;
            }

            LogInfo alarmModel = new LogInfo
            {
                Index = currentAlarmInfoIndex++,
                Message = alarmInfo
            };

            Application.Current.Dispatcher.Invoke(() => AlarmInfos.Add(alarmModel));
            AppLog.Info(alarmInfo);

            return true;
        }

        /// <summary>
        /// 移除报警信息
        /// </summary>
        /// <param name="alarmInfo">报警信息</param>
        /// <returns>是否移除成功</returns>
        public bool RemoveAlarm(string alarmInfo)
        {
            // 查找具有指定消息的第一个LogInfo对象
            var alarmToRemove = AlarmInfos.FirstOrDefault(a => a.Message == alarmInfo);
            if (alarmToRemove == null)
            {
                return false;
            }

            bool result = false;
            Application.Current.Dispatcher.Invoke(() => result = AlarmInfos.Remove(alarmToRemove));

            if (result)
            {
                AppLog.Info($"Removed alarm: {alarmInfo}");
            }

            return result;
        }

        #endregion 报警日志记录UI绑定展示

        #region Run Recipe

        /// <summary>
        /// 当前配方名列表
        /// </summary>
        [ObservableProperty]
        private RecipeNames curRecipeName;

        /// <summary>
        /// 当前选中的数据库流程配方信息
        /// </summary>
        [ObservableProperty]
        private RecipeSeqInfo curDbRecipeSeqInfo;

        /// <summary>
        /// 制定配方
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> recipeList;

        /// <summary>
        /// 非UI后台选中的配方名
        /// </summary>
        public string CurSelectedRecipe
        {
            get
            {
                if (CurRecipeName != null && CurRecipeName.CurSelectedRecipeName != null)
                {
                    _CurSelectedRecipe = CurRecipeName.CurSelectedRecipeName.KeyValue;
                }
                return _CurSelectedRecipe;
            }
            set
            {
                if (SetProperty(ref _CurSelectedRecipe, value))
                {
                    if (!string.IsNullOrWhiteSpace(value))
                    {
                        CurDbRecipeSeqInfo = CurRecipeName.DbRecipeSeqInfo.FirstOrDefault(t => t.RecipeName == value);

                        // 更新当前选中的配方名
                        CurRecipeName.CurSelectedRecipeName = CurRecipeName.RecipeNameList.FirstOrDefault(t => t.KeyValue == value);

                        if (CurDbRecipeSeqInfo != null)
                        {
                            HcGrowlExtensions.Info($"{CurDbRecipeSeqInfo.RecipeName}配方信息：\r\n{CurDbRecipeSeqInfo.ToString()}，请确认这些配方配置是否OK");
                        }
                    }
                }
            }
        }
        private string _CurSelectedRecipe;

        /// <summary>
        /// Wafer数量列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<int> waferCountList;

        /// <summary>
        /// CurrentDto
        /// </summary>
        [ObservableProperty]
        private object currentDto = new();

        #endregion Run Recipe

        #region 整机运行流程命令

        /// <summary>
        /// 整机运行流程命令【带上升沿触发】
        /// </summary>
        [ObservableProperty]
        private WholeProcessCmdTriggered wholeProcessCmdTriggered = new WholeProcessCmdTriggered();

        #endregion 整机运行流程命令

        #region 来自IR400ViewModel_UI

        private readonly int _CurRunRecipeInfoId = -1;

        #region **************************************** 属性 ****************************************

        /// <summary>
        /// 当前运行条码配方信息
        /// </summary>
        [ObservableProperty]
        private DtoRunRecipeInfo curRunRecipeInfo = new DtoRunRecipeInfo();

        /// <summary>
        /// EAP远程运行条码配方信息
        /// </summary>
        private readonly DtoRunRecipeInfo curRemoteRunRecipeInfo = null;

        [ObservableProperty]
        private string message;

        /// <summary>
        /// 工艺进度总时间
        /// </summary>
        [ObservableProperty]
        private TimeSpan totalProcessTime = TimeSpan.Zero;

        /// <summary>
        /// 主机状态
        /// </summary>
        [ObservableProperty]
        private MainHost host;

        /// <summary>
        /// 腔体LoadLock
        /// </summary>
        [ObservableProperty]
        private Loadlock loadlock;

        /// <summary>
        /// Buffer
        /// </summary>
        [ObservableProperty]
        private BufferChamber buffer;

        /// <summary>
        /// Wafer编号区间范围
        /// </summary>
        [ObservableProperty]
        private string waferNoRange;

        /// <summary>
        /// 当前登入用户信息
        /// </summary>
        [ObservableProperty]
        private Base_User curUserInfo;

        /// <summary>
        /// 左边Wafer控件测试数据
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<Wafer> leftWafers;

        /// <summary>
        /// 右边Wafer控件测试数据
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<Wafer> rightWafers;

        #endregion **************************************** 属性 ****************************************

        #endregion 来自IR400ViewModel_UI

        #endregion 运行状态属性

        #region 构造函数

        public TransferWaferViewModel(IContainerProvider containerProvider, IDialogHostService dialogHostService, IRegionManager regionManager, IS200McuCmdService mcuCmdService, RecipeNames recipeNames) : this()
        {
            provider = containerProvider ?? throw new ArgumentNullException(nameof(containerProvider));
            dialogHost = dialogHostService ?? throw new ArgumentNullException(nameof(dialogHostService));
            _mcuCmdService = mcuCmdService ?? throw new ArgumentNullException(nameof(mcuCmdService));

            CurRecipeName = recipeNames ?? throw new ArgumentNullException(nameof(recipeNames));

            //重新选中原来的配方
            if (CurRecipeName != null && CurRecipeName.CurSelectedRecipeName != null && !string.IsNullOrEmpty(CurRecipeName.CurSelectedRecipeName.KeyValue))
            {
                CurSelectedRecipe = CurRecipeName.CurSelectedRecipeName.KeyValue;
            }
            else
            {
                CurSelectedRecipe = string.Empty;
            }

            Message = "Hello {IR400UiDesignView.ViewName}";

            IR400Instance();

            #region Run Recipe Test

            WaferCountList = new ObservableCollection<int> { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25 };
            bool blResult = true;
            string leftBarCode = "123456789";
            string rightBarCode = "987654321";
            if (blResult)
            {
                CurRunRecipeInfo.LeftBarcode = leftBarCode;
                CurRunRecipeInfo.RightBarcode = rightBarCode;
            }
            else
            {
                CurRunRecipeInfo.LeftBarcode = "123456789";
                CurRunRecipeInfo.RightBarcode = "987654321";

                AppLog.Debug($"生成条码失败，使用默认条码：{CurRunRecipeInfo.LeftBarcode}，{CurRunRecipeInfo.RightBarcode}");
            }

            RecipeList = new ObservableCollection<string>();
            if (!Golbal.IsDevDebug)
            {
                //根据dev扩展文件名获取SS200Modbus配方列表
                List<string> tempOpenRecipeList = PubHelper.GetRecipeList(Golbal.WorkRootPath, "*.dev");

                var tempRecipName = CurRecipeName.RecipeNameList.Where(t => tempOpenRecipeList.Contains(t.KeyValue)).Select(t => t.KeyValue).ToList();
                RecipeList.AddRange(tempRecipName);
            }
            else
            {
                var tempRecipName = CurRecipeName.RecipeNameList.Select(t => t.KeyValue).ToList();
                RecipeList.AddRange(tempRecipName);
            }

            if (Golbal.IsDevDebug)
            {
                CurRunRecipeInfo.Top = 1;
            }
            else
            {
                CurRunRecipeInfo.Top = 3;
            }
            CurRunRecipeInfo.Bottom = 1;

            #endregion Run Recipe Test

            // 使用 Dispatcher 调度异步操作在 UI 线程上执行
            Application.Current.Dispatcher.Invoke(async () => await ProcessAutoRunStart(""));
        }

        /// <summary>
        /// 后台动态更新报警信息
        /// </summary>
        private async Task UpdateAlarmsInBackground()
        {
            await Task.Run(() =>
            {
                lock (_lockObj)
                {
                    _curLogDatas.Clear();
                    // 动态添加、移除到报警列表
                    if (CurWaferDropDetection != null)
                    {
                        UpdateAlarm("CHA", CurWaferDropDetection.WaferDropDetection_CHA, "CHA晶圆掉落");
                        UpdateAlarm("CHB", CurWaferDropDetection.WaferDropDetection_CHB, "CHB晶圆掉落");
                        UpdateAlarm("CHC", CurWaferDropDetection.WaferDropDetection_CHC, "CHC晶圆掉落");
                        UpdateAlarm("LoadLock", CurWaferDropDetection.WaferDropDetection_LoadLock_And_CP, "LoadLock晶圆掉落");
                        UpdateAlarm("Stage_L", CurWaferDropDetection.WaferDropDetection_Stage_L, "Stage_L晶圆滑出");
                        UpdateAlarm("Stage_R", CurWaferDropDetection.WaferDropDetection_Stage_R, "Stage_R晶圆滑出");
                    }

                    // 更新报警信息到数据库中

                    //if (_curLogDatas.Count > 0)
                    //{
                    //    // 更新报警信息到数据库中
                    //    _logService.AddLogDatas(_curLogDatas);
                    //}
                    /*if (_curLogDatas.Count > 0)
                    {
                        var retResult = _dBAccessService.AddLogData(_curLogDatas);
                        AppLog.Info($"插入PLC 报警数据{retResult}条");
                        _curLogDatas.Clear();
                    }*/

                    IsShowAlarmConfirmed = AlarmInfos.Count > 0;
                }
            });
        }

        private readonly List<LogData> _curLogDatas = new List<LogData>();
        private readonly object _lockObj = new object();

        private void UpdateAlarm(string name, bool condition, string alarmMessage)
        {
            if (condition)
            {
                // 如果报警条件为真，则添加报警
                Application.Current.Dispatcher.Invoke(() =>
                {
                    bool blResult = AddAlarm(alarmMessage);
                    if (blResult)
                    {
                        _curLogDatas.Add(new LogData(EnuLogDataType.Alarm) { PosName0 = name, MessageDetail = alarmMessage, MessageStatus = "开始报警", SystemTime = DateTime.Now });
                    }
                });
            }
            else
            {
                // 如果报警条件为假，则移除报警
                Application.Current.Dispatcher.Invoke(() =>
                {
                    bool blResult = RemoveAlarm(alarmMessage);
                    if (blResult)
                    {
                        _curLogDatas.Add(new LogData(EnuLogDataType.Alarm) { PosName0 = name, MessageDetail = alarmMessage, MessageStatus = "结束报警", SystemTime = DateTime.Now });
                    }
                });
            }
        }

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public TransferWaferViewModel()
        {
            WaferDragDropHandler = new WaferDragDropHandler(this);
            CurPLcsignalSimulation = new PLcsignalSimulation();

            // 初始化组件
            InitializeComponents();

            // 初始化容器关系
            InitializeContainerRelationships();

            // 初始化定时器：统计程序运行耗时时间
            InitializeTimers();

            Golbal.IpAddress = Utility.GetLocalIp("192");
        }

        /// <summary>
        /// 初始化组件（腔体、机械臂等）
        /// </summary>
        private void InitializeComponents()
        {
            // 初始化Cassette
            Cassette = new Cassette(EnuChamberName.Cassette)
            {
                Mode = EnuMode.Auto,
                WorkStatus = EnuWorkStatus.Idle,
                WarnStatus = EnuWarnStatus.NoWarn,
                TimeRemain = new TimeSpan(1, 11, 11, 11),
            };
            IniWafers();
            Cassette.LoadWafers(out _errorMsgList);
            _strMsg = $"已创建左边Wafer：{string.Join(",", Cassette.LeftWaferAction.Wafers.ToList())}\r\n已创建右边Wafer：{string.Join(",", Cassette.RightWaferAction.Wafers.ToList())}\r\n";

            Console.WriteLine(_strMsg);
            UILogService.AddLog(_strMsg);

            ChamberA = new Chamber(EnuChamberName.CHA)
            {
                Mode = EnuMode.Auto,
                SlitDoorStatus = EnuSlitDoorStatus.Close,
                WorkStatus = EnuWorkStatus.Idle,
                StepProgressPercentage = 10,
                WarnStatus = EnuWarnStatus.NoWarn,
                TimeRemain = new TimeSpan(1, 11, 11, 11),
                StepLevel = "STEP1",
                StepTimeRemain = new TimeSpan(20, 20, 1, 11),
                TextAngle = -90,
            };

            ChamberB = new Chamber(EnuChamberName.CHB)
            {
                Mode = EnuMode.Manual,
                SlitDoorStatus = EnuSlitDoorStatus.Close,
                WorkStatus = EnuWorkStatus.Idle,
                StepProgressPercentage = 50,
                WarnStatus = EnuWarnStatus.NoWarn,
                TimeRemain = new TimeSpan(1, 22, 22, 222),
                StepLevel = "STEP2",
                StepTimeRemain = new TimeSpan(20, 20, 2, 22),
                TextAngle = 0,
            };

            ChamberC = new Chamber(EnuChamberName.CHC)
            {
                Mode = EnuMode.Auto,
                SlitDoorStatus = EnuSlitDoorStatus.Close,
                WorkStatus = EnuWorkStatus.Idle,
                StepProgressPercentage = 100,
                WarnStatus = EnuWarnStatus.NoWarn,
                TimeRemain = new TimeSpan(1, 24, 33, 33),
                StepLevel = "STEP3",
                StepTimeRemain = new TimeSpan(20, 20, 3, 33),
                TextAngle = 90,
            };

            Cooling = new CoolingChamber(EnuChamberName.Cooling)
            {
                Mode = EnuMode.Auto,
                WorkStatus = EnuWorkStatus.Idle,
                WarnStatus = EnuWarnStatus.NoWarn,
            };

            LeftRobotIRArm = new RobotArmNew(EnuChamberName.RobotArmNose, 500, 1, EnuArmFetchSide.Nose, this)
            {
                WorkStatus = EnuWorkStatus.Run,
                WarnStatus = EnuWarnStatus.NoWarn,
                TextAngle = 180,
            };

            RightRobotIRArm = new RobotArmNew(EnuChamberName.RobotArmSmooth, 500, 1, EnuArmFetchSide.Smooth, this)
            {
                WorkStatus = EnuWorkStatus.Run,
                WarnStatus = EnuWarnStatus.NoWarn,
                TextAngle = 0,
            };
        }

        /// <summary>
        /// 初始化容器关系
        /// </summary>
        private void InitializeContainerRelationships()
        {
            FromChamber.Add(ChamberA);
            FromChamber.Add(ChamberB);
            FromChamber.Add(ChamberC);
            FromChamber.Add(Cassette);
            FromChamber.Add(Cooling);
            FromChamber.Add(LeftRobotIRArm);
            FromChamber.Add(RightRobotIRArm);

            ToChamber.Add(ChamberA);
            ToChamber.Add(ChamberB);
            ToChamber.Add(ChamberC);
            ToChamber.Add(Cassette);
            ToChamber.Add(Cooling);
            ToChamber.Add(LeftRobotIRArm);
            ToChamber.Add(RightRobotIRArm);

            ByArmFetchSide = Enum.GetValues(typeof(EnuArmFetchSide));
            SelectedFromChamber = Cassette;
        }

        /// <summary>
        /// 初始化定时器：统计程序运行耗时时间
        /// </summary>
        private void InitializeTimers()
        {
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += Timer_Tick;
            _timer.IsEnabled = true;
            _timer.Stop();
        }

        #endregion 构造函数

        #region 方法

        /// <summary>
        /// 获取文件配方信息
        /// </summary>
        /// <param name="rootPath">文件配方路径</param>
        /// <returns></returns>
        private List<FileRecipeInfo> GetFileRecipeInfos(string rootPath)
        {
            List<FileRecipeInfo> lstFileRecipeInfo = new List<FileRecipeInfo>();
            if (!Directory.Exists(rootPath))
            {
                MessageBox.Show($"存放配方目录{rootPath}不存在，请检查！");
                return null;
            }
            try
            {
                var files = new DirectoryInfo(rootPath).GetFiles("Recipe*.json");

                foreach (var file in files)
                {
                    var json = File.ReadAllText(file.FullName);
                    var allFlow = json.JsonToEntity<AllFlow>();
                    if (allFlow != null)
                    {
                        lstFileRecipeInfo.Add(new FileRecipeInfo(file.Name.Replace(file.Extension, ""), allFlow));
                    }
                }
            }
            catch (Exception ex)
            {
                AppLog.Error("获取文件配方信息错误", ex);
                //throw;
            }

            return lstFileRecipeInfo;
        }

        /// <summary>
        /// 获取机限
        /// </summary>
        /// <param name="filePath">文件配方路径</param>
        /// <returns></returns>
        private AllFlowMachineLimit GetMachineLimit(string filePath)
        {
            AllFlowMachineLimit allFlowMachineLimit = null;
            if (!File.Exists(filePath))
            {
                MessageBox.Show($"机限文件：{filePath}，不存在，请检查！");
                return null;
            }
            try
            {
                var json = File.ReadAllText(filePath);
                allFlowMachineLimit = json.JsonToEntity<AllFlowMachineLimit>();
            }
            catch (Exception ex)
            {
                AppLog.Error("获取机限错误", ex);
            }
            return allFlowMachineLimit;
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            Golbal.RunTotalElapsedtime = Utility.GetHourMinutesInfo(DateTime.Now.Subtract(_AppStartDateTime));
            TotalProcessTime = DateTime.Now.Subtract(_AppStartDateTime);
        }

        private bool CheckIsRunCmd()
        {
            bool isRunCmd = SelectedFromChamber != null && SelectedToChamber != null && SelectedFromSlot != null && SelectedToSlot != null;
            isRunCmd = isRunCmd && !IsRunning;//运行中不允许再次手动操作命令
            return isRunCmd;
        }

        /// <summary>
        /// 自动勾选From选项按钮
        /// </summary>
        /// <param name="container"></param>
        public void AutoSelectedFromChamber(BContainer container)
        {
            foreach (var item in FromChamber)
            {
                if (container == item && container.HasUnfinishedWafer())
                {
                    item.LeftWaferAction.IsMoveChecked = true;
                    item.RightWaferAction.IsMoveChecked = true;
                }
                else
                {
                    item.LeftWaferAction.IsMoveChecked = false;
                    item.RightWaferAction.IsMoveChecked = false;
                }
            }

            //添加SLOT左边、右边合并，去掉重复
            Application.Current.Dispatcher.Invoke(() =>
            {
                FromAvailableWafers.Clear();
                if (SelectedFromChamber != null)
                {
                    //Wafer waferLeft;
                    foreach (var waferLeft in SelectedFromChamber.LeftWaferAction.Wafers)
                    {
                        if (!waferLeft.IsFinisheded)
                        {
                            if (!FromAvailableWafers.ToList().Exists(w => w.WaferNo == waferLeft.WaferNo))
                                FromAvailableWafers.Add(waferLeft);
                        }
                    }
                    foreach (var waferRight in SelectedFromChamber.RightWaferAction.Wafers)
                    {
                        if (!waferRight.IsFinisheded)
                        {
                            if (!FromAvailableWafers.ToList().Exists(w => w.WaferNo == waferRight.WaferNo))
                            {
                                FromAvailableWafers.Add(waferRight);
                            }
                        }
                    }
                    //if (FromAvailableWafers.Count > 0)
                    //{
                    //    FromAvailableWafers.Insert(0, string.Empty);
                    //}
                }
            });
        }

        /// <summary>
        /// 自动勾选To选项按钮
        /// </summary>
        /// <param name="container"></param>
        public void AutoSelectedToChamber(BContainer container)
        {
            foreach (var item in ToChamber)
            {
                if (container == item && (!container.IsFull()))
                {
                    item.LeftWaferAction.IsReplaceChecked = true;
                    item.RightWaferAction.IsReplaceChecked = true;
                }
                else
                {
                    item.LeftWaferAction.IsReplaceChecked = false;
                    item.RightWaferAction.IsReplaceChecked = false;
                }
            }

            if (SelectedToChamber is RobotArmNew robotArm)
            {
                SelectedByArmFetchSide = robotArm.ArmFetchSide;
            }

            //添加SLOT左边、右边合并，去掉重复
            Application.Current.Dispatcher.Invoke(() =>
            {
                ToAvailableWafers.Clear();
                if (SelectedToChamber != null)
                {
                    if (SelectedFromChamber != null)
                    {
                        if (SelectedToChamber is Cassette)
                        {
                            ToAvailableWafers.AddRange(FromAvailableWafers.ToArray());
                        }
                        else
                        {
                            SelectedToSlot = SelectedFromSlot;
                            if (SelectedToSlot != null)
                            {
                                ToAvailableWafers.Add(new Wafer(EnuChamberWaferSide.LeftWafers, SelectedToSlot.Value));
                                //ToAvailableWafers.Add(new Wafer(EnuChamberWaferSide.LeftWafers, 1));
                            }
                        }
                    }

                    //Wafer waferLeft;
                    foreach (var waferLeft in SelectedToChamber.LeftWaferAction.Wafers)
                    {
                        if (!waferLeft.IsFinisheded)
                        {
                            if (!ToAvailableWafers.ToList().Exists(w => w.WaferNo == waferLeft.WaferNo))
                                ToAvailableWafers.Add(waferLeft);
                        }
                    }
                    foreach (var waferRight in SelectedToChamber.RightWaferAction.Wafers)
                    {
                        if (!waferRight.IsFinisheded)
                        {
                            if (!ToAvailableWafers.ToList().Exists(w => w.WaferNo == waferRight.WaferNo))
                            {
                                ToAvailableWafers.Add(waferRight);
                            }
                        }
                    }
                    //if (ToAvailableWafers.Count > 0)
                    //{
                    //    ToAvailableWafers.Insert(0, string.Empty);
                    //}
                }
            });
        }

        /// <summary>
        /// 后台动态判断勾选 手动模式命令选项
        /// </summary>
        public async Task MonitoringManualOperation(CancellationToken cancellationToken)
        {
            while (IsMonitoringManualOperation)
            {
                var orderByUpdateTimeByLeft = FromChamber.OrderByDescending(t => t.LeftWaferAction.UpdateTime).ToList();
                var orderByUpdateTimeByRight = FromChamber.OrderByDescending(t => t.LeftWaferAction.UpdateTime).ToList();
                var leftUpdate = orderByUpdateTimeByLeft.FirstOrDefault()!.LeftWaferAction.UpdateTime;
                var rightUpdate = orderByUpdateTimeByRight.FirstOrDefault()!.RightWaferAction.UpdateTime;

                var neworderByUpdateTime = leftUpdate > rightUpdate ? orderByUpdateTimeByLeft : orderByUpdateTimeByRight;

                bool blFromSelected = false;
                bool blToSelectd = false;
                foreach (var item in neworderByUpdateTime)
                {
                    if (item.LeftWaferAction.IsMoveChecked || item.RightWaferAction.IsMoveChecked)
                    {
                        SelectedFromChamber = item;
                        blFromSelected = true;
                    }
                    if (item.LeftWaferAction.IsReplaceChecked || item.RightWaferAction.IsReplaceChecked)
                    {
                        SelectedToChamber = item;
                        blToSelectd = true;
                    }
                }

                if (!blFromSelected)
                {
                    SelectedFromChamber = null!;
                }
                if (!blToSelectd)
                {
                    SelectedToChamber = null!;
                }

                await Task.Delay(3000, cancellationToken);
            }
        }

        /// <summary>
        /// 初始化全局Wafer容器
        /// </summary>
        private void IniWafers()
        {
            Application.Current.Dispatcher.Invoke(() =>
           {
               Golbal.CurLeftAvailableWafers.Clear();
               Golbal.CurRightAvailableWafers.Clear();

               //创建全局左边、右边Wafer 列表
               for (int id = 1; id <= WaferTotalCount; id++)
               {
                   Golbal.CurLeftAvailableWafers.Add(new Wafer(EnuChamberWaferSide.LeftWafers, id));
                   Golbal.CurRightAvailableWafers.Add(new Wafer(EnuChamberWaferSide.RightWafers, id));
               }
           });
        }

        private List<Wafer> CreateCassette(int count = 25)
        {
            List<Wafer> wafers = new List<Wafer>();
            for (int i = 1; i <= count; i++)
            {
                wafers.Add(new Wafer(EnuChamberWaferSide.LeftWafers, i));
            }

            return wafers;
        }

        #endregion 方法

        #region 命令事件处理

        /// <summary>
        /// 事件：加载窗体Loaded
        /// </summary>
        /// <param name="parameter"></param>
        [RelayCommand]
        private async Task OnExecuteLoadedWindow(object parameter)
        {
            //修复没有加载完成，导致hc:Badge报错 //未知报错 无法在"System.Windows.Controls.ControlTemplate"的名称范围内内找到"Border"名称
            //if (Golbal.IsDevDebug)
            //{
            //    IsTabItemtMainSelected = false;
            //    IsTabItemtSecsSelected = true;
            //}

            /*MyAspenProcess = new AspenProcess();

            //自动启动secsGem
            await OnSecsEnableAsync();*/

            await Task.CompletedTask;
        }

        //private DelegateCommand<string> _RunCmd = null!;

        ///// <summary>
        ///// Wafer 命令处理
        ///// </summary>
        //public DelegateCommand<string> RunCmd =>
        //    _RunCmd ?? (_RunCmd = new DelegateCommand<string>(ExecuteRunCmd));

        /// <summary>
        /// 【***不再使用***】
        /// </summary>
        /// <param name="parameter"></param>
        private async Task ExecuteRunCmd(string parameter)
        {
            switch (parameter.Trim())
            {
                case "Custom":
                    await TrasferWafer();//替换OK
                    break;

                case "Reset":
                    OnProcessReset();//替换OK
                    break;

                case "Loop":
                    await OnExecuteProcessLoop();//修改后 替换OK
                    break;

                case "AutoRunStart":
                    // OnAutoRunStart();
                    break;

                ////整机流程运行启动
                //case "ProcessAutoRunStart":
                //    IsRunAllProcessLoop = true;
                //    await ProcessAutoRunStart(parameter);
                //    break;

                //case "ProcessAutoRunStop":
                //    IsRunAllProcessLoop = false;
                //    break;

                case "Stop":
                    await OnProcessStop();//替换OK
                    break;

                case "StartLoad"://【UI未使用】
                    var blResult = ProcessStartLoad();
                    UILogService.AddLog($"加载处理工艺流程结果：{blResult}");

                    break;

                case "StopProcessLoop":
                    //OnStopProcessLoop();
                    break;

                case "Pause":
                    await OnProcessPauseNew();//替换OK
                    //ProcessPauseCommand(parameter);
                    break;

                case "RunRecipe":
                    await OnRunRecipe();
                    break;

                case "ReadPLCCurRunStatus":
                    //await OnReadPLCCurRunStatus();
                    break;

                case "ReadAndSetPLCCurRunStatus":
                    //OnReadAndSetPLCCurRunStatus();
                    break;

                case "SwitchHostMode":
                    await OnSwitchHostMode();
                    break;

                case "SwitchLoadlockMode":
                    await OnSwitchLoadlockMode();
                    break;

                case "SwitchCoolingMode":
                    OnSwitchCoolingMode();
                    break;

                case "DevTest":
                    await OnDevTest();//替换OK
                    break;

                case "AlarmConfirmed":
                    //OnAlarmConfirmed();
                    break;

                default:
                    HcGrowlExtensions.Info($"未知命令：{parameter}");
                    break;
            }
        }

        [RelayCommand]
        private Task OnSwitchHostMode()
        {
            if (Host.Mode == EnuMode.Auto)
            {
                if (!IsRunning)
                {
                    Host.Mode = EnuMode.Manual;
                }
                else
                {
                    HcGrowlExtensions.Warning("请先停止运行！");
                }
            }
            else
            {
                Host.Mode = EnuMode.Auto;
            }
            return Task.CompletedTask;
        }

        [RelayCommand]
        private Task OnSwitchLoadlockMode()
        {
            if (Loadlock.Mode == EnuMode.Auto)
            {
                Loadlock.Mode = EnuMode.Manual;
            }
            else
            {
                Loadlock.Mode = EnuMode.Auto;
            }
            return Task.CompletedTask;
        }

        [RelayCommand]
        private Task OnSwitchCoolingMode()
        {
            if (Cooling.Mode == EnuMode.Auto)
            {
                Cooling.Mode = EnuMode.Manual;
            }
            else
            {
                Cooling.Mode = EnuMode.Auto;
            }
            return Task.CompletedTask;
        }

        [RelayCommand]
        private async Task OnStopProcessLoop()
        {
            await Task.CompletedTask;
        }

        private CancellationTokenSource _autoRunCancellationTokenSource;

        /// <summary>
        /// 加载处理工艺流程
        /// </summary>
        private bool ProcessStartLoad()
        {
            if (CurRunRecipeInfo == null)
            {
                _strMsg = "当前配方为空，请先选择配方！";
                HcGrowlExtensions.Warning(_strMsg, waitTime: 30, showDateTime: true);
                UILogService.AddLog(_strMsg);
                return false;
            }

            CurRunRecipeInfo.CalculateRecipeIds();

            return true;
        }

        private async Task ProcessAutoRunStart(string parameter)
        {
            // 初始化 CancellationTokenSource
            _autoRunCancellationTokenSource = new CancellationTokenSource();

            try
            {
                // 启动一个新的任务在后台执行循环
                await RunPeriodicTask(parameter, TimeSpan.FromSeconds(1), _autoRunCancellationTokenSource.Token);
            }
            catch (OperationCanceledException)
            {
                // 处理取消操作
                Application.Current.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show("操作已取消", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
                });
            }
            catch (Exception ex)
            {
                // 捕获后台任务中的异常并在UI线程中处理
                Application.Current.Dispatcher.Invoke(() =>
                {
                    AppLog.Error("捕获后台任务中的异常并在UI线程中处理", ex);
                    MessageBox.Show($"捕获到异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                });
            }
        }

        private async Task RunPeriodicTask(string parameter, TimeSpan interval, CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                if (WholeProcessCmdTriggered.AutoRunBusy)
                {
                    IsProcessAutoRunStart = true;
                    //await RunAutoRunStart(parameter);
                    await Task.Delay(interval, cancellationToken);
                }
                else
                {
                    IsProcessAutoRunStart = false;
                    // 等待指定的时间间隔
                    await Task.Delay(TimeSpan.FromSeconds(1), cancellationToken);
                }
            }
        }

        private void CancelAutoRun()
        {
            // 请求取消操作
            _autoRunCancellationTokenSource?.Cancel();
        }

        [RelayCommand]
        private async Task OnDevTest()
        {
            try
            {
                #region InterLock数据测试

                /*
                var robotSettingValue = RobotConfigureSettings.GetSettingValue(EnuRobotConfigureSettingCodes.RPS1);
                HcGrowlExtensions.Info($"robotSettingValue={robotSettingValue}");

                var loadlockPressureOffset = ShuttleConfigureSettings.Instance.LoadlockPressureOffset;
                HcGrowlExtensions.Info($"loadlockPressureOffset={loadlockPressureOffset}");

                var alarmItem = RobotErrorCodesProvider.Instance.GetAlarmItemByCode(EnuRobotAlarmCodes.RA1);
                HcGrowlExtensions.Info($"alarmItem={alarmItem?.Code}，{alarmItem?.Content}");
                */

                // var alarms = RobotErrorCodesProvider.Instance.GetAllAlarmItems();
                // HcGrowlExtensions.Info($"当前所有报警信息：{string.Join(",", alarms.Select(t => $"{t.Code}：{t.Content}\r\n"))}");

                #endregion InterLock数据测试

                if (File.Exists(Path.Combine(Golbal.WorkRootPath, "CodeFirst.tag")))
                {
                    await RunCodeFirst();
                    return;
                }

                if (false)
                {
                    dialogHost.Show(nameof(WaferInfoDisplay), new DialogParameters("message=Hello World"), result =>
                     {
                         if (result.Result == ButtonResult.OK)
                         {
                             // 处理用户点击OK按钮的逻辑
                         }
                         else if (result.Result == ButtonResult.Cancel)
                         {
                             // 处理用户点击取消按钮的逻辑
                         }
                     });
                }

                //var dialog = new WaferInfoDisplay();
                //dialog.Show();

                //MessageBox.Show(Host.Mode.ToString());

                #region UI界面Wafer跑马灯状态测试

                //for (int i = 0; i < Cassette.LeftWaferAction.Wafers.Count; i++)
                //{
                //    Cassette.LeftWaferAction.Wafers[i].WaferStatus = Zishan.SS200.Cmd.Enums.EnuWaferStatus.AutoFinished;
                //    Cassette.RightWaferAction.Wafers[i].WaferStatus = Zishan.SS200.Cmd.Enums.EnuWaferStatus.AutoFinished;
                //    await Task.Delay(200);
                //}

                //for (int i = 0; i < Cassette.LeftWaferAction.Wafers.Count; i++)
                //{
                //    Cassette.LeftWaferAction.Wafers[i].WaferStatus = Zishan.SS200.Cmd.Enums.EnuWaferStatus.ManuFinished;
                //    Cassette.RightWaferAction.Wafers[i].WaferStatus = Zishan.SS200.Cmd.Enums.EnuWaferStatus.ManuFinished;
                //    await Task.Delay(200);
                //}

                //for (int i = 0; i < Cassette.LeftWaferAction.Wafers.Count; i++)
                //{
                //    Cassette.LeftWaferAction.Wafers[i].WaferStatus = Zishan.SS200.Cmd.Enums.EnuWaferStatus.Others;
                //    Cassette.RightWaferAction.Wafers[i].WaferStatus = Zishan.SS200.Cmd.Enums.EnuWaferStatus.Others;
                //    await Task.Delay(200);
                //}

                //for (int i = 0; i < Cassette.LeftWaferAction.Wafers.Count; i++)
                //{
                //    Cassette.LeftWaferAction.Wafers[i].WaferStatus = Zishan.SS200.Cmd.Enums.EnuWaferStatus.Have;
                //    Cassette.RightWaferAction.Wafers[i].WaferStatus = Zishan.SS200.Cmd.Enums.EnuWaferStatus.Have;
                //    await Task.Delay(200);
                //}

                #endregion UI界面Wafer跑马灯状态测试

                //Cooling.ProcessTimer = TimeSpan.FromSeconds(90);
                // Cooling.ProcessTimer=TimeSpan.Zero;

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _strMsg = $"OnDevTest 开发测试失败,Message：{ex.Message}";
                AppLog.Error(_strMsg, ex);
                MessageBox.Show(_strMsg);
            }
        }

        /// <summary>
        /// CodeFirst生成表，开发测试使用
        /// </summary>
        private Task RunCodeFirst()
        {
            List<Type> lstType = new List<Type>();
            if (lstType == null) throw new ArgumentNullException(nameof(lstType));

            //lstType.Add(typeof(RobotParameter));
            //lstType.Add(typeof(RobotCommand));
            //lstType.Add(typeof(AxisAfterRunCmd));

            #region 基础表

            //lstType.Add(typeof(Base_User));
            //lstType.Add(typeof(Base_Role));
            //lstType.Add(typeof(Base_UserRole));
            //lstType.Add(typeof(Base_Action));
            //lstType.Add(typeof(Base_RoleAction));

            //lstType.Add(typeof(Base_LogAlarm));
            //lstType.Add(typeof(Base_LogData));
            //lstType.Add(typeof(Base_LogOperating));
            //lstType.Add(typeof(Base_LogSystem));
            //lstType.Add(typeof(Base_LogVisit));

            //lstType.Add(typeof(LogData));

            #endregion 基础表

            //lstType.Add(typeof(Test_SensorData));
            //lstType.Add(typeof(RecipeSeqInfo));

            //lstType.Add(typeof(RecipeInfoHistory));
            //lstType.Add(typeof(ArmFetchProcesHistory));

            //lstType.Add(typeof(DataMfcRf));
            var strTableName = string.Join(",", lstType.Select(t => t.Name));

            if (lstType.Count > 0)
            {
                var dialogResult = MessageBox.Show($"是否要执行：{strTableName}，这{lstType.Count}个实体生成表,因CodeFirst无法创建可空字段，执行后请执行变更某些字段为可空类型", "CodeFirst友情提示", MessageBoxButton.YesNo, MessageBoxImage.Question, MessageBoxResult.No);
                if (dialogResult == MessageBoxResult.Yes)
                {
                    //_dBAccessService.CodeFirstInitTables(lstType.ToArray());
                    MessageBox.Show($"{strTableName}，这{lstType.Count}张表已经生成表，因CodeFirst无法创建可空字段，已执行，请在数据库管理系统中执行变更某些字段为可空类型，谢谢！");
                }
            }
            else
            {
                MessageBox.Show("没有实体CodeFirst", "CodeFirst友情提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            return Task.CompletedTask;
        }

        #region 处理Robot自定义搬运命令，比如：从Cassette容器中获取一个Wafer放置到容器A ToDo：改为调用 RobotArmNew.TransferWafer

        /// <summary>
        /// 处理Robot自定义搬运命令，比如：从Cassette容器中获取一个Wafer放置到容器A ToDo：改为调用 RobotArmNew.TransferWafer
        /// </summary>
        [RelayCommand]
        private async Task TrasferWafer()
        {
            _AppStartDateTime = DateTime.Now;
            _timer.Start();

            IsCmdRunning = true;

            int slotFromValue = SelectedFromSlot ?? -1;
            int slotToValue = SelectedToSlot ?? -1;
            if (slotFromValue == -1 || slotToValue == -1)
            {
                HcGrowlExtensions.Warning("slot不能为空");
                return;
            }

            try
            {
                // 根据选择的机械臂端口类型选择对应的机械臂
                RobotArmNew robotArm = SelectedByArmFetchSide == EnuArmFetchSide.Nose ? LeftRobotIRArm : RightRobotIRArm;

                // 设置全局CurGolbalRunRecipeInfo，确保HasUnfinishedWafer方法能正确工作
                Golbal.CurGolbalRunRecipeInfo = CurRunRecipeInfo;

                bool result;

                // 判断是否是腔体到腔体的搬运（源和目标都不是机械臂）
                bool fromIsRobotArm = SelectedFromChamber is RobotArmNew;
                bool toIsRobotArm = SelectedToChamber is RobotArmNew;

                if (!fromIsRobotArm && !toIsRobotArm)
                {
                    UILogService.AddLog($"腔体到腔体的搬运必须经过机械臂，执行两步搬运：{SelectedFromChamber.ChamberName}->机械臂->{SelectedToChamber.ChamberName}");

                    // 第一步：从源腔体到机械臂
                    UILogService.AddLog($"第一步：从{SelectedFromChamber.ChamberName}(Slot:{slotFromValue})到机械臂");
                    result = await robotArm.TransferWafer(
                        SelectedFromChamber,
                        slotFromValue,
                        robotArm,  // 目标是机械臂自身
                        1,         // 机械臂槽位默认为1
                        Cassette,  // 传递Cassette参数
                        SelectedByArmFetchSide);

                    if (!result)
                    {
                        UILogService.AddErrorLog($"晶圆搬运失败：从{SelectedFromChamber.ChamberName}到机械臂失败");
                        ExcuteResult = false;
                        return;
                    }

                    // 添加延时，让UI界面能够更新并显示中间状态
                    UILogService.AddLog($"晶圆已从{SelectedFromChamber.ChamberName}搬运到机械臂，等待继续...");
                    await Task.Delay(1500); // 添加1.5秒延时

                    // 第二步：从机械臂到目标腔体
                    UILogService.AddLog($"第二步：从机械臂到{SelectedToChamber.ChamberName}(Slot:{slotToValue})");
                    result = await robotArm.TransferWafer(
                        robotArm,  // 源是机械臂自身
                        1,         // 机械臂槽位默认为1
                        SelectedToChamber,
                        slotToValue,
                        Cassette,  // 传递Cassette参数
                        SelectedByArmFetchSide);

                    if (result)
                    {
                        UILogService.AddSuccessLog($"晶圆搬运成功: 从{SelectedFromChamber.ChamberName}(Slot:{slotFromValue})经过机械臂到{SelectedToChamber.ChamberName}(Slot:{slotToValue})");
                        ExcuteResult = true;
                    }
                    else
                    {
                        UILogService.AddErrorLog($"晶圆搬运失败：从机械臂到{SelectedToChamber.ChamberName}失败");
                        ExcuteResult = false;
                    }
                }
                else
                {
                    // 如果源或目标中有一个是机械臂，则直接执行当前的搬运逻辑
                    // 调用RobotArmNew的TransferWafer方法执行晶圆搬运
                    result = await robotArm.TransferWafer(
                        SelectedFromChamber,
                        slotFromValue,
                        SelectedToChamber,
                        slotToValue,
                        Cassette, // 传递Cassette参数
                        SelectedByArmFetchSide);

                    if (result)
                    {
                        UILogService.AddSuccessLog($"晶圆搬运成功: 从{SelectedFromChamber.ChamberName}(Slot:{slotFromValue})到{SelectedToChamber.ChamberName}(Slot:{slotToValue})");
                        ExcuteResult = true;
                    }
                    else
                    {
                        UILogService.AddErrorLog($"晶圆搬运失败");
                        ExcuteResult = false;
                    }
                }
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"晶圆搬运异常: {ex.Message}");
                ExcuteResult = false;
            }

            // 执行完SLOT刷新
            SelectedFromSlot = null;
            SelectedToSlot = null;

            SelectedFromChamber = SelectedFromChamber;
            SelectedToChamber = SelectedToChamber;

            _timer.Stop();
            IsCmdRunning = false;
        }

        /// <summary>
        /// 判断是否可以执行晶圆搬运命令
        /// </summary>
        /// <returns>是否可以执行</returns>
        private bool CanTrasferWafer()
        {
            return !IsCmdRunning && SelectedFromChamber != null && SelectedToChamber != null;
        }

        #endregion 处理Robot自定义搬运命令，比如：从Cassette容器中获取一个Wafer放置到容器A ToDo：改为调用 RobotArmNew.TransferWafer

        /// <summary>
        /// 处理流程状态重置初始状态
        /// </summary>
        [RelayCommand]
        private Task OnProcessReset()
        {
            // Application.Current.Dispatcher.Invoke(() =>
            //  {
            //      LogList.Clear();
            //  });

            foreach (var item in FromChamber)
            {
                item.ClearWafer();
            }

            IniWafers();
            Cassette.LoadWafers(out _errorMsgList);
            _strMsg = $"已创建左边Wafer：{string.Join(",", Cassette.LeftWaferAction.Wafers.ToList())}\r\n已创建右边Wafer：{string.Join(",", Cassette.RightWaferAction.Wafers.ToList())}\r\n";
            Console.WriteLine(_strMsg);
            UILogService.AddLog(_strMsg);

            //执行完SLOT刷新
            SelectedFromSlot = null;
            SelectedToSlot = null;

            SelectedFromChamber = SelectedFromChamber;
            SelectedToChamber = SelectedToChamber;

            RequestCmd = string.Empty;
            ResponseCmd = string.Empty;

            //WaferTotalCount = 6;
            IsRobotReset = !IsRobotReset;//重置Robot

            //重置状态指示灯
            ChamberA.WorkStatus = EnuWorkStatus.Idle;
            ChamberA.StopTimer();
            ChamberA.ProcessTimer = TimeSpan.Zero;
            ChamberB.WorkStatus = EnuWorkStatus.Idle;
            ChamberB.StopTimer();
            ChamberB.ProcessTimer = TimeSpan.Zero;
            ChamberC.WorkStatus = EnuWorkStatus.Idle;
            ChamberC.StopTimer();
            ChamberC.ProcessTimer = TimeSpan.Zero;
            Cooling.WorkStatus = EnuWorkStatus.Idle;

            Cooling.StopTimer();
            Cooling.ProcessTimer = TimeSpan.Zero;
            Cassette.WorkStatus = EnuWorkStatus.Idle;//Loadlock replace by Cassette

            //重置SlitDoor门开关状态
            Buffer.ChaSlitDoorStatus = EnuSlitDoorStatus.Close;
            Buffer.ChbSlitDoorStatus = EnuSlitDoorStatus.Close;
            Buffer.ChcSlitDoorStatus = EnuSlitDoorStatus.Close;
            Buffer.LoadLocklitDoorStatus = EnuSlitDoorStatus.Close;
            Buffer.WorkStatus = EnuWorkStatus.Idle;

            Loadlock.SlitDoorStatus = EnuSlitDoorStatus.Close;

            //PLC信号模拟 重置
            CurPLcsignalSimulation.Reset();

            return Task.CompletedTask;
        }

        /// <summary>
        /// 弹出跑配方流程窗口
        /// </summary>
        /// <returns></returns>
        [RelayCommand]
        private async Task OnRunRecipe()
        {
            if (Host.Mode == EnuMode.Auto)
            {
                var runRecipeFinished = await OpenJustRunRecipeView();
                //await OpenRunRecipeView();//以前能跑的Sequence
            }
            else
            {
                HcGrowlExtensions.Warning("请先切换到自动模式！");
            }
        }

        /// <summary>
        /// 根据Sequence跑配方流程
        /// </summary>
        [RelayCommand]
        private async Task OnExecuteProcessLoop()
        {
            if (!string.IsNullOrWhiteSpace(CurSelectedRecipe))
            {
                //构建配方信息
                CurRunRecipeInfo = new DtoRunRecipeInfo()
                {
                    LeftBarcode = CurRunRecipeInfo.LeftBarcode,
                    RightBarcode = CurRunRecipeInfo.RightBarcode,
                    Top = CurRunRecipeInfo.Top,
                    Bottom = CurRunRecipeInfo.Bottom,
                    RecipeName = CurSelectedRecipe
                };
                Golbal.CurGolbalRunRecipeInfo = CurRunRecipeInfo;
                var result = await ProcessLoopCommand();
            }
            else
            {
                HcGrowlExtensions.Warning("请选择配方！");
            }
        }

        /// <summary>
        /// 循环跑工序流程
        /// </summary>
        private async Task<bool> ProcessLoopCommand()
        {
            bool blFinishedOKResult = false;
            try
            {
                /*
                if (!WholeProcessCmdTriggered.StartSequenceSingal)
                {
                    _strMsg = "PLC 启动运行Sequence Busy信号未启用，无法运行Sequence";
                    UILogService.AddLog(_strMsg);
                    HcGrowlExtensions.Warning(_strMsg);
                    return blFinishedOKResult;
                }
                */

                _AppStartDateTime = DateTime.Now;
                _timer.Start();
                //Task.Run(() =>
                //{
                //    while (true)
                //    {
                //    }
                //});
                //Golbal.IsDevDebug = File.Exists(Path.Combine("C:\\IR400", "debug.tag"));

                //List<string> cmdList = new List<string>();
                //if (cmdList == null) throw new ArgumentNullException(nameof(cmdList));

                IsStopLoopRunning = false;
                IsReset = false;
                IsRunCmd = false;
                _isStopFlag = false;

                int loopRealCount = 0;
                if (LoopCount >= 0)
                {
                    loopRealCount = LoopCount;
                }
                else if (LoopCount == -1)
                {
                    loopRealCount = int.MaxValue;
                }
                else
                {
                    loopRealCount = 0;
                    HcGrowlExtensions.Warning("请设置正确的循环次数");
                }

                _ = Task.Run(async () =>
                {
                    CurPLcsignalSimulation.IsLoopCheckAutoClick = false;
                    CtsPathPlanning ??= new CancellationTokenSource();
                    await Task.Delay(2000, CtsPathPlanning.Token);//等待PLC上次自动点击腔体工艺完成

                    List<string> lstCHs = new List<string>();
                    if (CurRunRecipeInfo.RecipeName.Contains("A"))
                    {
                        lstCHs.Add("CHA");
                    }
                    if (CurRunRecipeInfo.RecipeName.Contains("B"))
                    {
                        lstCHs.Add("CHB");
                    }
                    if (CurRunRecipeInfo.RecipeName.Contains("C"))
                    {
                        lstCHs.Add("CHC");
                    }
                    await CurPLcsignalSimulation.AutoClickProcessFinished(new Queue<string>(lstCHs));
                }, Golbal.CtsAppExit.Token);

                if (loopRealCount > 0)
                {
                    IsEnablePauseCmd = true;
                    CtsPathPlanning = new CancellationTokenSource();
                    //CtsRobotCmdTimeOut = new CancellationTokenSource(TimeSpan.FromSeconds(Golbal.IsDevDebug ? Golbal.DebugCommandRunTimeout : Golbal.CommandRunTimeout)); // 设置超时时间为120秒
                    IsRunning = true;
                    //记录循环次数
                    _strMsg = $"开始循环执行工艺流程，循环次数：{loopRealCount}";
                    UILogService.AddLog(_strMsg);
                }

                //执行前，保存配方信息

                RecipeInfoHistory recipeInfoHistory = new RecipeInfoHistory();
                recipeInfoHistory.LeftBarcode = CurRunRecipeInfo.LeftBarcode;
                recipeInfoHistory.RightBarcode = CurRunRecipeInfo.RightBarcode;
                recipeInfoHistory.RecipeName = CurRunRecipeInfo.RecipeName;
                recipeInfoHistory.Top = CurRunRecipeInfo.Top;
                recipeInfoHistory.Bottom = CurRunRecipeInfo.Bottom;
                recipeInfoHistory.LeftLotActualCount = CurRunRecipeInfo.LeftRecipeIds.Count(t => t == EnuWaferMappingStatus.Exist);
                recipeInfoHistory.RightLotActualCount = CurRunRecipeInfo.RightRecipeIds.Count(t => t == EnuWaferMappingStatus.Exist);
                recipeInfoHistory.LeftRecipeIds = CurRunRecipeInfo.LeftRecipeIds;
                recipeInfoHistory.RightRecipeIds = CurRunRecipeInfo.RightRecipeIds;
                //_CurRunRecipeInfoId = _dBAccessService.AddRecipeInfoHistory(recipeInfoHistory);

                for (int count = 0; count < loopRealCount; count++)
                {
                    if (count > 0)
                    {
                        OnProcessReset();
                    }
                    blFinishedOKResult = false;
                    Cassette.WorkStatus = EnuWorkStatus.Run;
                    Buffer.WorkStatus = EnuWorkStatus.Busy;
                    //Sequence 方式1：Cassette->CH-Cooling&Cassette

                    #region 方式1：Cassette->CH->Cooling->Cassette 开发测试

                    //CurPLcsignalSimulation.Reset();

                    List<int> listSlot = this.Cassette.GetUnFinishededWaferNos().Where(t => t >= CurRunRecipeInfo.Bottom && t <= CurRunRecipeInfo.Top).ToList();

                    CurRunRecipeInfo = CurRunRecipeInfo;

                    //List<int> listSlot = Enumerable.Range(1, WaferTotalCount).ToList();

                    // 取出左边右边边都是空的Slot，并且去除及跳过
                    var fullEpmtySlots = CurRunRecipeInfo.GetFullEmptySlots();
                    foreach (var item in fullEpmtySlots)
                    {
                        if (listSlot.Contains(item))
                        {
                            listSlot.Remove(item);
                        }
                    }

                    Queue<int> queueWaferSlot = new Queue<int>(listSlot);

                    var chaHasWafer = ChamberA.GetUnFinishededWaferNos().Count > 0;
                    var slotCha = chaHasWafer ? ChamberA.GetUnFinishededWaferNos().FirstOrDefault() : 0;

                    var chbHasWafer = ChamberB.GetUnFinishededWaferNos().Count > 0;
                    var slotChb = chbHasWafer ? ChamberB.GetUnFinishededWaferNos().FirstOrDefault() : 0;

                    var chcHasWafer = ChamberC.GetUnFinishededWaferNos().Count > 0;
                    var slotChc = chcHasWafer ? ChamberC.GetUnFinishededWaferNos().FirstOrDefault() : 0;

                    var coolingHasWafer = Cooling.GetUnFinishededWaferNos().Count > 0;
                    var slotCooling = coolingHasWafer ? Cooling.GetUnFinishededWaferNos().FirstOrDefault() : 0;

                    var robotNorthHaseWafer = LeftRobotIRArm.GetUnFinishededWaferNos().Count > 0;
                    var slotNorth = robotNorthHaseWafer ? LeftRobotIRArm.GetUnFinishededWaferNos().FirstOrDefault() : 0;

                    var robotSmothHaseWafer = RightRobotIRArm.GetUnFinishededWaferNos().Count > 0;
                    var slotSmooth = robotSmothHaseWafer ? RightRobotIRArm.GetUnFinishededWaferNos().FirstOrDefault() : 0;

                    CurPLcsignalSimulation.SetCurrentUIWaferProcess(chaHasWafer, slotCha, chbHasWafer, slotChb, chcHasWafer, slotChc, coolingHasWafer, slotCooling, robotNorthHaseWafer, slotNorth, robotSmothHaseWafer, slotSmooth);//检查设置界面上是否有剩余Wafer工艺未完成

                    var token = CtsPathPlanning.Token;

                    Loadlock.IsOpen = true;
                    await Task.Delay(1000);
                    Loadlock.IsOpen = false;

                    //一开始清空发送命令内容，防止有StopA命令残留
                    //var bResult = _adsHelper.Write(WaferDropDetectionPLCCmdConstants.StopACmdContentDetection, string.Empty);
                    var bResult = true;
                    if (!bResult)
                    {
                        AppLog.Error("清空StopA命令失败");
                        HcGrowlExtensions.Warning("清空StopA命令失败");
                        return false;
                    }
                    await Task.Delay(1000);

                    ArmFetchHistories.Clear();
                    //_processHistorys.Clear();
                    while ((queueWaferSlot.Count > 0 || CurPLcsignalSimulation.CheckIsRemainWaferProcess(CurRunRecipeInfo.RecipeName)))
                    {
                        try
                        {
                            CurPLcsignalSimulation.CheckCanProcessFinished(true);
                            var curSlot = 0;

                            if (CurPLcsignalSimulation.SlotCooling > 0)
                            {
                                if (Golbal.IsDevDebug || CurPLcsignalSimulation.CoolingProcessFinished)
                                {
                                    SelectedFromChamber.StopTimer();

                                    if (Golbal.IsDevDebug)
                                    {
                                        ArmFetchHistories[CurPLcsignalSimulation.SlotCooling].ProcessEndTime = DateTime.Now;
                                        //_dBAccessService.AddArmFetchProcesHistory(_armFetchHistories[CurPLcsignalSimulation.SlotCooling]);
                                        CurPLcsignalSimulation.CoolingProcessFinishedTrigger = false;
                                    }

                                    curSlot = CurPLcsignalSimulation.SlotCooling;
                                    _strMsg = $"SLOT:{curSlot},Cooling->Cassette 发送指令";
                                    AppLog.Info(_strMsg);
                                    Console.WriteLine(_strMsg);

                                    //currentLogIndex = 1;//日志索引从1开始
                                    SelectedFromChamber = Cooling;
                                    SelectedToChamber = Cassette;
                                    Loadlock.SlitDoorStatus = EnuSlitDoorStatus.Open;

                                    ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Smooth);//out _strMsg
                                    if (!ExcuteResult)
                                        return false;
                                    SelectedFromChamber.WorkStatus = EnuWorkStatus.Idle;
                                    Loadlock.SlitDoorStatus = EnuSlitDoorStatus.Close;

                                    CurPLcsignalSimulation.CoolingHasWafer = false;
                                    CurPLcsignalSimulation.SlotCooling = 0;

                                    CurPLcsignalSimulation.RobotSmothHaseWafer = false;
                                }
                                else
                                {
                                    _strMsg = "Cooling工艺未完成，等待Cooling工艺完成";
                                    UILogService.AddLog(_strMsg);
                                    await Task.Delay(1000, token);
                                }
                            }
                            else if (!CurPLcsignalSimulation.RobotNorthHaseWafer && CurPLcsignalSimulation.RobotSmothHaseWafer)//Nose面没有,只有Smooth面有Wafer 放回到Cassette
                            {
                                /*if (!Golbal.IsDevDebug)
                                {*/
                                //冷板加载启动处理流程【发送指令前加载冷板配方】
                                /*_adsHelper.Write(CollingProcessPLCCmdConstants.UnitRecipeName_Pre_RecipeDefinitionName, CollingProcessPLCCmdConstants.RecipeTypeCooling);
                                _adsHelper.Write(CollingProcessPLCCmdConstants.UnitRecipeName_Pre_RecipeName, CurDbRecipeSeqInfo.CoolingRecipeName);
                                _adsHelper.Write(CollingProcessPLCCmdConstants.LoadRecipe_B_Start, true);
                                _adsHelper.Write(CollingProcessPLCCmdConstants.LoadRecipe_D_Done, false);//清空 加载配方_完成标志位*/
                                _strMsg = $"Cooling加载配方： 【{CurDbRecipeSeqInfo.CoolingRecipeName}】启动命令完成";
                                UILogService.AddLog(_strMsg);
                                await Task.Delay(1000);

                                /*bool isRecipeLoadOk = await CheckIsTrueAsync(CollingProcessPLCCmdConstants.LoadRecipe_D_Done, TimeSpan.FromSeconds(10), message: $"加载配方：{CurDbRecipeSeqInfo.CoolingRecipeName}，完成标志位检测失败，请确认后重试！", title: "加载配方检测失败", Golbal.IsDevDebug ? Visibility.Visible : Visibility.Collapsed);
                                if (!isRecipeLoadOk)
                                {
                                    _strMsg = "加载配方检测失败，无法执行下去";
                                    UILogService.AddLog(_strMsg);
                                    MessageBox.Show(_strMsg, "加载配方检测失败，无法执行下去", MessageBoxButton.OK, MessageBoxImage.Error);
                                    return false;
                                }*/
                                /*}*/

                                curSlot = CurPLcsignalSimulation.SlotSmooth;
                                _strMsg = $"SLOT:{curSlot},Smooth->Cooling 发送指令";
                                AppLog.Info(_strMsg);
                                Console.WriteLine(_strMsg);

                                //currentLogIndex = 1;//日志索引从1开始
                                SelectedFromChamber = RightRobotIRArm;
                                SelectedToChamber = Cooling;
                                ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Smooth);//out _strMsg
                                if (!ExcuteResult)
                                    return false;

                                /*if (!Golbal.IsDevDebug)
                                {*/
                                //3、运行单元流程_启动按钮
                                //GVL.CP_ComIO.Process.B_Start
                                //GVL.CP_ComIO.Process.D_Done 也清一下

                                /*_adsHelper.Write(CollingProcessPLCCmdConstants.Process_B_Start, true);
                                _adsHelper.Write(CollingProcessPLCCmdConstants.Process_D_Done, false);//清空 单元流程_完成标志位，后台自动检测流程是否完成*/
                                CurPLcsignalSimulation.CoolingProcessFinished = false;//先写，再读取最新值，防止读取到旧值，后面再判断处理

                                _strMsg = "冷板工艺流程启动命令完成";
                                UILogService.AddLog(_strMsg);
                                /*}*/

                                //记录做工艺历史记录
                                ArmFetchHistories[curSlot].ClearFetchInfor();
                                ArmFetchHistories[curSlot].ProcessChamberType = "CP";
                                ArmFetchHistories[curSlot].ProcessStartTime = DateTime.Now;
                                ArmFetchHistories[curSlot].ProcessEndTime = null;

                                SelectedToChamber.StartTimer();
                                SelectedToChamber.WorkStatus = EnuWorkStatus.Process;

                                CurPLcsignalSimulation.SlotCooling = curSlot;
                                CurPLcsignalSimulation.CoolingHasWafer = true;
                                CurPLcsignalSimulation.RobotNorthHaseWafer = false;
                                CurPLcsignalSimulation.SlotSmooth = 0;

                                //await Task.Delay(1000, token);

                                //slot = CurPLcsignalSimulation.SlotCooling;
                                //_strMsg = $"SLOT:{slot},Cooling->Cassette 发送指令";
                                //AppLog.Info(_strMsg);
                                //Console.WriteLine(_strMsg);

                                //currentLogIndex = 1;//日志索引从1开始
                                //SelectedFromChamber = Cooling;
                                //SelectedToChamber = Cassette;
                                //ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, slot, SelectedToChamber, slot, Cassette, EnuArmFetchSide.Smooth);//out _strMsg
                                //if (!ExcuteResult) return;

                                //CurPLcsignalSimulation.CoolingHasWafer = false;
                                //CurPLcsignalSimulation.SlotCooling = 0;

                                //CurPLcsignalSimulation.RobotSmothHaseWafer = false;
                            }
                            else if (CurPLcsignalSimulation.RobotNorthHaseWafer) //只有Nose面有Wafer  CH没有发送到Cha、Chb、Chc, CH有完成工艺发送到Smooth端
                            {
                                if (CurRunRecipeInfo.RecipeName.Contains("A") && CurPLcsignalSimulation.RobotNorthHaseWafer)
                                {
                                    if (!CurPLcsignalSimulation.ChaHasWafer && CurPLcsignalSimulation.RobotNorthHaseWafer)//Cha没有Wafer, Nose->Cha 发送指令
                                    {
                                        /*if (!Golbal.IsDevDebug)
                                        {*/
                                        //CH腔体加载启动处理流程【发送指令前加载冷板配方】
                                        /*_adsHelper.Write(CHAProcessPLCCmdConstants.UnitRecipeName_Pre_RecipeDefinitionName, CHAProcessPLCCmdConstants.RecipeTypeCH);
                                        _adsHelper.Write(CHAProcessPLCCmdConstants.UnitRecipeName_Pre_RecipeName, CurDbRecipeSeqInfo.ChRecipeName);
                                        _adsHelper.Write(CHAProcessPLCCmdConstants.LoadRecipe_B_Start, true);
                                        _adsHelper.Write(CHAProcessPLCCmdConstants.LoadRecipe_D_Done, false);//清空 加载配方_完成标志位*/
                                        _strMsg = $"CH加载配方： 【{CurDbRecipeSeqInfo.ChRecipeName}】启动命令完成";
                                        UILogService.AddLog(_strMsg);
                                        await Task.Delay(1000);

                                        /*bool isRecipeLoadOk = await CheckIsTrueAsync(CHAProcessPLCCmdConstants.LoadRecipe_D_Done, TimeSpan.FromSeconds(10), message: $"加载配方：{CurDbRecipeSeqInfo.ChRecipeName}，完成标志位检测失败，请确认后重试！", title: "加载配方检测失败", Golbal.IsDevDebug ? Visibility.Visible : Visibility.Collapsed);
                                        if (!isRecipeLoadOk)
                                        {
                                            _strMsg = "加载配方检测失败，无法执行下去";
                                            UILogService.AddLog(_strMsg);
                                            MessageBox.Show(_strMsg, "加载配方检测失败，无法执行下去", MessageBoxButton.OK, MessageBoxImage.Error);
                                            return false;
                                        }*/
                                        /*}*/

                                        curSlot = CurPLcsignalSimulation.SlotNorth;
                                        _strMsg = $"SLOT:{curSlot},Nose->Cha 发送指令";
                                        AppLog.Info(_strMsg);
                                        Console.WriteLine(_strMsg);

                                        //currentLogIndex = 1;//日志索引从1开始
                                        SelectedFromChamber = LeftRobotIRArm;
                                        SelectedToChamber = ChamberA;
                                        Buffer.ChaSlitDoorStatus = EnuSlitDoorStatus.Open;
                                        ExcuteResult = await LeftRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Nose);//out _strMsg
                                        if (!ExcuteResult)
                                            return false;
                                        /*if (!Golbal.IsDevDebug)
                                        {*/
                                        //3、运行单元流程_启动按钮
                                        //GVL.Chamber_A_ComIO.Process.B_Start
                                        //GVL.Chamber_A_ComIO.Process.D_Done 也清一下

                                        // IR400 还没有实现，下面暂时注释掉，OK后后台要监控ChaProcessFinished流程是否完成
                                        /*_adsHelper.Write(CHAProcessPLCCmdConstants.Process_B_Start, true);
                                        _adsHelper.Write(CHAProcessPLCCmdConstants.Process_D_Done, false);//清空 单元流程_完成标志位，后台自动检测流程是否完成*/
                                        CurPLcsignalSimulation.ChaProcessFinished = false;//先写，再读取最新值，防止读取到旧值，后面再判断处理

                                        _strMsg = "CHA工艺流程启动命令完成";
                                        UILogService.AddLog(_strMsg);
                                        /*}*/

                                        //记录做工艺历史记录
                                        ArmFetchHistories[curSlot].ClearFetchInfor();
                                        ArmFetchHistories[curSlot].ProcessChamberType = "CH";
                                        ArmFetchHistories[curSlot].ProcessStartTime = DateTime.Now;
                                        ArmFetchHistories[curSlot].ProcessEndTime = null;

                                        SelectedToChamber.StartTimer();
                                        SelectedToChamber.WorkStatus = EnuWorkStatus.Process;

                                        Buffer.ChaSlitDoorStatus = EnuSlitDoorStatus.Close;
                                        CurPLcsignalSimulation.SlotCha = curSlot;
                                        CurPLcsignalSimulation.SlotNorth = 0;
                                        CurPLcsignalSimulation.RobotNorthHaseWafer = false;
                                        CurPLcsignalSimulation.ChaHasWafer = true;
                                        CurPLcsignalSimulation.ChaProcessFinished = false;//先写，再读取最新值，防止读取到旧值，后面再判断处理
                                        CurPLcsignalSimulation.ChaHasWafer = true;
                                    }
                                    else if (!CurPLcsignalSimulation.CoolingHasWafer && (CurPLcsignalSimulation.ChaProcessFinished ?? false) && RightRobotIRArm.IsEmpty() && CurPLcsignalSimulation.GetFirstFinishedChamberName(CurRunRecipeInfo.RecipeName) == EnuChamberName.CHA) //cha有Wafer且Cooling没有Wafer以及Robot Smooth端为空,防止冲突, Cha->Smooth 发送指令
                                    {
                                        SelectedFromChamber.StopTimer();
                                        curSlot = CurPLcsignalSimulation.SlotCha;
                                        _strMsg = $"SLOT:{curSlot},Cha->Smooth 发送指令";
                                        AppLog.Info(_strMsg);
                                        Console.WriteLine(_strMsg);

                                        //currentLogIndex = 1;//日志索引从1开始
                                        SelectedFromChamber = ChamberA;
                                        SelectedToChamber = RightRobotIRArm;
                                        Buffer.ChaSlitDoorStatus = EnuSlitDoorStatus.Open;
                                        ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Smooth);//out _strMsg
                                        if (!ExcuteResult)
                                            return false;
                                        SelectedFromChamber.WorkStatus = EnuWorkStatus.Idle;
                                        Buffer.ChaSlitDoorStatus = EnuSlitDoorStatus.Close;
                                        CurPLcsignalSimulation.SlotCha = 0;
                                        CurPLcsignalSimulation.ChaHasWafer = false;
                                        CurPLcsignalSimulation.ChaProcessFinished = null;//回到初始状态

                                        CurPLcsignalSimulation.RobotSmothHaseWafer = true;
                                        CurPLcsignalSimulation.SlotSmooth = curSlot;
                                    }
                                }
                                if (CurRunRecipeInfo.RecipeName.Contains("B") && CurPLcsignalSimulation.RobotNorthHaseWafer)
                                {
                                    if (!CurPLcsignalSimulation.ChbHasWafer)//Chb没有Wafer, Nose->Chb 发送指令
                                    {
                                        /*if (!Golbal.IsDevDebug)
                                        {*/
                                        //CH腔体加载启动处理流程【发送指令前加载冷板配方】
                                        /*_adsHelper.Write(CHBProcessPLCCmdConstants.UnitRecipeName_Pre_RecipeDefinitionName, CHBProcessPLCCmdConstants.RecipeTypeCH);
                                        _adsHelper.Write(CHBProcessPLCCmdConstants.UnitRecipeName_Pre_RecipeName, CurDbRecipeSeqInfo.ChRecipeName);
                                        _adsHelper.Write(CHBProcessPLCCmdConstants.LoadRecipe_B_Start, true);
                                        _adsHelper.Write(CHBProcessPLCCmdConstants.LoadRecipe_D_Done, false);//清空 加载配方_完成标志位*/
                                        _strMsg = $"CH加载配方： 【{CurDbRecipeSeqInfo.ChRecipeName}】启动命令完成";
                                        UILogService.AddLog(_strMsg);
                                        await Task.Delay(1000);

                                        /*bool isRecipeLoadOk = await CheckIsTrueAsync(CHBProcessPLCCmdConstants.LoadRecipe_D_Done, TimeSpan.FromSeconds(10), message: $"加载配方：{CurDbRecipeSeqInfo.ChRecipeName}，完成标志位检测失败，请确认后重试！", title: "加载配方检测失败", Golbal.IsDevDebug ? Visibility.Visible : Visibility.Collapsed);
                                        if (!isRecipeLoadOk)
                                        {
                                            _strMsg = "加载配方检测失败，无法执行下去";
                                            UILogService.AddLog(_strMsg);
                                            MessageBox.Show(_strMsg, "加载配方检测失败，无法执行下去", MessageBoxButton.OK, MessageBoxImage.Error);
                                            return false;
                                        }*/
                                        /*}*/

                                        curSlot = CurPLcsignalSimulation.SlotNorth;
                                        _strMsg = $"SLOT:{curSlot},Nose->Chb 发送指令";
                                        AppLog.Info(_strMsg);
                                        Console.WriteLine(_strMsg);

                                        //currentLogIndex = 1;//日志索引从1开始
                                        SelectedFromChamber = LeftRobotIRArm;
                                        SelectedToChamber = ChamberB;
                                        Buffer.ChbSlitDoorStatus = EnuSlitDoorStatus.Open;
                                        ExcuteResult = await LeftRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Nose);//out _strMsg
                                        if (!ExcuteResult)
                                            return false;
                                        /*if (!Golbal.IsDevDebug)
                                        {*/
                                        //3、运行单元流程_启动按钮
                                        //GVL.Chbmber_A_ComIO.Process.B_Start
                                        //GVL.Chbmber_A_ComIO.Process.D_Done 也清一下

                                        // IR400 还没有实现，下面暂时注释掉，OK后后台要监控ChbProcessFinished流程是否完成
                                        /*_adsHelper.Write(CHBProcessPLCCmdConstants.Process_B_Start, true);
                                        _adsHelper.Write(CHBProcessPLCCmdConstants.Process_D_Done, false);//清空 单元流程_完成标志位，后台自动检测流程是否完成*/
                                        CurPLcsignalSimulation.ChbProcessFinished = false;//先写，再读取最新值，防止读取到旧值，后面再判断处理

                                        _strMsg = "CHB工艺流程启动命令完成";
                                        UILogService.AddLog(_strMsg);
                                        /*}*/

                                        //记录做工艺历史记录
                                        ArmFetchHistories[curSlot].ClearFetchInfor();
                                        ArmFetchHistories[curSlot].ProcessChamberType = "CH";
                                        ArmFetchHistories[curSlot].ProcessStartTime = DateTime.Now;
                                        ArmFetchHistories[curSlot].ProcessEndTime = null;

                                        SelectedToChamber.StartTimer();
                                        SelectedToChamber.WorkStatus = EnuWorkStatus.Process;

                                        Buffer.ChbSlitDoorStatus = EnuSlitDoorStatus.Close;
                                        CurPLcsignalSimulation.SlotChb = curSlot;
                                        CurPLcsignalSimulation.SlotNorth = 0;
                                        CurPLcsignalSimulation.RobotNorthHaseWafer = false;
                                        CurPLcsignalSimulation.ChbHasWafer = true;
                                        CurPLcsignalSimulation.ChbProcessFinished = false;//先写，再读取最新值，防止读取到旧值，后面再判断处理
                                        CurPLcsignalSimulation.ChbHasWafer = true;
                                    }
                                    else if (!CurPLcsignalSimulation.CoolingHasWafer && (CurPLcsignalSimulation.ChbProcessFinished ?? false) && RightRobotIRArm.IsEmpty() && CurPLcsignalSimulation.GetFirstFinishedChamberName(CurRunRecipeInfo.RecipeName) == EnuChamberName.CHB) //Chb有Wafer且Cooling没有Wafer以及Robot Smooth端为空,防止冲突, Chb->Smooth 发送指令
                                    {
                                        SelectedFromChamber.StopTimer();
                                        curSlot = CurPLcsignalSimulation.SlotChb;
                                        _strMsg = $"SLOT:{curSlot},Chb->Smooth 发送指令";
                                        AppLog.Info(_strMsg);
                                        Console.WriteLine(_strMsg);

                                        //currentLogIndex = 1;//日志索引从1开始
                                        SelectedFromChamber = ChamberB;
                                        SelectedToChamber = RightRobotIRArm;
                                        Buffer.ChbSlitDoorStatus = EnuSlitDoorStatus.Open;
                                        ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Smooth);//out _strMsg
                                        if (!ExcuteResult)
                                            return false;
                                        SelectedFromChamber.WorkStatus = EnuWorkStatus.Idle;
                                        Buffer.ChbSlitDoorStatus = EnuSlitDoorStatus.Close;
                                        CurPLcsignalSimulation.SlotChb = 0;
                                        CurPLcsignalSimulation.ChbHasWafer = false;
                                        CurPLcsignalSimulation.ChbProcessFinished = null;//回到初始状态

                                        CurPLcsignalSimulation.RobotSmothHaseWafer = true;
                                        CurPLcsignalSimulation.SlotSmooth = curSlot;
                                    }
                                }
                                if (CurRunRecipeInfo.RecipeName.Contains("C") && CurPLcsignalSimulation.RobotNorthHaseWafer)
                                {
                                    if (!CurPLcsignalSimulation.ChcHasWafer)//Chc没有Wafer, Nose->Chc 发送指令
                                    {
                                        /*if (!Golbal.IsDevDebug)
                                        {*/
                                        //CH腔体加载启动处理流程【发送指令前加载冷板配方】
                                        /*_adsHelper.Write(CHCProcessPLCCmdConstants.UnitRecipeName_Pre_RecipeDefinitionName, CHCProcessPLCCmdConstants.RecipeTypeCH);
                                        _adsHelper.Write(CHCProcessPLCCmdConstants.UnitRecipeName_Pre_RecipeName, CurDbRecipeSeqInfo.ChRecipeName);
                                        _adsHelper.Write(CHCProcessPLCCmdConstants.LoadRecipe_B_Start, true);
                                        _adsHelper.Write(CHCProcessPLCCmdConstants.LoadRecipe_D_Done, false);//清空 加载配方_完成标志位*/
                                        _strMsg = $"CH加载配方： 【{CurDbRecipeSeqInfo.ChRecipeName}】启动命令完成";
                                        UILogService.AddLog(_strMsg);
                                        await Task.Delay(1000);

                                        /*bool isRecipeLoadOk = await CheckIsTrueAsync(CHCProcessPLCCmdConstants.LoadRecipe_D_Done, TimeSpan.FromSeconds(10), message: $"加载配方：{CurDbRecipeSeqInfo.ChRecipeName}，完成标志位检测失败，请确认后重试！", title: "加载配方检测失败", Golbal.IsDevDebug ? Visibility.Visible : Visibility.Collapsed);
                                        if (!isRecipeLoadOk)
                                        {
                                            _strMsg = "加载配方检测失败，无法执行下去";
                                            UILogService.AddLog(_strMsg);
                                            MessageBox.Show(_strMsg, "加载配方检测失败，无法执行下去", MessageBoxButton.OK, MessageBoxImage.Error);
                                            return false;
                                        }*/
                                        /*}*/

                                        curSlot = CurPLcsignalSimulation.SlotNorth;
                                        _strMsg = $"SLOT:{curSlot},Nose->Chc 发送指令";
                                        AppLog.Info(_strMsg);
                                        Console.WriteLine(_strMsg);

                                        //currentLogIndex = 1;//日志索引从1开始
                                        SelectedFromChamber = LeftRobotIRArm;
                                        SelectedToChamber = ChamberC;
                                        Buffer.ChcSlitDoorStatus = EnuSlitDoorStatus.Open;
                                        ExcuteResult = await LeftRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Nose);//out _strMsg
                                        if (!ExcuteResult)
                                            return false;
                                        /*if (!Golbal.IsDevDebug)
                                        {*/
                                        //3、运行单元流程_启动按钮
                                        //GVL.Chcmber_A_ComIO.Process.B_Start
                                        //GVL.Chcmber_A_ComIO.Process.D_Done 也清一下

                                        // IR400 还没有实现，下面暂时注释掉，OK后后台要监控ChcProcessFinished流程是否完成
                                        /*_adsHelper.Write(CHCProcessPLCCmdConstants.Process_B_Start, true);
                                        _adsHelper.Write(CHCProcessPLCCmdConstants.Process_D_Done, false);//清空 单元流程_完成标志位，后台自动检测流程是否完成*/
                                        CurPLcsignalSimulation.ChcProcessFinished = false;//先写，再读取最新值，防止读取到旧值，后面再判断处理

                                        _strMsg = "CHC工艺流程启动命令完成";
                                        UILogService.AddLog(_strMsg);
                                        /*}*/

                                        //记录做工艺历史记录
                                        ArmFetchHistories[curSlot].ClearFetchInfor();
                                        ArmFetchHistories[curSlot].ProcessChamberType = "CH";
                                        ArmFetchHistories[curSlot].ProcessStartTime = DateTime.Now;
                                        ArmFetchHistories[curSlot].ProcessEndTime = null;

                                        SelectedToChamber.StartTimer();
                                        SelectedToChamber.WorkStatus = EnuWorkStatus.Process;

                                        Buffer.ChcSlitDoorStatus = EnuSlitDoorStatus.Close;
                                        CurPLcsignalSimulation.SlotChc = curSlot;
                                        CurPLcsignalSimulation.SlotNorth = 0;
                                        CurPLcsignalSimulation.RobotNorthHaseWafer = false;
                                        CurPLcsignalSimulation.ChcHasWafer = true;
                                        CurPLcsignalSimulation.ChcProcessFinished = false;//先写，再读取最新值，防止读取到旧值，后面再判断处理
                                        CurPLcsignalSimulation.ChcHasWafer = true;
                                    }
                                    else if (!CurPLcsignalSimulation.CoolingHasWafer && (CurPLcsignalSimulation.ChcProcessFinished ?? false) && RightRobotIRArm.IsEmpty() && CurPLcsignalSimulation.GetFirstFinishedChamberName(CurRunRecipeInfo.RecipeName) == EnuChamberName.CHC) //Chc有Wafer且Cooling没有Wafer以及Robot Smooth端为空,防止冲突, Chc->Smooth 发送指令
                                    {
                                        SelectedFromChamber.StopTimer();
                                        curSlot = CurPLcsignalSimulation.SlotChc;
                                        _strMsg = $"SLOT:{curSlot},Chc->Smooth 发送指令";
                                        AppLog.Info(_strMsg);
                                        Console.WriteLine(_strMsg);

                                        //currentLogIndex = 1;//日志索引从1开始
                                        SelectedFromChamber = ChamberC;
                                        SelectedToChamber = RightRobotIRArm;
                                        Buffer.ChcSlitDoorStatus = EnuSlitDoorStatus.Open;
                                        ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Smooth);//out _strMsg
                                        if (!ExcuteResult)
                                            return false;
                                        SelectedFromChamber.WorkStatus = EnuWorkStatus.Idle;
                                        Buffer.ChcSlitDoorStatus = EnuSlitDoorStatus.Close;
                                        CurPLcsignalSimulation.SlotChc = 0;
                                        CurPLcsignalSimulation.ChcHasWafer = false;
                                        CurPLcsignalSimulation.ChcProcessFinished = null;//回到初始状态

                                        CurPLcsignalSimulation.RobotSmothHaseWafer = true;
                                        CurPLcsignalSimulation.SlotSmooth = curSlot;
                                    }
                                }

                                if (CurPLcsignalSimulation.ChaHasWafer || CurPLcsignalSimulation.ChbHasWafer || CurPLcsignalSimulation.ChcHasWafer)
                                {
                                    //CurPLcsignalSimulation.CheckCanProcessFinished(true);
                                    //Console.WriteLine($"等待腔体工艺中。。。ChaProcessFinished：{CurPLcsignalSimulation.ChaProcessFinished}，ChbProcessFinished：{CurPLcsignalSimulation.ChbProcessFinished}，ChcProcessFinished：{CurPLcsignalSimulation.ChcProcessFinished}");
                                }
                            }
                            else if (queueWaferSlot.Count > 0)
                            {
                                if (!CurPLcsignalSimulation.RobotNorthHaseWafer)
                                {
                                    curSlot = queueWaferSlot.Dequeue();

                                    ArmFetchHistories.Add(curSlot, new ArmFetchProcesHistory(_CurRunRecipeInfoId) { SlotLeft = curSlot, SlotRight = curSlot });

                                    _strMsg = $"SLOT:{curSlot},Cassette->Nose 发送指令";
                                    AppLog.Info(_strMsg);
                                    Console.WriteLine(_strMsg);

                                    //currentLogIndex = 1;//日志索引从1开始
                                    SelectedFromChamber = Cassette;
                                    SelectedToChamber = LeftRobotIRArm;

                                    Loadlock.SlitDoorStatus = EnuSlitDoorStatus.Open;
                                    Buffer.LoadLocklitDoorStatus = EnuSlitDoorStatus.Open;
                                    ExcuteResult = await LeftRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Nose);//out _strMsg
                                    if (!ExcuteResult)
                                        return false;
                                    Loadlock.SlitDoorStatus = EnuSlitDoorStatus.Close;
                                    Buffer.LoadLocklitDoorStatus = EnuSlitDoorStatus.Close;

                                    CurPLcsignalSimulation.SlotNorth = curSlot;
                                    CurPLcsignalSimulation.RobotNorthHaseWafer = true;
                                }
                                else
                                {
                                    _strMsg = $"Wafer数量:{queueWaferSlot.Count},等待 RobotNorthHaseWafer={CurPLcsignalSimulation.RobotNorthHaseWafer}";
                                    AppLog.Info(_strMsg);
                                    Console.WriteLine(_strMsg);
                                }
                            }
                            else
                            {
                                //处理剩余工艺中的wafer,到Smooth端\Cooling端\Cassette端
                                if (CurRunRecipeInfo.RecipeName.Contains("A") && (CurPLcsignalSimulation.ChaProcessFinished ?? false) && CurPLcsignalSimulation.ChaHasWafer && CurPLcsignalSimulation.GetFirstFinishedChamberName(CurRunRecipeInfo.RecipeName) == EnuChamberName.CHA)//有Wafer, Cha->Smooth 发送指令
                                {
                                    SelectedFromChamber.StopTimer();
                                    curSlot = CurPLcsignalSimulation.SlotCha;
                                    _strMsg = $"SLOT:{curSlot},Cha->Smooth 发送指令";
                                    AppLog.Info(_strMsg);
                                    Console.WriteLine(_strMsg);

                                    //currentLogIndex = 1;//日志索引从1开始
                                    SelectedFromChamber = ChamberA;
                                    SelectedToChamber = RightRobotIRArm;
                                    Buffer.ChaSlitDoorStatus = EnuSlitDoorStatus.Open;
                                    ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Smooth);//out _strMsg
                                    if (!ExcuteResult)
                                        return false;
                                    SelectedFromChamber.WorkStatus = EnuWorkStatus.Idle;
                                    Buffer.ChaSlitDoorStatus = EnuSlitDoorStatus.Close;

                                    CurPLcsignalSimulation.SlotCha = 0;
                                    CurPLcsignalSimulation.ChaHasWafer = false;
                                    CurPLcsignalSimulation.ChaProcessFinished = false;

                                    CurPLcsignalSimulation.RobotSmothHaseWafer = true;
                                    CurPLcsignalSimulation.SlotSmooth = curSlot;
                                }
                                else if (CurRunRecipeInfo.RecipeName.Contains("B") && (CurPLcsignalSimulation.ChbProcessFinished ?? false) && CurPLcsignalSimulation.ChbHasWafer && CurPLcsignalSimulation.GetFirstFinishedChamberName(CurRunRecipeInfo.RecipeName) == EnuChamberName.CHB)//有Wafer, Chb->Smooth 发送指令
                                {
                                    SelectedFromChamber.StopTimer();
                                    curSlot = CurPLcsignalSimulation.SlotChb;
                                    _strMsg = $"SLOT:{curSlot},Chb->Smooth 发送指令";
                                    AppLog.Info(_strMsg);
                                    Console.WriteLine(_strMsg);

                                    //currentLogIndex = 1;//日志索引从1开始
                                    SelectedFromChamber = ChamberB;
                                    SelectedToChamber = RightRobotIRArm;
                                    Buffer.ChbSlitDoorStatus = EnuSlitDoorStatus.Open;
                                    ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Smooth);//out _strMsg
                                    if (!ExcuteResult)
                                        return false;
                                    SelectedFromChamber.WorkStatus = EnuWorkStatus.Idle;
                                    Buffer.ChbSlitDoorStatus = EnuSlitDoorStatus.Close;
                                    CurPLcsignalSimulation.SlotChb = 0;
                                    CurPLcsignalSimulation.ChbHasWafer = false;
                                    CurPLcsignalSimulation.ChbProcessFinished = false;

                                    CurPLcsignalSimulation.RobotSmothHaseWafer = true;
                                    CurPLcsignalSimulation.SlotSmooth = curSlot;
                                }
                                else if (CurRunRecipeInfo.RecipeName.Contains("C") && (CurPLcsignalSimulation.ChcProcessFinished ?? false) && CurPLcsignalSimulation.ChcHasWafer && CurPLcsignalSimulation.GetFirstFinishedChamberName(CurRunRecipeInfo.RecipeName) == EnuChamberName.CHC)//有Wafer, Chc->Smooth 发送指令
                                {
                                    SelectedFromChamber.StopTimer();
                                    curSlot = CurPLcsignalSimulation.SlotChc;
                                    _strMsg = $"SLOT:{curSlot},Chc->Smooth 发送指令";
                                    AppLog.Info(_strMsg);
                                    Console.WriteLine(_strMsg);
                                    //currentLogIndex = 1;//日志索引从1开始
                                    SelectedFromChamber = ChamberC;
                                    SelectedToChamber = RightRobotIRArm;
                                    Buffer.ChcSlitDoorStatus = EnuSlitDoorStatus.Open;
                                    ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Smooth);//out _strMsg
                                    if (!ExcuteResult)
                                        return false;
                                    SelectedFromChamber.WorkStatus = EnuWorkStatus.Idle;
                                    Buffer.ChcSlitDoorStatus = EnuSlitDoorStatus.Close;

                                    CurPLcsignalSimulation.SlotChc = 0;
                                    CurPLcsignalSimulation.ChcHasWafer = false;
                                    CurPLcsignalSimulation.ChcProcessFinished = false;

                                    CurPLcsignalSimulation.RobotSmothHaseWafer = true;
                                    CurPLcsignalSimulation.SlotSmooth = curSlot;
                                }
                                else
                                {
                                    if (CurPLcsignalSimulation.ChaHasWafer || CurPLcsignalSimulation.ChbHasWafer || CurPLcsignalSimulation.ChcHasWafer)
                                    {
                                        //CurPLcsignalSimulation.CheckCanProcessFinished(true);
                                        //Console.WriteLine($"等待腔体工艺中。。。ChaProcessFinished：{CurPLcsignalSimulation.ChaProcessFinished}，ChbProcessFinished：{CurPLcsignalSimulation.ChbProcessFinished}，ChcProcessFinished：{CurPLcsignalSimulation.ChcProcessFinished}");
                                    }
                                }
                            }

                            await Task.Delay(100, token);
                        }
                        catch (Exception ex)
                        {
                            var oce = ex as OperationCanceledException;
                            if (oce != null)
                            {
                                if (IsPauseAndContinue)//点击暂停后捕获
                                {
                                    while (IsPauseAndContinue)
                                    {
                                        await Task.Delay(1000);

                                        if (_isStopFlag)//在暂停中，执行停止
                                        {
                                            break;
                                        }
                                    }
                                    if (_isStopFlag)//在暂停中，执行停止
                                    {
                                        IsEnablePauseCmd = false;
                                        break;
                                    }
                                    else
                                    {
                                        CtsPathPlanning = new CancellationTokenSource();
                                        token = CtsPathPlanning.Token;
                                        //CtsRobotCmdTimeOut = new CancellationTokenSource(TimeSpan.FromSeconds(Golbal.IsDevDebug ? Golbal.DebugCommandRunTimeout : Golbal.CommandRunTimeout)); // 设置超时时间为120秒
                                    }
                                }
                                else  //直接点击停止后捕获
                                {
                                    CtsPathPlanning = null;
                                    IsEnablePauseCmd = false;
                                    break;
                                }
                            }
                        }
                        //MessageBox.Show("观察状态");
                    }
                    Loadlock.IsOpen = true;
                    Cassette.WorkStatus = EnuWorkStatus.Idle;
                    Buffer.WorkStatus = EnuWorkStatus.Idle;
                    if (WholeProcessCmdTriggered.StartSequenceSingal)
                    {
                        _strMsg = "整个处理完成！\r\n";
                        blFinishedOKResult = true;
                        HcGrowlExtensions.Info(_strMsg);
                    }
                    else
                    {
                        _strMsg = "整个处理未完成！Sequence Busy信号={WholeProcessCmdTriggered.StartSequenceSingal}\r\n";
                        HcGrowlExtensions.Warning(_strMsg);
                    }
                    UILogService.AddLog(_strMsg);
                    CurPLcsignalSimulation.Reset();

                    #endregion 方式1：Cassette->CH->Cooling->Cassette 开发测试

                    if (LoopCount != -1)
                    {
                        LoopCount--;
                    }
                }
            }
            catch (Exception ex)
            {
                AppLog.Error(ex.Message);
                Console.WriteLine(ex.Message);
            }
            finally
            {
                CurPLcsignalSimulation.IsLoopCheckAutoClick = false;//循环结束，退出自动点击按钮
                IsStopLoopRunning = true;
                IsReset = true;
                //IsRunCmd = true;
                //    break;
                _timer.Stop();
                IsRunning = false;
            }

            return blFinishedOKResult;
        }

        /// <summary>
        /// 在指定的时间内检测是否True及完成
        /// </summary>
        /// <param name="plcVarAddress">PLC变量地址</param>
        /// <param name="timeout">溢出时间</param>
        /// <returns></returns>
        public async Task<bool> CheckIsTrueAsync(string plcVarAddress, TimeSpan timeout, string message, string title, Visibility visibility = Visibility.Collapsed)
        {
            var startTime = DateTime.Now;
            bool isTrue = false;

            while (!isTrue)
            {
                // 根据传入的地址检测是否True
                // isTrue = _adsHelper.Read<bool>(plcVarAddress);
                isTrue = true;
                if (isTrue)
                {
                    return true; // 状态为True
                }

                // 检查是否超时
                if (DateTime.Now - startTime > timeout)
                {
                    // 超时，显示自定义对话框
                    var customMessageBoxResult = await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        var customMessageBox = new CustomMessageBox(message, title, visibility);
                        return customMessageBox.ShowDialog() == true ? customMessageBox.Result : CustomMessageBox.CustomMessageBoxResult.Exit;
                    });

                    switch (customMessageBoxResult)
                    {
                        case CustomMessageBox.CustomMessageBoxResult.Retry:
                            //调整在5秒内重试
                            startTime = DateTime.Now.Subtract(timeout).AddSeconds(5);
                            break;

                        case CustomMessageBox.CustomMessageBoxResult.Exit:
                            // 退出
                            return false;

                        case CustomMessageBox.CustomMessageBoxResult.Continue:
                            // 忽略
                            return true;
                    }
                }

                await Task.Delay(1000); // 等待一段时间后再次检测
            }

            return false; // 默认返回false
        }

        /// <summary>
        /// 最初Sequence
        /// </summary>
        private async Task SequenceTest1()
        {
            #region 到CHA、CHB、CHC

            SelectedFromChamber = Cassette;
            SelectedToChamber = ChamberA;
            //cmdList.Clear();
            //currentLogIndex = 1;//日志索引从1开始
            var curSlot = 1;
            ExcuteResult = await LeftRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, SelectedByArmFetchSide);//out _strMsg
            if (!ExcuteResult)
                return;

            //cmdList.Clear();
            //currentLogIndex = 1;//日志索引从1开始
            curSlot = 2;
            SelectedFromChamber = Cassette;
            SelectedToChamber = ChamberB;
            ExcuteResult = await LeftRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, SelectedByArmFetchSide);//out _strMsg
            if (!ExcuteResult)
                return;

            //cmdList.Clear();
            //currentLogIndex = 1;//日志索引从1开始
            curSlot = 3;
            SelectedFromChamber = Cassette;
            SelectedToChamber = ChamberC;
            ExcuteResult = await LeftRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, SelectedByArmFetchSide);//out _strMsg
            if (!ExcuteResult)
                return;

            #endregion 到CHA、CHB、CHC

            #region CHA回Cassettes

            //cmdList.Clear();
            //currentLogIndex = 1;//日志索引从1开始
            curSlot = 1;
            SelectedFromChamber = ChamberA;
            SelectedToChamber = Cooling;
            ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Smooth);//out _strMsg
            if (!ExcuteResult)
                return;

            //cmdList.Clear();
            //currentLogIndex = 1;//日志索引从1开始
            curSlot = 1;
            SelectedFromChamber = Cooling;
            SelectedToChamber = Cassette;
            ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Smooth);//out _strMsg
            if (!ExcuteResult)
                return;

            #endregion CHA回Cassettes

            #region CHB回Cassettes

            //cmdList.Clear();
            //currentLogIndex = 1;//日志索引从1开始
            curSlot = 2;
            SelectedFromChamber = ChamberB;
            SelectedToChamber = Cooling;
            ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Smooth);//out _strMsg
            if (!ExcuteResult)
                return;

            //cmdList.Clear();
            //currentLogIndex = 1;//日志索引从1开始
            curSlot = 2;
            SelectedFromChamber = Cooling;
            SelectedToChamber = Cassette;
            ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Smooth);//out _strMsg
            if (!ExcuteResult)
                return;

            #endregion CHB回Cassettes

            #region CHC回Cassettes

            //cmdList.Clear();
            //currentLogIndex = 1;//日志索引从1开始
            curSlot = 3;
            SelectedFromChamber = ChamberC;
            SelectedToChamber = Cooling;
            ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Smooth);//out _strMsg
            if (!ExcuteResult)
                return;

            //cmdList.Clear();
            //currentLogIndex = 1;//日志索引从1开始
            curSlot = 3;
            SelectedFromChamber = Cooling;
            SelectedToChamber = Cassette;
            ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Smooth);//out _strMsg
            if (!ExcuteResult)
                return;

            #endregion CHC回Cassettes

            #region 第四片循环处理

            //cmdList.Clear();
            //currentLogIndex = 1;//日志索引从1开始
            curSlot = 4;
            SelectedFromChamber = Cassette;
            SelectedToChamber = ChamberA;
            ExcuteResult = await LeftRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, SelectedByArmFetchSide);//out _strMsg
            if (!ExcuteResult)
                return;

            //cmdList.Clear();
            //currentLogIndex = 1;//日志索引从1开始
            curSlot = 4;
            SelectedFromChamber = ChamberA;
            SelectedToChamber = Cooling;

            ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Smooth);//out _strMsg
            if (!ExcuteResult)
                return;

            //cmdList.Clear();
            //currentLogIndex = 1;//日志索引从1开始
            curSlot = 4;
            SelectedFromChamber = Cooling;
            SelectedToChamber = Cassette;

            ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, curSlot, SelectedToChamber, curSlot, Cassette, EnuArmFetchSide.Smooth);//out _strMsg
            if (!ExcuteResult)
                return;

            #endregion 第四片循环处理
        }

        /// <summary>
        /// 执行Robot停止命令：STOP A
        /// </summary>
        [RelayCommand]
        private async Task OnProcessStop()
        {
            _AppStartDateTime = DateTime.Now;
            _timer.Start();

            //Golbal.IsDevDebug = File.Exists(Path.Combine("C:\\IR400", "debug.tag"));
            string cmdStop = string.Empty;
            (bool, string) tupleResult = (false, string.Empty);
            await CtsPathPlanning.CancelAsync();
            if (!Golbal.IsDevDebug)
            {
                cmdStop = "STOP A";
                tupleResult = await ProcessRobotCustomCommand(cmdStop);
                RequestCmdDesc = cmdStop;
                ResponseCmd = $"Result:{tupleResult.Item1},message:{tupleResult.Item2}";
            }

            //CtsRobotCmdTimeOut.Cancel();
            CancelRobotCmdNextExcute = true;
            PauseRobotCmdNextExcute = false;

            //记录日志
            _strMsg = $"执行停止命令：{cmdStop}，返回：{tupleResult.Item1}，返回信息：{tupleResult.Item2}";
            UILogService.AddLog(_strMsg);
            ResponseCmd = tupleResult.Item1.ToString();
            HcGrowlExtensions.Warning("取消处理Robot命令");

            _isStopFlag = true;
            _timer.Stop();
        }

        /// <summary>
        /// 执行Robot暂停后下次再往下执行
        /// </summary>
        [RelayCommand]
        private async Task OnProcessPauseNew()
        {
            _isStopFlag = false;
            IsPauseAndContinue = !IsPauseAndContinue;
            IsEnablePauseContent = IsPauseAndContinue ? "继续" : "暂停";
            if (!IsPauseAndContinue)
            {
                return;
            }

            _AppStartDateTime = DateTime.Now;
            _timer.Start();

            //Golbal.IsDevDebug = File.Exists(Path.Combine("C:\\IR400", "debug.tag"));
            string cmdStop = string.Empty;
            (bool, string) tupleResult = (false, string.Empty);
            if (!Golbal.IsDevDebug)
            {
                cmdStop = "STOP A";
                tupleResult = await ProcessRobotCustomCommand(cmdStop);
                RequestCmdDesc = cmdStop;
                ResponseCmd = $"Result:{tupleResult.Item1},message:{tupleResult.Item2}";
            }

            await CtsPathPlanning.CancelAsync();
            //CtsRobotCmdTimeOut.Cancel();
            CancelRobotCmdNextExcute = true;

            //记录日志
            _strMsg = $"执行停止命令：{cmdStop}，返回：{tupleResult.Item1}，返回信息：{tupleResult.Item2}";
            UILogService.AddLog(_strMsg);
            ResponseCmd = tupleResult.Item1.ToString();
            HcGrowlExtensions.Warning("取消处理Robot命令");

            _timer.Stop();
        }

        /// <summary>
        /// 执行Robot暂停后下次再往下执行
        /// </summary>
        /// <param name="parameter"></param>
        private async void ProcessPauseCommand(string parameter)
        {
            if (PauseRobotCmdNextExcute)
            {
                IsEnablePauseContent = CancelRobotCmdNextExcute ? "继续" : "暂停";
                PauseRobotCmdNextExcute = false;
                return;
            }

            _AppStartDateTime = DateTime.Now;
            _timer.Start();

            //Golbal.IsDevDebug = File.Exists(Path.Combine("C:\\IR400", "debug.tag"));
            string cmdStop = string.Empty;
            (bool, string) tupleResult = (false, string.Empty);
            if (!Golbal.IsDevDebug)
            {
                cmdStop = "STOP A";
                tupleResult = await ProcessRobotCustomCommand(cmdStop);
                RequestCmdDesc = cmdStop;
                ResponseCmd = $"Result:{tupleResult.Item1},message:{tupleResult.Item2}";
            }

            await CtsPathPlanning.CancelAsync();
            //CtsRobotCmdTimeOut.Cancel();
            CancelRobotCmdNextExcute = true;

            PauseRobotCmdNextExcute = !PauseRobotCmdNextExcute;
            IsEnablePauseContent = CancelRobotCmdNextExcute ? "继续" : "暂停";

            //记录日志
            _strMsg = $"执行停止命令：{cmdStop}，返回：{tupleResult.Item1}，返回信息：{tupleResult.Item2}";
            UILogService.AddLog(_strMsg);
            ResponseCmd = tupleResult.Item1.ToString();
            HcGrowlExtensions.Warning("取消处理Robot命令");

            _timer.Stop();
        }

        /// <summary>
        /// 处理自定义命令
        /// </summary>
        /// <param name="requestCmd"></param>
        /// <param name="requestTag"></param>
        /// <param name="responseTag"></param>
        /// <param name="responseOK"></param>
        /// <returns></returns>
        private Task<(bool, string)> ProcessRobotCustomCommand(string requestCmd, string requestTag = "GVL.WholeCommand_sPcControlWord", string responseTag = "GVL.WholeCommand_sPlcResponse", string responseOK = "OK")
        {
            _AppStartDateTime = DateTime.Now;
            _timer.Start();

            bool blResult = false;
            string message = string.Empty;
            try
            {
                /*if (_adsHelper.IsRun)
                {
                    CancellationTokenSource ctsRobotCmdTimeOut = new CancellationTokenSource(TimeSpan.FromSeconds(Golbal.IsDevDebug ? Golbal.DebugCommandRunTimeout : Golbal.CommandRunTimeout)); // 设置超时时间为120秒
                                                                                                                                                                                                  //symbol.WriteValue(true);
                    _adsHelper.Write(responseTag, string.Empty);
                    _adsHelper.Write(requestTag, requestCmd);
                    _adsHelper.Write("GVL.WholeCommand_CMD_Enable", true);//PLC那边解决重复执行命令问题,不回复，需要先置位

                    await Task.Delay(1000, CtsPathPlanning.Token);  // 1000毫秒

                    do
                    {
                        await Task.Delay(100, CtsPathPlanning.Token);  // 每次循环等待100毫秒

                        //symbol = (DynamicSymbol)Golbal._symbolLoader.Symbols[responseTag];
                        //ResponseCmd = symbol.ReadValue().ToString();
                        ResponseCmd = _adsHelper.Read<string>(responseTag);

                        message = ResponseCmd;

                        if (!string.IsNullOrEmpty(ResponseCmd))
                        {
                            blResult = true;
                            break;  // 响应为"OK"，退出循环
                        }
                    } while (!ctsRobotCmdTimeOut.IsCancellationRequested);

                    if (!blResult) // 如果在超时时间内未收到"OK"响应
                    {
                        message = "等待超时，未收到正确的响应";
                    }
                }
                else
                {
                    blResult = true;
                    message = "没有点击启动按钮";
                }*/
            }
            catch (Exception ex)
            {
                message = ex.Message;
            }
            finally
            {
                _timer.Stop();
            }

            return Task.FromResult((blResult, message));
        }

        ///// <summary>
        ///// 保存轴位置
        ///// </summary>
        ///// <param name="requestCmd">请求命令</param>
        ///// <param name="requestCmdDesc">请求命令描述解释</param>
        /////  <param name="responseResult">命令回复结果</param>
        //public void SaveAxisPosition(string requestCmd, string requestCmdDesc, string responseResult)
        //{
        //    try
        //    {
        //        AxisAfterRunCmd axisAfterRunCmd = new AxisAfterRunCmd();
        //        axisAfterRunCmd.RequestCmd = requestCmd;
        //        axisAfterRunCmd.RequestCmdDesc = requestCmdDesc;
        //        axisAfterRunCmd.ResponseResult = responseResult;
        //        if (Golbal.RTZAxisLocationPLCAddressVarInfos == null)
        //        {
        //            return;
        //        }
        //        //重新获取更新轴位置
        //        if (_adsHelper.IsRun)
        //        {
        //            foreach (var item in Golbal.RTZAxisLocationPLCAddressVarInfos)
        //            {
        //                //var symbol = (DynamicSymbol)Golbal._symbolLoader.Symbols[item.PLC_AddressVar];
        //                //item.PLC_AddressVarValue = symbol.ReadValue();
        //                item.PLC_AddressVarValue = _adsHelper.Read<double>(item.PLC_AddressVar);
        //            }
        //        }

        //        var axisLocationPLCAddressVarInfo = Golbal.RTZAxisLocationPLCAddressVarInfos.FirstOrDefault(t => t.ModuleInType == "R_Axsix");
        //        axisAfterRunCmd.RAxisCurrentValue = axisLocationPLCAddressVarInfo != null ? Math.Round((double)axisLocationPLCAddressVarInfo.PLC_AddressVarValue, 2) : -1;
        //        axisLocationPLCAddressVarInfo = Golbal.RTZAxisLocationPLCAddressVarInfos.FirstOrDefault(t => t.ModuleInType == "T_Axsix");
        //        axisAfterRunCmd.TAxisCurrentValue = axisLocationPLCAddressVarInfo != null ? Math.Round((double)axisLocationPLCAddressVarInfo.PLC_AddressVarValue, 2) : -1;
        //        axisLocationPLCAddressVarInfo = Golbal.RTZAxisLocationPLCAddressVarInfos.FirstOrDefault(t => t.ModuleInType == "Z_Axsix");
        //        axisAfterRunCmd.ZAxisCurrentValue = axisLocationPLCAddressVarInfo != null ? Math.Round((double)axisLocationPLCAddressVarInfo.PLC_AddressVarValue, 2) : -1;

        //        _dBAccessService.AddAxisAfterRunCmdData(axisAfterRunCmd);
        //    }
        //    catch (Exception ex)
        //    {
        //        AppLog.Error("SaveAxisPosition 命令保存轴位置异常", ex);
        //        throw;
        //    }
        //}

        /// <summary>
        /// 保存轴位置
        /// </summary>
        /// <param name="requestCmd">请求命令</param>
        /// <param name="requestCmdDesc">请求命令描述解释</param>
        ///  <param name="responseResult">命令回复结果</param>
        public void SaveAxisPosition(string requestCmd, string requestCmdDesc, string responseResult)
        {
            try
            {
                /*AxisAfterRunCmd axisAfterRunCmd = GetAxisPosition();
                axisAfterRunCmd.RequestCmd = requestCmd;
                axisAfterRunCmd.RequestCmdDesc = requestCmdDesc;
                axisAfterRunCmd.ResponseResult = responseResult;

                _dBAccessService.AddAxisAfterRunCmdData(axisAfterRunCmd);*/
            }
            catch (Exception ex)
            {
                AppLog.Error("SaveAxisPosition 命令保存轴位置异常", ex);
                throw;
            }
        }

        /*/// <summary>
        /// 获取轴位置
        /// </summary>
        /// <returns></returns>
        public AxisAfterRunCmd GetAxisPosition()
        {
            AxisAfterRunCmd axisAfterRunCmd = null;

            try
            {
                (axisAfterRunCmd, _readCount) = _adsHelper.ReadSumAdsVariables<AxisAfterRunCmd>();
                if (axisAfterRunCmd != null && _readCount != 6)
                {
                    AppLog.Error($"获取轴位置,读取到的数量不为6，实际为：{_readCount}");
                    //throw new Exception($"读取WholeProcessCmd全部变量失败");
                }

                //if (_RTZAxisLocationMaxRTravelPLCAddressVarInfos != null)
                //{
                //    //重新获取更新轴位置
                //    if (_adsHelper.CanReadWrite)
                //    {
                //        foreach (var item in _RTZAxisLocationMaxRTravelPLCAddressVarInfos)
                //        {
                //            item.PLC_AddressVarValue = _adsHelper.Read<double>(item.PLC_AddressVar);
                //        }
                //    }
                //    else
                //    {
                //        return null;
                //    }

                //    axisAfterRunCmd = new AxisAfterRunCmd();

                //    var axisLocationPLCAddressVarInfo = _RTZAxisLocationMaxRTravelPLCAddressVarInfos.FirstOrDefault(t => t.ModuleInType == "R_Axsix");
                //    axisAfterRunCmd.RAxisCurrentValue = axisLocationPLCAddressVarInfo != null ? Math.Round((double)axisLocationPLCAddressVarInfo.PLC_AddressVarValue, 2) : -1;
                //    axisAfterRunCmd.RAxisPosTransformX = _adsHelper.Read<double>("GVL.Axis_R_Pos_Transform_X");
                //    axisAfterRunCmd.RAxisPosTransformY = _adsHelper.Read<double>("GVL.Axis_R_Pos_Transform_Y");

                //    axisLocationPLCAddressVarInfo = _RTZAxisLocationMaxRTravelPLCAddressVarInfos.FirstOrDefault(t => t.ModuleInType == "T_Axsix");
                //    axisAfterRunCmd.TAxisCurrentValue = axisLocationPLCAddressVarInfo != null ? Math.Round((double)axisLocationPLCAddressVarInfo.PLC_AddressVarValue, 2) : -1;
                //    axisLocationPLCAddressVarInfo = _RTZAxisLocationMaxRTravelPLCAddressVarInfos.FirstOrDefault(t => t.ModuleInType == "Z_Axsix");
                //    axisAfterRunCmd.ZAxisCurrentValue = axisLocationPLCAddressVarInfo != null ? Math.Round((double)axisLocationPLCAddressVarInfo.PLC_AddressVarValue, 2) : -1;

                //    axisLocationPLCAddressVarInfo = _RTZAxisLocationMaxRTravelPLCAddressVarInfos.FirstOrDefault(t => t.ModuleInType == "ArmA" && t.DescriptionChs == "R轴最大行程_CHA");
                //    axisAfterRunCmd.RAxisMaxValue = axisLocationPLCAddressVarInfo != null ? Math.Round((double)axisLocationPLCAddressVarInfo.PLC_AddressVarValue, 2) : -1;

                //    //axisAfterRunCmd.RAxisMaxValue = _adsHelper.Read<double>("GVL_SV.UnitParameter.UnitCoordinate.ArmA.UnitMaxPos[0]");
                //}
            }
            catch (Exception ex)
            {
                // 记录详细的错误信息
                AppLog.Error("读取轴位置失败", ex);
                //throw;
            }

            return axisAfterRunCmd;
        }*/

        #endregion 命令事件处理

        #region UI拖拽处理

        void IDropTarget.DragOver(IDropInfo dropInfo)
        {
            //同项目内禁止拖动调整顺序
            if (dropInfo.VisualTarget == dropInfo.DragInfo.VisualSource)
            {
                dropInfo.NotHandled = dropInfo.VisualTarget == dropInfo.DragInfo.VisualSource;
                return;
            }

            dropInfo.Effects = DragDropEffects.Move;
            dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
            //dropInfo.DropTargetAdorner = typeof(DropTargetHighlightAdorner);
            //dropInfo.DropTargetAdorner = typeof(SimpleCircleAdorner);
        }

        async void IDropTarget.Drop(IDropInfo dropInfo)
        {
            //同项目内禁止拖动调整顺序
            if (dropInfo.VisualTarget == dropInfo.DragInfo.VisualSource)
            {
                dropInfo.NotHandled = dropInfo.VisualTarget == dropInfo.DragInfo.VisualSource;
                return;
            }

            // 获取源列表的父控件
            var sourceParent = dropInfo.DragInfo.VisualSource as FrameworkElement;
            // 获取目标列表的父控件
            var targetParent = dropInfo.VisualTarget as FrameworkElement;

            IWaferDragDropInfo SrcWaferDragDropInfo = sourceParent as IWaferDragDropInfo;
            IWaferDragDropInfo TargetWaferDragDropInfo = targetParent as IWaferDragDropInfo;

            _strMsg = string.Empty;
            if (SrcWaferDragDropInfo != null)
            {
                _strMsg = "源信息：" + SrcWaferDragDropInfo.ToString();
            }
            if (TargetWaferDragDropInfo != null)
            {
                _strMsg += "\r\n目标信息：" + TargetWaferDragDropInfo.ToString();
            }
            //MessageBox.Show(_strMsg);

            int waferNo = 0;

            if (targetParent is Arm targetArm)
            {
                MessageBoxResult resultRobot = MessageBox.Show($"{_strMsg}\r\n请选择机械臂 Nose端【Y】 或 Smooth端【N】,取消【ESC】", "选择", MessageBoxButton.YesNoCancel, MessageBoxImage.Question);
                if (resultRobot == MessageBoxResult.Yes)
                {
                    SelectedByArmFetchSide = EnuArmFetchSide.Nose;
                    SelectedToChamber = targetArm.NoseRobotIRArm;
                }
                else if (resultRobot == MessageBoxResult.No)
                {
                    SelectedByArmFetchSide = EnuArmFetchSide.Smooth;
                    SelectedToChamber = targetArm.SmoothRobotIRArm;
                }
                else if (resultRobot == MessageBoxResult.Cancel)
                {
                    return;
                }

                //判断目标是否已经有Wafer，有绝不允许拖入
                if (SelectedToChamber.HasUnfinishedWafer() && SelectedToChamber.ChamberName != EnuChamberName.Cassette)
                {
                    MessageBox.Show($"目标：{SelectedToChamber.ChamberName}，已经有Wafer，无法再次拖入！！！");
                    return;
                }

                waferNo = SrcWaferDragDropInfo.WaferNo;
                SelectedFromSlot = SelectedToSlot = waferNo;
                SelectedFromChamber = SrcWaferDragDropInfo.CurCharber;
            }
            else if (sourceParent is Arm srcArm)
            {
                MessageBoxResult resultRobot = MessageBox.Show($"{_strMsg}\r\n请选择机械臂 Nose端【Y】 或 Smooth端【N】,取消【ESC】", "选择", MessageBoxButton.YesNoCancel, MessageBoxImage.Question);
                if (resultRobot == MessageBoxResult.Yes)
                {
                    SelectedByArmFetchSide = EnuArmFetchSide.Nose;
                    SelectedFromChamber = srcArm.NoseRobotIRArm;
                }
                else if (resultRobot == MessageBoxResult.No)
                {
                    SelectedByArmFetchSide = EnuArmFetchSide.Smooth;
                    SelectedFromChamber = srcArm.SmoothRobotIRArm;
                }
                else if (resultRobot == MessageBoxResult.Cancel)
                {
                    return;
                }

                //判断源是否有Wafer，有才允许拖入
                if (!SelectedFromChamber.HasUnfinishedWafer())
                {
                    MessageBox.Show($"源头：{SelectedFromChamber.ChamberName}，没有Wafer，无法拖入！！！");
                    return;
                }
                //waferNo = SrcWaferDragDropInfo.WaferNo;
                waferNo = SelectedFromChamber.GetUnFinishededWaferNos().FirstOrDefault();
                //waferNo= SelectedFromChamber.LeftWaferAction.Wafers[0].WaferNo;
                SelectedFromChamber.GetUnFinishededWaferNos();
                SelectedFromSlot = SelectedToSlot = waferNo;
                SelectedToChamber = TargetWaferDragDropInfo.CurCharber;
            }
            else
            {
                //判断目标是否已经有Wafer，有绝不允许拖入
                if (TargetWaferDragDropInfo.CurCharber.HasUnfinishedWafer() && TargetWaferDragDropInfo.CurCharber.ChamberName != EnuChamberName.Cassette)
                {
                    MessageBox.Show($"目标：{TargetWaferDragDropInfo.CurCharber.ChamberName}，已经有Wafer，无法再次拖入！！！");
                    return;
                }

                waferNo = SrcWaferDragDropInfo.WaferNo;
                SelectedFromSlot = SelectedToSlot = waferNo;
                SelectedFromChamber = SrcWaferDragDropInfo.CurCharber;
                SelectedToChamber = TargetWaferDragDropInfo.CurCharber;

                //通过界面选择
                _strMsg = $"源头信息: {SelectedFromChamber.ChamberName} Slot：{waferNo}\r\n" +
                          $"目标信息: {SelectedToChamber.ChamberName} Slot：{waferNo}\r\n";
                MessageBoxResult result = MessageBox.Show($"{_strMsg}\r\n请选择机械臂 Nose端【Y】 或 Smooth端【N】,取消【ESC】", "选择", MessageBoxButton.YesNoCancel, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    SelectedByArmFetchSide = EnuArmFetchSide.Nose;
                }
                else if (result == MessageBoxResult.No)
                {
                    SelectedByArmFetchSide = EnuArmFetchSide.Smooth;
                }
                else if (result == MessageBoxResult.Cancel)
                {
                    return;
                }
            }

            //var waferNo = SrcWaferDragDropInfo.WaferNo;
            //SelectedFromSlot = SelectedToSlot = waferNo;
            //SelectedFromChamber = SrcWaferDragDropInfo.CurCharber;
            //SelectedToChamber = TargetWaferDragDropInfo.CurCharber;

            //currentLogIndex = 1;//日志索引从1开始

            #region 搬运开门控制

            switch (SelectedToChamber.ChamberName)
            {
                case EnuChamberName.CHA:
                    Buffer.ChaSlitDoorStatus = EnuSlitDoorStatus.Open;
                    break;

                case EnuChamberName.CHB:
                    Buffer.ChbSlitDoorStatus = EnuSlitDoorStatus.Open;
                    break;

                case EnuChamberName.CHC:
                    Buffer.ChcSlitDoorStatus = EnuSlitDoorStatus.Open;
                    break;

                case EnuChamberName.Cooling:
                    break;

                case EnuChamberName.Cassette:
                    Buffer.LoadLocklitDoorStatus = EnuSlitDoorStatus.Open;
                    break;

                case EnuChamberName.LoadLock:
                    break;

                case EnuChamberName.RobotArmNose:
                    break;

                case EnuChamberName.RobotArmSmooth:
                    break;

                case EnuChamberName.Home:
                    break;

                case EnuChamberName.Host:
                    break;

                case EnuChamberName.Buffer:
                    break;
            }

            switch (SelectedFromChamber.ChamberName)
            {
                case EnuChamberName.CHA:
                    Buffer.ChaSlitDoorStatus = EnuSlitDoorStatus.Open;
                    SelectedToChamber.StopTimer();
                    SelectedToChamber.WorkStatus = EnuWorkStatus.Idle;
                    break;

                case EnuChamberName.CHB:
                    Buffer.ChbSlitDoorStatus = EnuSlitDoorStatus.Open;
                    SelectedToChamber.StopTimer();
                    SelectedToChamber.WorkStatus = EnuWorkStatus.Idle;
                    break;

                case EnuChamberName.CHC:
                    Buffer.ChcSlitDoorStatus = EnuSlitDoorStatus.Open;
                    SelectedToChamber.StopTimer();
                    SelectedToChamber.WorkStatus = EnuWorkStatus.Idle;
                    break;

                case EnuChamberName.Cooling:
                    SelectedToChamber.StopTimer();
                    SelectedToChamber.WorkStatus = EnuWorkStatus.Idle;
                    break;

                case EnuChamberName.Cassette:
                    Buffer.LoadLocklitDoorStatus = EnuSlitDoorStatus.Open;
                    break;

                case EnuChamberName.LoadLock:
                    break;

                case EnuChamberName.RobotArmNose:
                    break;

                case EnuChamberName.RobotArmSmooth:
                    break;

                case EnuChamberName.Home:
                    break;

                case EnuChamberName.Host:
                    break;

                case EnuChamberName.Buffer:
                    break;
            }

            #endregion 搬运开门控制

            switch (SelectedByArmFetchSide)
            {
                case EnuArmFetchSide.Nose:

                    ExcuteResult = await LeftRobotIRArm.TransferWafer(SelectedFromChamber, waferNo, SelectedToChamber, waferNo, Cassette, SelectedByArmFetchSide);//out _strMsg
                    break;

                case EnuArmFetchSide.Smooth:

                    ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, waferNo, SelectedToChamber, waferNo, Cassette, SelectedByArmFetchSide);//out _strMsg

                    break;

                case EnuArmFetchSide.Unknow:
                    MessageBox.Show(EnuArmFetchSide.Unknow.ToString());
                    break;
            }

            if (!ExcuteResult)
            {
                return;
            }

            #region 搬运关门控制

            switch (SelectedToChamber.ChamberName)
            {
                case EnuChamberName.CHA:
                    Buffer.ChaSlitDoorStatus = EnuSlitDoorStatus.Close;
                    SelectedToChamber.StartTimer();
                    SelectedToChamber.WorkStatus = EnuWorkStatus.Process;
                    break;

                case EnuChamberName.CHB:
                    Buffer.ChbSlitDoorStatus = EnuSlitDoorStatus.Close;
                    SelectedToChamber.StartTimer();
                    SelectedToChamber.WorkStatus = EnuWorkStatus.Process;
                    break;

                case EnuChamberName.CHC:
                    Buffer.ChcSlitDoorStatus = EnuSlitDoorStatus.Close;
                    SelectedToChamber.StartTimer();
                    SelectedToChamber.WorkStatus = EnuWorkStatus.Process;
                    break;

                case EnuChamberName.Cooling:
                    SelectedToChamber.StartTimer();
                    SelectedToChamber.WorkStatus = EnuWorkStatus.Process;

                    break;

                case EnuChamberName.Cassette:
                    Buffer.LoadLocklitDoorStatus = EnuSlitDoorStatus.Close;
                    break;

                case EnuChamberName.LoadLock:
                    break;

                case EnuChamberName.RobotArmNose:
                    break;

                case EnuChamberName.RobotArmSmooth:
                    SelectedToChamber.StopTimer();
                    SelectedToChamber.WorkStatus = EnuWorkStatus.Idle;
                    Buffer.ChaSlitDoorStatus = EnuSlitDoorStatus.Close;
                    break;

                case EnuChamberName.Home:
                    break;

                case EnuChamberName.Host:
                    break;

                case EnuChamberName.Buffer:
                    break;
            }

            switch (SelectedFromChamber.ChamberName)
            {
                case EnuChamberName.CHA:
                    Buffer.ChaSlitDoorStatus = EnuSlitDoorStatus.Close;
                    SelectedFromChamber.StopTimer();
                    SelectedFromChamber.WorkStatus = EnuWorkStatus.Idle;
                    break;

                case EnuChamberName.CHB:
                    Buffer.ChbSlitDoorStatus = EnuSlitDoorStatus.Close;
                    SelectedFromChamber.StopTimer();
                    SelectedFromChamber.WorkStatus = EnuWorkStatus.Idle;
                    break;

                case EnuChamberName.CHC:
                    Buffer.ChcSlitDoorStatus = EnuSlitDoorStatus.Close;
                    SelectedFromChamber.StopTimer();
                    SelectedFromChamber.WorkStatus = EnuWorkStatus.Idle;
                    break;

                case EnuChamberName.Cooling:
                    SelectedFromChamber.StopTimer();
                    SelectedFromChamber.WorkStatus = EnuWorkStatus.Idle;
                    break;

                case EnuChamberName.Cassette:
                    Buffer.LoadLocklitDoorStatus = EnuSlitDoorStatus.Close;
                    break;

                case EnuChamberName.LoadLock:
                    break;

                case EnuChamberName.RobotArmNose:
                    break;

                case EnuChamberName.RobotArmSmooth:
                    break;

                case EnuChamberName.Home:
                    break;

                case EnuChamberName.Host:
                    break;

                case EnuChamberName.Buffer:
                    break;
            }

            #endregion 搬运关门控制

            //targetUContainer.CurWaferStatus = waferSrc;
            //targetUContainer.IsLeftWaferShow = true;
            //targetUContainer.IsRightWaferShow = true;

            // 获取源列表的父控件
            //var sourceParent = dropInfo.DragInfo.VisualSource as FrameworkElement;
            //// 获取目标列表的父控件
            //var targetParent = dropInfo.VisualTarget as FrameworkElement;

            //// 获取源列表父控件的 UContainer 实例
            //var sourceUContainer = GetParentOfType<UContainer>(sourceParent);
            //// 获取目标列表父控件的 UContainer 实例
            //var targetUContainer = GetParentOfType<UContainer>(targetParent);

            ////判断目标是否已经有Wafer，有绝不允许拖入
            //if (targetUContainer.CurCharber.HasUnfinishedWafer() && targetUContainer.CurCharber.ChamberName != EnuChamberName.Cassette)
            //{
            //    MessageBox.Show($"目标：{targetUContainer.CurCharber.ChamberName}，已经有Wafer，无法再次拖入！！！");
            //    return;
            //}

            //var waferSrc = dropInfo.Data as Wafer;

            //SelectedFromSlot = SelectedToSlot = waferSrc.WaferNo;
            //SelectedFromChamber = sourceUContainer.CurCharber;
            //SelectedToChamber = targetUContainer.CurCharber;
            //通过界面选择
            //_strMsg = $"源头信息: {sourceUContainer.CurCharber.ChamberName} Slot：{waferSrc.WaferNo}\r\n" +
            //          $"目标信息: {targetUContainer.CurCharber.ChamberName} Slot：{waferSrc.WaferNo}\r\n";
            //MessageBoxResult result = MessageBox.Show($"{_strMsg}\r\n请选择机械臂 Nose端【Y】 或 Smooth端【N】,取消【ESC】", "选择", MessageBoxButton.YesNoCancel, MessageBoxImage.Question);
            //if (result == MessageBoxResult.Yes)
            //{
            //    SelectedByArmFetchSide = EnuArmFetchSide.Nose;
            //}
            //else if (result == MessageBoxResult.No)
            //{
            //    SelectedByArmFetchSide = EnuArmFetchSide.Smooth;
            //}
            //else if (result == MessageBoxResult.Cancel)
            //{
            //    return;
            //}
            //currentLogIndex = 1;//日志索引从1开始

            //switch (SelectedByArmFetchSide)
            //{
            //    case EnuArmFetchSide.Nose:

            //        ExcuteResult = await LeftRobotIRArm.TransferWafer(SelectedFromChamber, waferSrc.WaferNo, SelectedToChamber, waferSrc.WaferNo, Cassette, SelectedByArmFetchSide);//out _strMsg
            //        break;

            //    case EnuArmFetchSide.Smooth:

            //        ExcuteResult = await RightRobotIRArm.TransferWafer(SelectedFromChamber, waferSrc.WaferNo, SelectedToChamber, waferSrc.WaferNo, Cassette, SelectedByArmFetchSide);//out _strMsg

            //        break;

            //    case EnuArmFetchSide.Unknow:
            //        MessageBox.Show(EnuArmFetchSide.Unknow.ToString());
            //        break;
            //}

            //if (!ExcuteResult)
            //{
            //    return;
            //}

            // 获取另外一边Wafer对象
            //Wafer waferAnotherSide;

            //if (waferSrc.ChamberWaferSide == EnuChamberWaferSide.LeftWafers)
            //{
            //    waferAnotherSide = sourceUContainer.CurCharber.RightWaferAction.Wafers.Where(w => w.WaferNo == waferSrc.WaferNo).FirstOrDefault();
            //}
            //else
            //{
            //    waferAnotherSide = sourceUContainer.CurCharber.LeftWaferAction.Wafers.Where(w => w.WaferNo == waferSrc.WaferNo).FirstOrDefault();
            //}

            //// 从源 UContainer 中移除 Wafer 对象
            //sourceUContainer.CurCharber.RemoveWafers(1, EnuWaferRemoveMode.specified, waferSrc.WaferNo);
            //// 将 Wafer 对象添加到目标 UContainer 中
            //List<Wafer> wafers = new List<Wafer>();
            //wafers.Add(waferSrc);
            //if (waferAnotherSide != null)
            //{
            //    wafers.Add(waferAnotherSide);
            //}
            //targetUContainer.CurCharber.AddWafers(wafers, out string msg);

            //OnProcessCustom("CUSTOM");

            // 显示源列表父控件和目标列表父控件的 UContainer 实例
            //_strMsg = $"源列表父控件的 UContainer 实例: {sourceUContainer.CurCharber.ChamberName} Slot：{waferSrc.WaferNo}\n" +
            //         $"目标列表父控件的 UContainer 实例: {targetUContainer.CurCharber.ChamberName} Slot：{waferSrc.WaferNo}";
            //HcGrowlExtensions.Info(_strMsg);

            //// 获取源列表的父控件
            //var sourceParent = dropInfo.DragInfo.VisualSource as FrameworkElement;
            //// 获取目标列表的父控件
            //var targetParent = dropInfo.VisualTarget as FrameworkElement;

            //// 获取源列表父控件的名字
            //var sourceParentName = sourceParent?.Name;

            //// 获取目标列表父控件的名字
            //var targetParentName = targetParent?.Name;

            //// 显示源列表父控件和目标列表父控件的名字
            //MessageBox.Show($"源列表父控件的名字: {sourceParentName}\n目标列表父控件的名字: {targetParentName}");

            //MessageBox.Show("Drop");

            //throw new NotImplementedException();
        }

        public static T GetParentOfType<T>(DependencyObject start) where T : DependencyObject
        {
            var parent = VisualTreeHelper.GetParent(start);

            if (parent == null)
                return null;

            if (parent is T correctlyTyped)
            {
                return correctlyTyped;
            }

            return GetParentOfType<T>(parent);
        }

        public void StartDrag(IDragInfo dragInfo)
        {
            // 获取你想要拖动的控件
            var waferStatus = dragInfo.VisualSource as IWaferDragDropInfo;
            if (waferStatus != null)
            {
                var arm = dragInfo.VisualSource as Arm;

                if (arm == null)
                {
                    if (waferStatus.WaferNo > 0)
                    {
                        // 设置dragInfo.Data为这个数据对象
                        dragInfo.Data = waferStatus;
                        dragInfo.Effects = DragDropEffects.Copy | DragDropEffects.Move;
                    }
                }
                else
                {
                    // 判断机械臂是否有Wafer  怎么区分是Nose还是Smooth
                    if (arm.NoseRobotIRArm.HasWaferNo > 0)
                    {
                        // 设置dragInfo.Data为这个数据对象
                        waferStatus.CurCharber = arm.NoseRobotIRArm;
                        waferStatus.WaferNo = arm.NoseRobotIRArm.HasWaferNo;
                    }
                    else if (arm.SmoothRobotIRArm.HasWaferNo > 0)
                    {
                        // 设置dragInfo.Data为这个数据对象
                        waferStatus.CurCharber = arm.SmoothRobotIRArm;
                        waferStatus.WaferNo = arm.SmoothRobotIRArm.HasWaferNo;
                    }

                    dragInfo.Data = waferStatus;
                    dragInfo.Effects = DragDropEffects.Copy | DragDropEffects.Move;
                }
            }
            else
            {
                dragInfo.Effects = DragDropEffects.None;
            }
        }

        public bool CanStartDrag(IDragInfo dragInfo)
        {
            var sourceParent = dragInfo.VisualSource as FrameworkElement;

            // 获取源列表父控件的 UContainer 实例
            var waferSrc = sourceParent as IWaferDragDropInfo;

            //if (waferSrc.CurWaferStatus != Enums.EnuWaferStatus.Have)
            //{
            //    MessageBox.Show($"Wafer:{waferSrc.WaferNo}，【CanStartDrag】没有Wafer，无法拖拽！！！");
            //    return false;
            //}

            //return !IsRunning;
            return true;
        }

        public void Dropped(IDropInfo dropInfo)
        {
            //MessageBox.Show("Dropped");

            //// 获取源列表的父控件
            //var sourceParent = dropInfo.DragInfo.VisualSource as FrameworkElement;
            //// 获取目标列表的父控件
            //var targetParent = dropInfo.VisualTarget as FrameworkElement;

            //// 获取源列表父控件的名字
            //var sourceParentName = sourceParent?.Name;
            //// 获取目标列表父控件的名字
            //var targetParentName = targetParent?.Name;

            //// 显示源列表父控件和目标列表父控件的名字
            //MessageBox.Show($"源列表父控件的名字: {sourceParentName}\n目标列表父控件的名字: {targetParentName}");
        }

        public void DragDropOperationFinished(DragDropEffects operationResult, IDragInfo dragInfo)
        {
            //MessageBox.Show("DragDropOperationFinished");
        }

        public void DragCancelled()
        {
            //MessageBox.Show("DragCancelled");
            //throw new NotImplementedException();
        }

        public bool TryCatchOccurredException(Exception exception)
        {
            return false;

            //MessageBox.Show("TryCatchOccurredException");
            ////throw new NotImplementedException();
            //return true;
        }

        #endregion UI拖拽处理

        #region **************************************** 方法 ****************************************

        /// <summary>
        /// 实例化IR400 UI各配件
        /// </summary>

        /// <summary>
        /// 初始化IR400界面组件
        /// </summary>
        private void IR400Instance()
        {
            // 初始化晶圆集合
            LeftWafers = new ObservableCollection<Wafer>();
            RightWafers = new ObservableCollection<Wafer>();

            Host = new MainHost()
            {
                Name = EnuChamberName.Host,
                EAPCommunicationStatus = EnuEAPCommunicationStatus.Online,
                Mode = EnuMode.Auto,
                WorkStatus = EnuWorkStatus.Run,
                WarnStatus = EnuWarnStatus.NoWarn,
                TimeRemain = new TimeSpan(0, 0, 0, 0),
            };

            Loadlock = new Loadlock()
            {
                Name = EnuChamberName.LoadLock,
                LeftSn = "1234567890AB",
                RightSn = "0987654321AB",
                LeftFinishedProcess = 50,
                RightFinishedProcess = 50,
                Capacity = 1,
                LeftWaferAction = new WaferAction(EnuChamberWaferSide.LeftWafers, 0),
                RightWaferActon = new WaferAction(EnuChamberWaferSide.RightWafers, 0),
                SlitDoorStatus = EnuSlitDoorStatus.Close,
                WorkStatus = EnuWorkStatus.Idle,
                WarnStatus = EnuWarnStatus.NoWarn,
            };

            Buffer = new BufferChamber()
            {
                Name = EnuChamberName.Buffer,
                Capacity = 2,
                LeftWaferAction = new WaferAction(EnuChamberWaferSide.LeftWafers, 0),
                RightWaferActon = new WaferAction(EnuChamberWaferSide.RightWafers, 0),
                WorkStatus = EnuWorkStatus.Idle,
                WarnStatus = EnuWarnStatus.NoWarn,
                ChaSlitDoorStatus = EnuSlitDoorStatus.Close,
                ChbSlitDoorStatus = EnuSlitDoorStatus.Close,
                ChcSlitDoorStatus = EnuSlitDoorStatus.Close,
                LoadLocklitDoorStatus = EnuSlitDoorStatus.Close,
            };

            WaferNoRange = "1-25";

            CurUserInfo = Golbal.CurUser;
            //CurUserInfo = new Base_User { UserName = "Unknow",Password="123456"};

            // 如果需要启动UI动画演示，可以在这里添加代码
            if (_isUIAnimationDemoLoop)
            {
                StartUIAnimationDemo();
            }
        }

        /// <summary>
        /// 启动UI动画演示
        /// </summary>
        private void StartUIAnimationDemo()
        {
            Task.Run(async () =>
            {
                int enuValue = 0;
                int iLoop = 0;

                while (_isUIAnimationDemoLoop)
                {
                    if (Golbal.CtsAppExit.Token.IsCancellationRequested)
                    {
                        break;
                    }

                    await Task.Delay(1000);

                    // 更新腔体状态
                    UpdateChamberStatuses(ref enuValue);

                    // 更新LoadLock状态
                    UpdateLoadLockStatus(ref enuValue);

                    // 防止溢出
                    enuValue = enuValue == int.MaxValue ? 0 : enuValue;
                    iLoop = iLoop == int.MaxValue ? 0 : iLoop;
                }
            }, Golbal.CtsAppExit.Token);
        }

        /// <summary>
        /// 更新腔体状态
        /// </summary>
        /// <param name="enuValue">状态值</param>
        private void UpdateChamberStatuses(ref int enuValue)
        {
            // 更新ChamberA状态
            ChamberA.TimeRemain = DateTime.Now.TimeOfDay;
            ChamberA.StepTimeRemain = DateTime.Now.TimeOfDay;
            ChamberA.WorkStatus = (EnuWorkStatus)(enuValue++ % 5);
            ChamberA.StepProgressPercentage = enuValue % 101;

            // 更新ChamberB状态
            ChamberB.TimeRemain = DateTime.Now.TimeOfDay;
            ChamberB.StepTimeRemain = DateTime.Now.TimeOfDay;
            ChamberB.WorkStatus = (EnuWorkStatus)(enuValue++ % 5);
            ChamberB.StepProgressPercentage = (enuValue + 30) % 101;

            // 更新ChamberC状态
            ChamberC.TimeRemain = DateTime.Now.TimeOfDay;
            ChamberC.StepTimeRemain = DateTime.Now.TimeOfDay;
            ChamberC.WorkStatus = (EnuWorkStatus)(enuValue++ % 5);
            ChamberC.StepProgressPercentage = (enuValue + 60) % 101;

            // 更新Cooling状态
            Cooling.TimeRemain = DateTime.Now.TimeOfDay;
            Cooling.StepTimeRemain = DateTime.Now.TimeOfDay;
            Cooling.WorkStatus = (EnuWorkStatus)(enuValue % 5);
            Cooling.StepProgressPercentage = enuValue % 101;
        }

        /// <summary>
        /// 更新LoadLock状态
        /// </summary>
        /// <param name="enuValue">状态值</param>
        private void UpdateLoadLockStatus(ref int enuValue)
        {
            Loadlock.WorkStatus = (EnuWorkStatus)(enuValue % 5);

            // 更新左侧进度
            if (Loadlock.LeftFinishedProcess < 100)
            {
                Loadlock.LeftFinishedProcess += 5;
            }
            else
            {
                Loadlock.LeftFinishedProcess = 0;
            }

            // 更新右侧进度
            if (Loadlock.RightFinishedProcess < 100)
            {
                Loadlock.RightFinishedProcess += 5;
            }
            else
            {
                Loadlock.RightFinishedProcess = 0;
            }
        }

        /// <summary>
        /// 打开Run配方页面，选择配方后，开始运行
        /// </summary>
        private async Task OpenRunRecipeView()
        {
            try
            {
                DialogParameters param = new()
                {
                    { "key", "key_value" },
                   { "key1", "key1_value" },
                   { "key2", "key2_value" },
                };
                var dialogResult = await dialogHost.ShowDialog(nameof(RunRecipe), param);

                if (dialogResult.Result == ButtonResult.OK)
                {
                    var resultButtonType = dialogResult.Parameters.GetValue<string>("ButtonType");
                    if (!string.IsNullOrEmpty(resultButtonType))
                    {
                        switch (resultButtonType)
                        {
                            case "Run":
                                var tempCurRunRecipeInfo = dialogResult.Parameters.GetValue<DtoRunRecipeInfo>(EAPInfoConstants.TagDialogCurRunRecipeInfo);
                                if (tempCurRunRecipeInfo is null)
                                {
                                    HcGrowlExtensions.Warning("DtoRunRecipeInfo is null");
                                    return;
                                }

                                // 判断当前配方是否在运行，如果在运行，提示是否停止当前配方
                                if (IsRunning)
                                {
                                    MessageBox.Show("当前配方正在运行，请先停止当前配方运行", "配方正在运行", MessageBoxButton.OK, MessageBoxImage.Warning);
                                    return;
                                }

                                CurRunRecipeInfo = tempCurRunRecipeInfo;
                                Golbal.CurGolbalRunRecipeInfo = CurRunRecipeInfo;
                                LoopCount = 1;

                                OnProcessReset();
                                var result = await ProcessLoopCommand();
                                break;

                            case "Stop":
                                await OnProcessStop();
                                break;
                        }
                    }
                }
                else
                {
                    HcGrowlExtensions.Info("Dialog Cancel");
                }
            }
            catch (Exception)
            {
                // ignored
            }
        }

        /// <summary>
        /// 只打开Run配方页面，选择配方后，不开始运行
        /// </summary>
        private async Task<bool> OpenJustRunRecipeView()
        {
            bool blResult = false;
            try
            {
                DialogParameters param = new();
                if (curRemoteRunRecipeInfo != null && curRemoteRunRecipeInfo.IsRemote)
                {
                    param.Add(EAPInfoConstants.TagDialogRemoteRunRecipeInfo, curRemoteRunRecipeInfo);
                }

                IDialogResult dialogResult = null;

                await Application.Current.Dispatcher.Invoke(async () =>
                {
                    // Use the root DialogHost identifier
                    dialogResult = await dialogHost.ShowDialog(nameof(RunRecipe), param, "RootDialog");
                });

                if (dialogResult.Result == ButtonResult.OK)
                {
                    var resultButtonType = dialogResult.Parameters.GetValue<string>("ButtonType");
                    if (!string.IsNullOrEmpty(resultButtonType))
                    {
                        switch (resultButtonType)
                        {
                            case "Run":
                                var tempCurRunRecipeInfo = dialogResult.Parameters.GetValue<DtoRunRecipeInfo>(EAPInfoConstants.TagDialogCurRunRecipeInfo);
                                if (tempCurRunRecipeInfo is null)
                                {
                                    HcGrowlExtensions.Warning("DtoRunRecipeInfo is null");
                                    return false;
                                }

                                // 判断当前配方是否在运行，如果在运行，提示是否停止当前配方
                                if (IsRunning)
                                {
                                    MessageBox.Show("当前配方正在运行，请先停止当前配方运行", "配方正在运行", MessageBoxButton.OK, MessageBoxImage.Warning);
                                    return false;
                                }

                                CurRunRecipeInfo = tempCurRunRecipeInfo;
                                Golbal.CurGolbalRunRecipeInfo = CurRunRecipeInfo;
                                CurSelectedRecipe = CurRunRecipeInfo.RecipeName;

                                // 修复配方名称为空时，无法Sequence运行时加载配方问题
                                if (CurRunRecipeInfo != null)
                                {
                                    CurDbRecipeSeqInfo = CurRecipeName.DbRecipeSeqInfo.FirstOrDefault(t => t.RecipeName == CurSelectedRecipe);
                                }

                                blResult = true;
                                ProcessStartLoad();
                                break;
                        }
                    }
                }
                else
                {
                    HcGrowlExtensions.Info("Dialog Cancel");
                }
            }
            catch (Exception ex)
            {
                AppLog.Error("打开Run配方页面失败", ex);
                UILogService.AddErrorLog(ex.Message);
            }

            return blResult;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            // 停止所有定时器
            _isUIAnimationDemoLoop = false;
            _timer?.Stop();
            _readDataTimer?.Stop();

            // 取消所有任务
            _ctsStartTaskReadPlcLog?.Cancel();
            _autoRunCancellationTokenSource?.Cancel();
            CtsPathPlanning?.Cancel();

            // 清理集合
            AlarmInfos?.Clear();
            FromAvailableWafers?.Clear();
            ToAvailableWafers?.Clear();

            // 释放其他资源
            _ctsStartTaskReadPlcLog?.Dispose();
            _autoRunCancellationTokenSource?.Dispose();
            CtsPathPlanning?.Dispose();
        }

        #endregion **************************************** 方法 ****************************************
    }
}