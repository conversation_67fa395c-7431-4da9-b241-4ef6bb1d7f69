using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using log4net;

namespace Zishan.SS200.Cmd.Services
{
    /// <summary>
    /// 增强的取消令牌服务 - 实现报告中建议的取消令牌支持
    /// 为长时间运行的操作提供统一的取消管理
    /// ✅ 优化报告建议：统一管理取消令牌，避免资源泄漏
    /// </summary>
    public class EnhancedCancellationService : IDisposable
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(EnhancedCancellationService));
        private readonly ConcurrentDictionary<string, CancellationTokenSource> _cancellationSources = new();
        private bool _disposed = false;
        
        /// <summary>
        /// 创建或获取指定操作的取消令牌源
        /// </summary>
        /// <param name="operationId">操作标识</param>
        /// <param name="timeout">超时时间（可选）</param>
        /// <returns>取消令牌源</returns>
        public CancellationTokenSource CreateOrGetCancellationSource(string operationId, TimeSpan? timeout = null)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(EnhancedCancellationService));
                
            if (string.IsNullOrEmpty(operationId))
                throw new ArgumentException("操作标识不能为空", nameof(operationId));
            
            // 如果已存在，先取消并移除旧的
            if (_cancellationSources.TryRemove(operationId, out var existingSource))
            {
                try
                {
                    existingSource.Cancel();
                    existingSource.Dispose();
                    _logger.Debug($"已取消并移除旧的操作: {operationId}");
                }
                catch (Exception ex)
                {
                    _logger.Warn($"取消旧操作时发生错误: {operationId}, {ex.Message}");
                }
            }
            
            // 创建新的取消令牌源
            var newSource = timeout.HasValue 
                ? new CancellationTokenSource(timeout.Value)
                : new CancellationTokenSource();
                
            _cancellationSources.TryAdd(operationId, newSource);
            
            _logger.Debug($"为操作 {operationId} 创建了新的取消令牌源" + 
                         (timeout.HasValue ? $"，超时时间: {timeout.Value}" : ""));
            
            return newSource;
        }
        
        /// <summary>
        /// 获取指定操作的取消令牌
        /// </summary>
        /// <param name="operationId">操作标识</param>
        /// <returns>取消令牌，如果不存在则返回默认令牌</returns>
        public CancellationToken GetCancellationToken(string operationId)
        {
            if (_disposed || string.IsNullOrEmpty(operationId))
                return CancellationToken.None;
                
            return _cancellationSources.TryGetValue(operationId, out var source) 
                ? source.Token 
                : CancellationToken.None;
        }
        
        /// <summary>
        /// 取消指定操作
        /// </summary>
        /// <param name="operationId">操作标识</param>
        /// <returns>是否成功取消</returns>
        public bool CancelOperation(string operationId)
        {
            if (_disposed || string.IsNullOrEmpty(operationId))
                return false;
                
            if (_cancellationSources.TryGetValue(operationId, out var source))
            {
                try
                {
                    if (!source.Token.IsCancellationRequested)
                    {
                        source.Cancel();
                        _logger.Info($"已取消操作: {operationId}");
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"取消操作时发生错误: {operationId}, {ex.Message}", ex);
                }
            }
            
            return false;
        }
        
        /// <summary>
        /// 完成指定操作（清理资源）
        /// </summary>
        /// <param name="operationId">操作标识</param>
        public void CompleteOperation(string operationId)
        {
            if (_disposed || string.IsNullOrEmpty(operationId))
                return;
                
            if (_cancellationSources.TryRemove(operationId, out var source))
            {
                try
                {
                    source.Dispose();
                    _logger.Debug($"已完成并清理操作: {operationId}");
                }
                catch (Exception ex)
                {
                    _logger.Warn($"清理操作资源时发生错误: {operationId}, {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// 取消所有正在进行的操作
        /// </summary>
        public void CancelAllOperations()
        {
            if (_disposed)
                return;
                
            var operationIds = _cancellationSources.Keys;
            foreach (var operationId in operationIds)
            {
                CancelOperation(operationId);
            }
            
            _logger.Info($"已取消所有操作，共 {operationIds.Count} 个");
        }
        
        /// <summary>
        /// 获取当前活动操作数量
        /// </summary>
        public int ActiveOperationsCount => _disposed ? 0 : _cancellationSources.Count;
        
        /// <summary>
        /// 检查指定操作是否已被取消
        /// </summary>
        /// <param name="operationId">操作标识</param>
        /// <returns>是否已取消</returns>
        public bool IsOperationCancelled(string operationId)
        {
            if (_disposed || string.IsNullOrEmpty(operationId))
                return true;
                
            return _cancellationSources.TryGetValue(operationId, out var source) 
                && source.Token.IsCancellationRequested;
        }
        
        /// <summary>
        /// 执行可取消的异步操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operationId">操作标识</param>
        /// <param name="operation">要执行的操作</param>
        /// <param name="timeout">超时时间</param>
        /// <returns>操作结果</returns>
        public async Task<T> ExecuteWithCancellationAsync<T>(
            string operationId,
            Func<CancellationToken, Task<T>> operation,
            TimeSpan? timeout = null)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(EnhancedCancellationService));
                
            var source = CreateOrGetCancellationSource(operationId, timeout);
            
            try
            {
                return await operation(source.Token);
            }
            finally
            {
                CompleteOperation(operationId);
            }
        }
        
        /// <summary>
        /// 执行可取消的异步操作（无返回值）
        /// </summary>
        /// <param name="operationId">操作标识</param>
        /// <param name="operation">要执行的操作</param>
        /// <param name="timeout">超时时间</param>
        public async Task ExecuteWithCancellationAsync(
            string operationId,
            Func<CancellationToken, Task> operation,
            TimeSpan? timeout = null)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(EnhancedCancellationService));
                
            var source = CreateOrGetCancellationSource(operationId, timeout);
            
            try
            {
                await operation(source.Token);
            }
            finally
            {
                CompleteOperation(operationId);
            }
        }
        
        public void Dispose()
        {
            if (_disposed)
                return;
                
            _disposed = true;
            
            try
            {
                CancelAllOperations();
                
                // 清理所有资源
                foreach (var kvp in _cancellationSources)
                {
                    try
                    {
                        kvp.Value.Dispose();
                    }
                    catch (Exception ex)
                    {
                        _logger.Warn($"释放取消令牌源时发生错误: {kvp.Key}, {ex.Message}");
                    }
                }
                
                _cancellationSources.Clear();
                _logger.Info("增强取消令牌服务已释放");
            }
            catch (Exception ex)
            {
                _logger.Error("释放增强取消令牌服务时发生错误", ex);
            }
        }
    }
}
