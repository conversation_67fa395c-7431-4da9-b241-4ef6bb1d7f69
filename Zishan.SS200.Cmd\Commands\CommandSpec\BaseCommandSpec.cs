using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using log4net;
using Newtonsoft.Json;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Config;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.Services.Interfaces;

namespace Zishan.SS200.Cmd.Commands.CommandSpec
{
    /// <summary>
    /// 命令规格基类
    /// </summary>
    /// <typeparam name="TEnum">命令枚举类型</typeparam>
    public abstract class BaseCommandSpec<TEnum> : ICommandSpec where TEnum : Enum
    {
        protected readonly ILog _logger;

        /// <summary>
        /// 命令代码
        /// </summary>
        public string CommandCode { get; }

        /// <summary>
        /// 命令名称
        /// </summary>
        public string CommandName { get; }

        /// <summary>
        /// 命令描述（英文）
        /// </summary>
        public string Description { get; }

        /// <summary>
        /// 命令描述（中文）
        /// </summary>
        public string DescriptionCn { get; }

        /// <summary>
        /// 设备类型
        /// </summary>
        public EnuMcuDeviceType DeviceType { get; }

        /// <summary>
        /// 命令索引
        /// </summary>
        public Enum CommandIndex { get; }

        /// <summary>
        /// 超时时间（毫秒）
        /// </summary>
        public int DefaultTimeout { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="deviceType">设备类型，例如"Robot"、"Shuttle"、"ChamberA"、"ChamberB"</param>
        /// <param name="cmdEnum">命令枚举值</param>
        /// <param name="commandCode">命令代码，例如"AR1"、"S1 SD"</param>
        /// <param name="commandName">命名称</param>
        /// <param name="description">命令中文描述</param>
        /// <param name="descriptionCn">命令中文描述</param>
        ///
        /// <param name="defaultTimeout">默认超时时间</param>
        protected BaseCommandSpec(
            EnuMcuDeviceType deviceType,
            TEnum cmdEnum,
            string commandName,
             string description,
            string descriptionCn,
            int defaultTimeout = 5000)
        {
            DeviceType = deviceType;
            CommandIndex = cmdEnum;
            CommandCode = cmdEnum.ToString();
            CommandName = commandName;
            Description = description;
            DescriptionCn = descriptionCn;
            DefaultTimeout = defaultTimeout;
            _logger = LogManager.GetLogger(GetType());
        }

        /// <summary>
        /// 参数验证，子类可重写添加更多验证逻辑
        /// </summary>
        /// <param name="parameters">参数列表</param>
        /// <returns>是否有效</returns>
        public virtual bool ValidateParameters(List<ushort> parameters)
        {
            // 默认实现，参数可以为空或有值
            return true;
        }

        /// <summary>
        /// 执行前处理，子类可重写添加更多前置逻辑
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="parameters">参数列表</param>
        /// <returns>是否继续执行命令</returns>
        public virtual Task<bool> BeforeExecuteAsync(IS200McuCmdService cmdService, List<ushort> parameters)
        {
            _logger.Info($"开始执行命令: {CommandCode}, 设备: {DeviceType}, 参数: {(parameters != null ? string.Join(",", parameters) : "无")}");

            // 默认返回true，表示继续执行
            return Task.FromResult(true);
        }

        /// <summary>
        /// 执行后处理，子类可重写添加更多后置逻辑
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="result">命令执行结果</param>
        /// <param name="parameters">参数列表</param>
        /// <returns>处理后的结果</returns>
        public virtual Task<(string Response, ushort RunInfo, ushort ReturnInfo)> AfterExecuteAsync(
            IS200McuCmdService cmdService,
            (string Response, ushort RunInfo, ushort ReturnInfo) result,
            List<ushort> parameters)
        {
            _logger.Info($"命令执行完成: {CommandCode}, 结果: {result.Response}");

            // 默认不修改结果
            return Task.FromResult(result);
        }

        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="parameters">参数列表</param>
        /// <param name="timeout">超时时间</param>
        /// <returns>执行结果</returns>
        public virtual async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteAsync(
            IS200McuCmdService cmdService,
            List<ushort> parameters = null,
            int? timeout = null)
        {
            // 参数验证
            if (!ValidateParameters(parameters))
            {
                string errorMsg = $"命令参数验证失败: {CommandCode}, 参数: {string.Join(",", parameters)}";
                _logger.Error(errorMsg);
                return (errorMsg, 0, 0);
            }

            // 执行前处理
            bool shouldContinue = await BeforeExecuteAsync(cmdService, parameters);
            if (!shouldContinue)
            {
                string errorMsg = $"命令执行前处理取消了命令: {CommandCode}";
                _logger.Warn(errorMsg);
                return (errorMsg, 0, 0);
            }

            // 获取设备实例
            // McuDevice device = GetDeviceFromService(cmdService);
            //
            // if (!device.IsConnected)
            // {
            //     string errorMsg = $"{DeviceType}设备未连接，无法执行命令: {CommandCode}";
            //     _logger.Error(errorMsg);
            //     return (errorMsg, 0, 0);
            // }
            //
            // // 执行命令（使用合并后的参数）
            // var actualTimeout = timeout ?? DefaultTimeout;
            // var result = await device.Run((TEnum)CommandIndex, parameters, actualTimeout);

            // 执行后处理
            return await AfterExecuteAsync(cmdService, (shouldContinue.ToString(), 0, 0), parameters);
        }

        /// <summary>
        /// 从命令服务获取设备实例
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <returns>设备实例</returns>
        protected virtual McuDevice GetDeviceFromService(IS200McuCmdService cmdService)
        {
            return DeviceType switch
            {
                EnuMcuDeviceType.Shuttle => cmdService.Shuttle,
                EnuMcuDeviceType.Robot => cmdService.Robot,
                EnuMcuDeviceType.ChamberA => cmdService.ChamberA,
                EnuMcuDeviceType.ChamberB => cmdService.ChamberB,
                _ => throw new ArgumentException($"未知的设备类型: {DeviceType}")
            };
        }
    }
}