using log4net;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.McuCmdIndex;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.Services.Interfaces;

namespace Zishan.SS200.Cmd.Commands.CommandSpec
{
    /// <summary>
    /// 命令管理器类，提供统一的命令执行入口
    /// </summary>
    public class CommandManager
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(CommandManager));
        private readonly IS200McuCmdService _cmdService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        public CommandManager(IS200McuCmdService cmdService)
        {
            _cmdService = cmdService ?? throw new ArgumentNullException(nameof(cmdService));
        }

        /// <summary>
        /// 执行命令 - 自动合并静态参数和动态参数
        /// </summary>
        /// <param name="deviceType">设备类型："Robot", "Shuttle", "ChamberA", "ChamberB"</param>
        /// <param name="commandCode">命令代码，如"AR1", "S1 SD"等</param>
        /// <param name="dynamicParameters">动态参数（可选）</param>
        /// <param name="timeout">超时时间（可选，为null时使用命令默认值）</param>
        /// <returns>命令执行结果</returns>
        public async Task<(string Response, ushort RunInfo, ushort ReturnInfo, long ElapsedMs)> ExecuteCommandAsync(
            EnuMcuDeviceType deviceType,
            string commandCode,
            List<ushort> dynamicParameters = null,
            int? timeout = null)
        {
            // 验证参数
            if (string.IsNullOrWhiteSpace(commandCode))
                throw new ArgumentException("命令代码不能为空", nameof(commandCode));

            try
            {
                // 检查设备连接状态
                McuDevice device = GetDevice(deviceType);
                if (!device.IsConnected)
                {
                    string errorMsg = $"{deviceType}设备未连接，请先连接设备";
                    _logger.Error(errorMsg);
                    return (errorMsg, 0, 0, 0);
                }

                // 记录开始执行命令
                _logger.Info($"开始执行命令: {deviceType}|{commandCode}|动态参数[{(dynamicParameters != null ? string.Join(",", dynamicParameters) : "无")}]");

                // 将命令代码转换为枚举格式（例如S1 SD -> S1_SD）
                string cmdEnum = commandCode.Replace(" ", "_");

                // 查找命令规格
                ICommandSpec commandSpec = null;
                if (CommandSpecManager.Instance.TryGetCommandSpec(commandCode, out commandSpec))
                {
                    // 使用命令规格执行
                    _logger.Debug($"使用命令规格执行: {commandCode}");

                    // 获取命令超时时间
                    int actualTimeout = timeout ?? commandSpec.DefaultTimeout;
                    if (Golbal.IsDevDebug)
                    {
                        actualTimeout = 3000; // 开发调试模式使用短超时
                    }

                    // 执行命令并记录时间
                    var sw = Stopwatch.StartNew();
                    var result = await commandSpec.ExecuteAsync(_cmdService, dynamicParameters, actualTimeout);
                    sw.Stop();

                    // 处理开发调试模式
                    if (Golbal.IsDevDebug)
                    {
                        _logger.Info($"开发调试模式: 模拟成功执行命令 {commandCode}");
                        return ("Success，备注：开发测试跳过，模拟成功，方便调试！", 0, 0, sw.ElapsedMilliseconds);
                    }

                    // 返回执行结果
                    return (result.Response, result.RunInfo, result.ReturnInfo, sw.ElapsedMilliseconds);
                }
                else
                {
                    // 命令规格不存在，使用传统方式执行
                    _logger.Debug($"命令规格不存在，使用传统方式执行: {commandCode}");

                    // 获取设备对象

                    // 获取超时时间（使用默认值）
                    int actualTimeout = timeout ?? 5000;
                    if (Golbal.IsDevDebug)
                    {
                        actualTimeout = 3000; // 开发调试模式使用短超时
                    }

                    // 根据设备类型执行对应的命令
                    var sw = Stopwatch.StartNew();
                    var result = await ExecuteCommandTraditionalAsync(device, deviceType, cmdEnum, dynamicParameters, actualTimeout);
                    sw.Stop();

                    // 处理开发调试模式
                    if (Golbal.IsDevDebug)
                    {
                        _logger.Info($"开发调试模式: 模拟成功执行命令 {commandCode}");
                        return ("Success，备注：开发测试跳过，模拟成功，方便调试！", 0, 0, sw.ElapsedMilliseconds);
                    }

                    // 返回执行结果
                    return (result.Response, result.RunInfo, result.ReturnInfo, sw.ElapsedMilliseconds);
                }
            }
            catch (Exception ex)
            {
                string errorMsg = $"执行命令 {deviceType}|{commandCode} 时发生错误: {ex.Message}";
                _logger.Error(errorMsg, ex);
                return (errorMsg, 0, 0, 0);
            }
        }

        /// <summary>
        /// 使用传统方式执行命令（直接调用设备Run方法）
        /// </summary>
        /// <param name="device">设备对象</param>
        /// <param name="deviceType">设备类型</param>
        /// <param name="cmdEnum">命令枚举名称</param>
        /// <param name="parameters">参数列表</param>
        /// <param name="timeout">超时时间</param>
        /// <returns>命令执行结果</returns>
        private async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteCommandTraditionalAsync(
            McuDevice device,
            EnuMcuDeviceType deviceType,
            string cmdEnum,
            List<ushort> parameters,
            int timeout)
        {
            // 根据设备类型，解析命令枚举并执行
            switch (deviceType)
            {
                case EnuMcuDeviceType.Shuttle:
                    var shuttleCmd = Enum.Parse<EnuShuttleCmdIndex>(cmdEnum);
                    return await device.Run(shuttleCmd, parameters, timeout);

                case EnuMcuDeviceType.Robot:
                    var robotCmd = Enum.Parse<EnuRobotCmdIndex>(cmdEnum);
                    return await device.Run(robotCmd, parameters, timeout);

                case EnuMcuDeviceType.ChamberA:
                    var chaCmd = Enum.Parse<EnuChaCmdIndex>(cmdEnum);
                    return await device.Run(chaCmd, parameters, timeout);

                case EnuMcuDeviceType.ChamberB:
                    var chbCmd = Enum.Parse<EnuChbCmdIndex>(cmdEnum);
                    return await device.Run(chbCmd, parameters, timeout);

                default:
                    throw new ArgumentException($"未知的设备类型: {deviceType}");
            }
        }

        /// <summary>
        /// 执行机器人命令
        /// </summary>
        /// <param name="commandCode">命令代码</param>
        /// <param name="dynamicParameters">动态参数（可选）</param>
        /// <param name="timeout">超时时间（可选）</param>
        /// <returns>命令执行结果</returns>
        public Task<(string Response, ushort RunInfo, ushort ReturnInfo, long ElapsedMs)> ExecuteRobotCommandAsync(
            string commandCode,
            List<ushort> dynamicParameters = null,
            int? timeout = null)
        {
            return ExecuteCommandAsync(EnuMcuDeviceType.Robot, commandCode, dynamicParameters, timeout);
        }

        /// <summary>
        /// 执行Shuttle命令
        /// </summary>
        /// <param name="commandCode">命令代码</param>
        /// <param name="dynamicParameters">动态参数（可选）</param>
        /// <param name="timeout">超时时间（可选）</param>
        /// <returns>命令执行结果</returns>
        public Task<(string Response, ushort RunInfo, ushort ReturnInfo, long ElapsedMs)> ExecuteShuttleCommandAsync(
            string commandCode,
            List<ushort> dynamicParameters = null,
            int? timeout = null)
        {
            return ExecuteCommandAsync(EnuMcuDeviceType.Shuttle, commandCode, dynamicParameters, timeout);
        }

        /// <summary>
        /// 执行工艺腔室A命令
        /// </summary>
        /// <param name="commandCode">命令代码</param>
        /// <param name="dynamicParameters">动态参数（可选）</param>
        /// <param name="timeout">超时时间（可选）</param>
        /// <returns>命令执行结果</returns>
        public Task<(string Response, ushort RunInfo, ushort ReturnInfo, long ElapsedMs)> ExecuteChaCommandAsync(
            string commandCode,
            List<ushort> dynamicParameters = null,
            int? timeout = null)
        {
            return ExecuteCommandAsync(EnuMcuDeviceType.ChamberA, commandCode, dynamicParameters, timeout);
        }

        /// <summary>
        /// 执行工艺腔室B命令
        /// </summary>
        /// <param name="commandCode">命令代码</param>
        /// <param name="dynamicParameters">动态参数（可选）</param>
        /// <param name="timeout">超时时间（可选）</param>
        /// <returns>命令执行结果</returns>
        public Task<(string Response, ushort RunInfo, ushort ReturnInfo, long ElapsedMs)> ExecuteChbCommandAsync(
            string commandCode,
            List<ushort> dynamicParameters = null,
            int? timeout = null)
        {
            return ExecuteCommandAsync(EnuMcuDeviceType.ChamberB, commandCode, dynamicParameters, timeout);
        }

        /// <summary>
        /// 执行命令序列（工艺流程）
        /// </summary>
        /// <param name="commandSequence">命令序列</param>
        /// <param name="stopOnError">发生错误时是否停止</param>
        /// <returns>命令执行结果列表</returns>
        public async Task<List<(string CommandInfo, string Response, ushort RunInfo, ushort ReturnInfo, long ElapsedMs)>> ExecuteCommandSequenceAsync(
            List<(EnuMcuDeviceType DeviceType, string CommandCode, List<ushort> DynamicParameters)> commandSequence,
            bool stopOnError = true)
        {
            var results = new List<(string CommandInfo, string Response, ushort RunInfo, ushort ReturnInfo, long ElapsedMs)>();

            if (commandSequence == null || commandSequence.Count == 0)
            {
                _logger.Warn("命令序列为空，无法执行");
                return results;
            }

            _logger.Info($"开始执行命令序列，共 {commandSequence.Count} 个命令");

            for (int i = 0; i < commandSequence.Count; i++)
            {
                var (deviceType, commandCode, dynamicParameters) = commandSequence[i];

                try
                {
                    _logger.Info($"执行命令序列 [{i + 1}/{commandSequence.Count}]: {deviceType}|{commandCode}");

                    // 执行命令
                    var result = await ExecuteCommandAsync(deviceType, commandCode, dynamicParameters);

                    // 记录结果
                    string commandInfo = $"[{i + 1}/{commandSequence.Count}] {deviceType}|{commandCode}|动态参数[{(dynamicParameters != null ? string.Join(",", dynamicParameters) : "无")}]";
                    results.Add((commandInfo, result.Response, result.RunInfo, result.ReturnInfo, result.ElapsedMs));

                    // 判断是否需要继续执行
                    if (stopOnError && result.ReturnInfo != 0)
                    {
                        _logger.Error($"命令 {deviceType}|{commandCode} 执行失败，中止后续命令执行");
                        break;
                    }
                }
                catch (Exception ex)
                {
                    string errorMsg = $"执行命令 {deviceType}|{commandCode} 时发生错误: {ex.Message}";
                    _logger.Error(errorMsg, ex);

                    string commandInfo = $"[{i + 1}/{commandSequence.Count}] {deviceType}|{commandCode}|动态参数[{(dynamicParameters != null ? string.Join(",", dynamicParameters) : "无")}]";
                    results.Add((commandInfo, errorMsg, 0, 0, 0));

                    if (stopOnError)
                    {
                        _logger.Error("由于错误，中止后续命令执行");
                        break;
                    }
                }
            }

            _logger.Info($"命令序列执行完成，共执行 {results.Count}/{commandSequence.Count} 个命令");
            return results;
        }

        /// <summary>
        /// 获取设备对象
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>设备对象</returns>
        private McuDevice GetDevice(EnuMcuDeviceType deviceType)
        {
            return deviceType switch
            {
                EnuMcuDeviceType.Shuttle => _cmdService.Shuttle,
                EnuMcuDeviceType.Robot => _cmdService.Robot,
                EnuMcuDeviceType.ChamberA => _cmdService.ChamberA,
                EnuMcuDeviceType.ChamberB => _cmdService.ChamberB,
                _ => throw new ArgumentException($"未知的设备类型: {deviceType}")
            };
        }
    }
}