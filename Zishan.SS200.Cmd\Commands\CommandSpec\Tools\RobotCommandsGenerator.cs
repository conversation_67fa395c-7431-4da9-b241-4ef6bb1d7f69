using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using log4net;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Commands.CommandSpec.Tools
{
    /// <summary>
    /// 机器人命令生成器，用于生成机器人命令规格类
    /// </summary>
    public static class RobotCommandsGenerator
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(RobotCommandsGenerator));

        /// <summary>
        /// 机器人命令类别枚举
        /// </summary>
        public enum RobotCommandCategory
        {
            TAxis,      // T轴命令
            RAxis,      // R轴命令
            ZAxis,      // Z轴命令
            WaferStatus, // 晶圆状态命令
            WaferTransfer // 晶圆传输命令
        }

        /// <summary>
        /// 机器人命令模型，扩展CommandInfo类
        /// </summary>
        public class RobotCommandModel : CommandSpecGenerator.CommandInfo
        {
            public RobotCommandCategory Category { get; set; }
            public List<string> DependentCommands { get; set; } = new List<string>();
            public List<string> PositionSensors { get; set; } = new List<string>();
            public Dictionary<string, string> AlarmCodes { get; set; } = new Dictionary<string, string>();
            public string LogicFilePath { get; set; }
        }

        /// <summary>
        /// 从逻辑文件中提取命令信息
        /// </summary>
        /// <param name="filePath">逻辑文件路径</param>
        /// <returns>命令模型</returns>
        public static RobotCommandModel AnalyzeCommandLogicFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"找不到逻辑文件: {filePath}");
                }

                string[] lines = File.ReadAllLines(filePath);
                if (lines.Length < 2)
                {
                    throw new InvalidDataException($"逻辑文件格式不正确: {filePath}");
                }

                string fileName = Path.GetFileNameWithoutExtension(filePath);
                string[] fileNameParts = fileName.Split(new[] { ' ' }, 2);

                string commandCode = fileNameParts[0].Trim();
                string description = fileNameParts.Length > 1 ? fileNameParts[1].Trim() : "";

                var model = new RobotCommandModel
                {
                    DeviceType = EnuMcuDeviceType.Robot,
                    CommandCode = commandCode,
                    CommandName = description,
                    Description = description,
                    DescriptionCn = lines.Length > 1 ? lines[1].Trim() : description,
                    EnumType = "EnuRobotCmd",
                    EnumValue = commandCode,
                    Category = DetermineCommandCategory(description),
                    DefaultTimeout = 30000, // 默认超时时间
                    LogicFilePath = filePath,
                    DependentCommands = ExtractDependentCommands(lines),
                    PositionSensors = ExtractPositionSensors(lines),
                    AlarmCodes = ExtractAlarmCodes(lines)
                };

                return model;
            }
            catch (Exception ex)
            {
                _logger.Error($"分析逻辑文件 {filePath} 时出错: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 确定命令类别
        /// </summary>
        /// <param name="description">命令描述</param>
        /// <returns>命令类别</returns>
        private static RobotCommandCategory DetermineCommandCategory(string description)
        {
            description = description.ToLower();

            if (description.Contains("t-axis") || description.Contains("t axis"))
                return RobotCommandCategory.TAxis;

            if (description.Contains("r-axis") || description.Contains("r axis"))
                return RobotCommandCategory.RAxis;

            if (description.Contains("z-axis") || description.Contains("z axis"))
                return RobotCommandCategory.ZAxis;

            if (description.Contains("wafer status") || description.Contains("status compare"))
                return RobotCommandCategory.WaferStatus;

            if (description.Contains("wafer exchange") || description.Contains("gw") || description.Contains("pw"))
                return RobotCommandCategory.WaferTransfer;

            // 默认返回T轴类别
            return RobotCommandCategory.TAxis;
        }

        /// <summary>
        /// 提取依赖的命令
        /// </summary>
        /// <param name="lines">文件内容行</param>
        /// <returns>依赖命令列表</returns>
        private static List<string> ExtractDependentCommands(string[] lines)
        {
            var dependentCommands = new List<string>();
            var pattern = @"AR\d+";

            foreach (var line in lines)
            {
                // 查找形如"Move_Z_Axis-RPS23"的模式
                if (line.Contains("AR") && line.Contains("-"))
                {
                    var matches = Regex.Matches(line, pattern);
                    foreach (Match match in matches)
                    {
                        string command = match.Value;
                        if (!dependentCommands.Contains(command))
                        {
                            dependentCommands.Add(command);
                        }
                    }
                }
            }

            return dependentCommands;
        }

        /// <summary>
        /// 提取位置和传感器信息
        /// </summary>
        /// <param name="lines">文件内容行</param>
        /// <returns>位置和传感器列表</returns>
        private static List<string> ExtractPositionSensors(string[] lines)
        {
            var sensors = new List<string>();
            var positionPattern = @"R[PS]\d+";
            var sensorPattern = @"SPS\d+";
            var diPattern = @"DI\d+";

            foreach (var line in lines)
            {
                // 提取位置代码
                var posMatches = Regex.Matches(line, positionPattern);
                foreach (Match match in posMatches)
                {
                    string position = match.Value;
                    if (!sensors.Contains(position))
                    {
                        sensors.Add(position);
                    }
                }

                // 提取传感器代码
                var spsMatches = Regex.Matches(line, sensorPattern);
                foreach (Match match in spsMatches)
                {
                    string sensor = match.Value;
                    if (!sensors.Contains(sensor))
                    {
                        sensors.Add(sensor);
                    }
                }

                // 提取DI传感器
                var diMatches = Regex.Matches(line, diPattern);
                foreach (Match match in diMatches)
                {
                    string di = match.Value;
                    if (!sensors.Contains(di))
                    {
                        sensors.Add(di);
                    }
                }
            }

            return sensors;
        }

        /// <summary>
        /// 提取报警代码
        /// </summary>
        /// <param name="lines">文件内容行</param>
        /// <returns>报警代码字典</returns>
        private static Dictionary<string, string> ExtractAlarmCodes(string[] lines)
        {
            var alarmCodes = new Dictionary<string, string>();
            var pattern = @"RA\d+\s+ALARM";

            foreach (var line in lines)
            {
                var match = Regex.Match(line, pattern);
                if (match.Success)
                {
                    string alarmCode = match.Value.Split(' ')[0].Trim();
                    string alarmDesc = line.Trim();

                    if (!alarmCodes.ContainsKey(alarmCode))
                    {
                        alarmCodes.Add(alarmCode, alarmDesc);
                    }
                }
            }

            return alarmCodes;
        }
    }
}