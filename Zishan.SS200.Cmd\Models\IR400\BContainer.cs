﻿using System;
using System.Collections.Generic;
using Prism.Mvvm;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Windows;
using System.Windows.Threading;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber;

namespace Zishan.SS200.Cmd.Models.IR400
{
    /// <summary>
    /// Container 类表示一个容器。
    /// </summary>
    public abstract class BContainer : BindableBase
    {
        #region 字段

        //private readonly ObservableCollection<Wafer> _wafers = new ObservableCollection<Wafer>();

        //private int _CurWaferCount;
        //private int _LeftWaferCount;

        //private bool _HaveWafer;
        //private bool _IsMove;
        //private bool _IsMoveChecked;
        //private bool _IsReplace;
        //private bool _IsReplaceChecked;
        //private bool _IsCreate;
        //private bool _IsCreateChecked;
        //private bool _IsDelete;
        //private bool _IsDeleteChecked;
        //private bool _CanRunCreateOrDeleteCmd;

        private readonly DispatcherTimer _timer;

        /// <summary>
        /// 耗时运行开始时间
        /// </summary>
        private DateTime _AppStartDateTime = DateTime.Now;

        #endregion 字段

        #region 属性

        public string Title { get; set; } = "标题：";

        /// <summary>
        /// R1\L1文本角度
        /// </summary>
        public double TextAngle { get => _TextAngle; set => SetProperty(ref _TextAngle, value); }
        private double _TextAngle = 45;

        /// <summary>
        /// 未知面，针对非机械臂选Unknown
        /// </summary>
        public EnuArmFetchSide ArmFetchSide { get; set; }

        /// <summary>
        /// 腔标题
        /// </summary>
        public EnuChamberName ChamberName { get => _ChamberName; set => SetProperty(ref _ChamberName, value); }
        private EnuChamberName _ChamberName;

        /// <summary>
        /// 腔体容量,默认1
        /// </summary>
        public int Capacity { get; set; }

        /// <summary>
        /// 工艺处理时间
        /// </summary>
        public TimeSpan ProcessTimer { get => _ProcessTimer; set => SetProperty(ref _ProcessTimer, value); }
        private TimeSpan _ProcessTimer;

        /// <summary>
        /// 加工处理时间，单位：ms
        /// </summary>
        public int ProcessingTime { get; set; }

        /// <summary>
        /// 检查腔体是否已经处理完成
        /// </summary>
        public bool IsProcessed { get; set; }

        ///// <summary>
        ///// 左边Wafe展示
        ///// </summary>
        ////public ObservableCollection<Wafer> Wafers
        ////{
        ////    get { return _wafers; }
        ////    //set { _wafers = value; CalWaferAction(); RaisePropertyChanged(); }
        ////}

        ///// <summary>
        ///// 容器当前Wafer数量
        ///// </summary>
        ////public int CurWaferCount
        ////{
        ////    get { return _CurWaferCount; }
        ////    set { _CurWaferCount = value; RaisePropertyChanged(); }
        ////}

        ///// <summary>
        ///// 容器剩余Wafer数量
        ///// </summary>
        //public int RemainWaferCount
        //{
        //    get { return _LeftWaferCount; }
        //    set { _LeftWaferCount = value; RaisePropertyChanged(); }
        //}

        /// <summary>
        /// Robot 单边：Nose或者Smooth是否有Wafer，带UI通知
        /// </summary>
        public bool HasWafer => LeftWaferAction?.HaveWafer == true || RightWaferAction?.HaveWafer == true;

        /// <summary>
        /// 根据左边HaveWaferNo和右边HaveWaferNo判断，获取当前Wafer编号，带UI通知
        /// </summary>
        public int HasWaferNo => LeftWaferAction.HaveWaferNo >= RightWaferAction.HaveWaferNo ? LeftWaferAction.HaveWaferNo : RightWaferAction.HaveWaferNo;

        /// <summary>
        /// 左边Wafer状态
        /// </summary>
        public WaferAction LeftWaferAction
        {
            get => _LeftWaferAction;
            set
            {
                if (_LeftWaferAction != null)
                {
                    _LeftWaferAction.PropertyChanged -= WaferAction_PropertyChanged;
                }

                if (SetProperty(ref _LeftWaferAction, value) && value != null)
                {
                    _LeftWaferAction.PropertyChanged += WaferAction_PropertyChanged;
                }
            }
        }
        private WaferAction _LeftWaferAction;

        /// <summary>
        /// 右边Wafer状态
        /// </summary>
        public WaferAction RightWaferAction
        {
            get => _RightWaferAction;
            set
            {
                if (_RightWaferAction != null)
                {
                    _RightWaferAction.PropertyChanged -= WaferAction_PropertyChanged;
                }

                if (SetProperty(ref _RightWaferAction, value) && value != null)
                {
                    _RightWaferAction.PropertyChanged += WaferAction_PropertyChanged;
                }
            }
        }

        private WaferAction _RightWaferAction;

        private void WaferAction_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(WaferAction.HaveWafer))
            {
                RaisePropertyChanged(nameof(HasWafer));
                RaisePropertyChanged(nameof(HasWaferNo));
            }
        }

        ///// <summary>
        ///// 容器状态信息，传递到前端
        ///// </summary>
        //public WaferAction LeftWaferAction
        //{
        //    get => _LeftWaferAction;
        //    set => _LeftWaferAction = value;
        //}

        ///// <summary>
        ///// 是否有Wafer
        ///// </summary>
        //public bool HaveWafer
        //{
        //    get { return _HaveWafer; }
        //    set
        //    {
        //        _HaveWafer = value;
        //        //JudgeByHaveWafer();
        //        RaisePropertyChanged();
        //    }
        //}

        ///// <summary>
        ///// Wafer是否可移动【前提有Wafer】
        ///// </summary>
        //public bool IsMove
        //{
        //    get { return _IsMove; }
        //    set { _IsMove = value; RaisePropertyChanged(); }
        //}

        //public bool IsMoveChecked
        //{
        //    get { return _IsMoveChecked; }
        //    set { _IsMoveChecked = value; UpdateTime = DateTime.Now; RaisePropertyChanged(); }
        //}

        ///// <summary>
        ///// Wafer是否可替换【前提没有Wafer】
        ///// </summary>
        //public bool IsReplace
        //{
        //    get { return _IsReplace; }
        //    set { _IsReplace = value; RaisePropertyChanged(); }
        //}

        //public bool IsReplaceChecked
        //{
        //    get { return _IsReplaceChecked; }
        //    set { _IsReplaceChecked = value; UpdateTime = DateTime.Now; RaisePropertyChanged(); }
        //}

        ///// <summary>
        ///// Wafer是否可替换【前提没有Wafer】
        ///// </summary>
        //public bool IsCreate
        //{
        //    get { return _IsCreate; }
        //    set { _IsCreate = value; RaisePropertyChanged(); }
        //}

        //public bool IsCreateChecked
        //{
        //    get { return _IsCreateChecked; }
        //    set { _IsCreateChecked = value; CanRunCreateOrDeleteCmd = IsCreateChecked || IsDeleteChecked; RaisePropertyChanged(); }
        //}

        ///// <summary>
        ///// Wafer是否可删除【前提有Wafer】
        ///// </summary>
        //public bool IsDelete
        //{
        //    get { return _IsDelete; }
        //    set { _IsDelete = value; RaisePropertyChanged(); }
        //}

        //public bool IsDeleteChecked
        //{
        //    get { return _IsDeleteChecked; }
        //    set { _IsDeleteChecked = value; CanRunCreateOrDeleteCmd = IsCreateChecked || IsDeleteChecked; RaisePropertyChanged(); }
        //}

        ///// <summary>
        ///// 是否可以运行创建或者删除操作
        ///// </summary>
        //public bool CanRunCreateOrDeleteCmd
        //{
        //    get { return _CanRunCreateOrDeleteCmd; }
        //    set { _CanRunCreateOrDeleteCmd = value; RaisePropertyChanged(); }
        //}

        //public DateTime UpdateTime { get; set; }

        #endregion 属性

        #region 属性【PLC对接信号】

        /// <summary>
        /// 工作模式
        /// </summary>
        public EnuMode Mode { get => _Mode; set => SetProperty(ref _Mode, value); }
        private EnuMode _Mode;

        /// <summary>
        /// SlitDoor门状态
        /// </summary>
        public EnuSlitDoorStatus SlitDoorStatus { get => _SlitDoorStatus; set => SetProperty(ref _SlitDoorStatus, value); }
        private EnuSlitDoorStatus _SlitDoorStatus;

        /// <summary>
        /// 工艺剩余总时间
        /// </summary>
        public TimeSpan TimeRemain { get => _TimeRemain; set => SetProperty(ref _TimeRemain, value); }
        private TimeSpan _TimeRemain = TimeSpan.Zero;

        /// <summary>
        /// 当前运行步骤
        /// </summary>
        public string StepLevel { get => _StepLevel; set => SetProperty(ref _StepLevel, value); }
        private string _StepLevel = "STEP2";

        /// <summary>
        /// 当前运行步骤剩余时间
        /// </summary>
        public TimeSpan StepTimeRemain { get => _StepTimeRemain; set => SetProperty(ref _StepTimeRemain, value); }
        private TimeSpan _StepTimeRemain = TimeSpan.Zero;

        #endregion 属性【PLC对接信号】

        #region 属性基类【PLC对接信号】

        ///// <summary>
        ///// 工作模式【子类派生】
        ///// </summary>
        //public EnuMode Mode { get => _Mode; set => SetProperty(ref _Mode, value); }
        //private EnuMode _Mode;

        /// <summary>
        /// 工作状态
        /// </summary>
        public EnuWorkStatus WorkStatus { get => _WorkStatus; set => SetProperty(ref _WorkStatus, value); }
        private EnuWorkStatus _WorkStatus;

        /// <summary>
        /// 工作Step进度百分比数
        /// </summary>
        public int StepProgressPercentage { get => _StepProgressPercentage; set => SetProperty(ref _StepProgressPercentage, value); }
        private int _StepProgressPercentage;

        /// <summary>
        /// 报警状态
        /// </summary>
        public EnuWarnStatus WarnStatus { get => _WarnStatus; set => SetProperty(ref _WarnStatus, value); }
        private EnuWarnStatus _WarnStatus;

        /// <summary>
        /// LoadLock门状态:是否已经打开
        /// </summary>
        public bool IsOpen { get => _IsOpen; set => SetProperty(ref _IsOpen, value); }
        private bool _IsOpen;

        #endregion 属性基类【PLC对接信号】

        #region 构造函数

        /// <summary>
        /// 容器构造函数
        /// </summary>
        /// <param chamberName="chamberName">名称</param>
        /// <param chamberName="processingTime">加工处理时间，单位：ms</param>
        /// <param chamberName="capacity">Wafer容量</param>
        protected BContainer(EnuChamberName chamberName, int processingTime, int capacity = 1)
        {
            ChamberName = chamberName;
            ProcessingTime = processingTime;
            Capacity = capacity;
            LeftWaferAction = new WaferAction(EnuChamberWaferSide.LeftWafers, capacity);
            RightWaferAction = new WaferAction(EnuChamberWaferSide.RightWafers, capacity);
            CalWaferAction();

            //初始化计时器
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += Timer_Tick;
            _timer.IsEnabled = true;
            _timer.Stop();
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            ProcessTimer = DateTime.Now.Subtract(_AppStartDateTime);
        }

        public void StartTimer()
        {
            _AppStartDateTime = DateTime.Now;
            _timer.Start();
        }

        public void StopTimer()
        {
            ProcessTimer = DateTime.Now.Subtract(_AppStartDateTime);
            _timer.Stop();
        }

        #endregion 构造函数

        #region 方法

        /// <summary>
        /// 计算关联逻辑判断
        /// </summary>
        public void CalWaferAction()
        {
            //var leftWafers = Wafers.Where(w => w.WaferNo % 2 == 1).ToList();
            //LeftWaferAction.Wafers.Clear();
            //LeftWaferAction.Wafers.AddRange(leftWafers);
            LeftWaferAction.CalWaferAction();

            //var rightWafers = Wafers.Where(w => w.WaferNo % 2 == 0).ToList();
            //RightWaferAction.Wafers.Clear();
            //RightWaferAction.Wafers.AddRange(rightWafers);
            RightWaferAction.CalWaferAction();

            //HaveWafer = this.Wafers.Any();
            //IsMove = this.Wafers.Count > 0;//只要有Wafer，不管状态多可以移出
            //if (!IsMove) { IsMoveChecked = false; }
            //IsReplace = !IsFull();//剩余容量>=2多可以移入
            //if (!IsReplace) { IsReplaceChecked = false; }

            //IsCreate = !IsFull();//剩余容量>=2多可以创建
            //if (!IsCreate) { IsCreateChecked = false; }
            //IsDelete = this.Wafers.Count > 0;//只要有Wafer，不管状态多可以删除
            //if (!IsDelete) { IsDeleteChecked = false; }

            //CurWaferCount = Wafers.Count / 2;

            //RemainWaferCount = Capacity - Wafers.Count;

            //CanRunCreateOrDeleteCmd = IsCreate || IsDelete;
            //CanRunCreateOrDeleteCmd = IsCreateChecked || IsDeleteChecked;

            /*
            _LeftWaferAction.HaveWafer = Wafers.Any();
            _LeftWaferAction.JudgeByHaveWafer();

            //_RightWaferAction.HaveWafer = _RightWafer.Any();
            //_RightWaferAction.JudgeByHaveWafer();

            RemainWaferCount = Wafers.Count;
            //RightWaferCount = _RightWafer.Count();
            */
        }

        ///// <summary>
        ///// 关联逻辑判断
        ///// </summary>

        //public void JudgeByHaveWafer()
        //{
        //    IsMove = this.Wafers.Count > 0;//只要有Wafer，不管状态多可以移出
        //    if (!IsMove) { IsMoveChecked = false; }
        //    IsReplace = !IsFull();//剩余容量>=2多可以移入
        //    if (!IsReplace) { IsReplaceChecked = false; }

        //    IsCreate = !IsFull();//剩余容量>=2多可以创建
        //    if (!IsCreate) { IsCreateChecked = false; }
        //    IsDelete = this.Wafers.Count > 0;//只要有Wafer，不管状态多可以删除
        //    if (!IsDelete) { IsDeleteChecked = false; }

        //    //CanRunCreateOrDeleteCmd = IsCreate || IsDelete;
        //    CanRunCreateOrDeleteCmd = IsCreateChecked || IsDeleteChecked;

        //    //_IsMove = HaveWafer;
        //    //_IsReplace = !HaveWafer;

        //    //IsCreate = !HaveWafer;
        //    //_IsDelete = HaveWafer;

        //    //_CanRunCreateOrDeleteCmd = IsCreate || IsDelete;
        //}

        /// <summary>
        /// 检查腔体是否满了:左右任意一边满了就算满
        /// </summary>
        /// <returns></returns>
        public bool IsFull()
        {
            var leftIsFull = LeftWaferAction.IsFull();
            var RightIsFull = RightWaferAction.IsFull();
            return leftIsFull || RightIsFull;
        }

        /// <summary>
        /// 检查腔体是否为空:左右两边都为空才算空
        /// </summary>
        /// <returns></returns>
        public bool IsEmpty()
        {
            var isLeftWaferEmpty = LeftWaferAction.IsEmpty();
            var isRightWaferEmpty = RightWaferAction.IsEmpty();
            return isLeftWaferEmpty && isRightWaferEmpty;
        }

        /// <summary>
        /// 检查腔体是否有未完成的Wafer：左右任意一边有未完成的Wafer就算有
        /// </summary>
        /// <returns></returns>
        public virtual bool HasUnfinishedWafer()
        {
            var leftHasUnfinishedWafer = LeftWaferAction.HasUnfinishedWafer();
            var RightHasUnfinishedWafer = RightWaferAction.HasUnfinishedWafer();
            return leftHasUnfinishedWafer || RightHasUnfinishedWafer;
        }

        /// <summary>
        /// 向腔体中添加晶圆,批量加，最多2个一起加
        /// </summary>
        /// <param chamberName="wafers"></param>
        /// <param chamberName="messageInfo"></param>
        public bool AddWafers(List<Wafer> wafers, out string messageInfo)
        {
            bool blResult = false;
            messageInfo = string.Empty;
            if (!IsFull())
            {
                LeftWaferAction.Wafers.AddRange(wafers.Where(w => w.ChamberWaferSide == EnuChamberWaferSide.LeftWafers).ToList());
                RightWaferAction.Wafers.AddRange(wafers.Where(w => w.ChamberWaferSide == EnuChamberWaferSide.RightWafers).ToList());
                CalWaferAction();
                IsProcessed = false;
                blResult = true;
            }
            else
            {
                messageInfo = $"{ChamberName}容量{Capacity}，当前数量{LeftWaferAction.Wafers.Count + RightWaferAction.Wafers.Count},容量已满了，无法放入!!!";
            }
            return blResult;
        }

        /// <summary>
        /// 从腔体中移出晶圆,有限制，最多2个一起减
        /// </summary>
        /// <param name="moveCount">要移除的晶圆数量，默认为1</param>
        /// <param name="enuWaferRemoveMode">晶圆移除模式，默认为从后部移除</param>
        /// <param name="waferNo">要移除的特定晶圆编号，默认为-1，表示没有特定的晶圆</param>
        /// <returns>被移除的晶圆列表</returns>
        public List<Wafer> RemoveWafers(int moveCount = 1, EnuWaferRemoveMode enuWaferRemoveMode = EnuWaferRemoveMode.Rear, int waferNo = -1)
        {
            var removewafers = new List<Wafer>(moveCount);
            for (int i = 0; i < moveCount && (LeftWaferAction.Wafers.Count > 0 || RightWaferAction.Wafers.Count > 0); i++)
            {
                Wafer leftWafer = null;
                Wafer rightWafer = null;
                int minLeftWaferNo = -1;
                int minRightWaferNo = -1;

                if (enuWaferRemoveMode != EnuWaferRemoveMode.specified)//当waferNo = 0时，按左边、右边最小值
                {
                    if (LeftWaferAction.Wafers.Count > 0)
                    {
                        minLeftWaferNo = LeftWaferAction.Wafers.Min(t => t.WaferNo);
                    }
                    if (RightWaferAction.Wafers.Count > 0)
                    {
                        minRightWaferNo = RightWaferAction.Wafers.Min(t => t.WaferNo);
                    }

                    if (minLeftWaferNo > -1 && minRightWaferNo >= 1 && minLeftWaferNo != minRightWaferNo)
                    {
                        waferNo = minLeftWaferNo < minRightWaferNo ? minLeftWaferNo : minRightWaferNo;
                    }
                    else
                    {
                        waferNo = minLeftWaferNo > minRightWaferNo ? minLeftWaferNo : minRightWaferNo;
                    }
                }
                else
                {
                    //更加WaferNo获取左边、右边Wafer对象
                }

                switch (enuWaferRemoveMode)
                {
                    case EnuWaferRemoveMode.Front:
                        if (waferNo == -1)//一致，正常成对处理
                        {
                            if (LeftWaferAction.Wafers.Count > 0)
                            {
                                leftWafer = LeftWaferAction.Wafers[0];
                                LeftWaferAction.Wafers.RemoveAt(0);
                            }
                            if (RightWaferAction.Wafers.Count > 0)
                            {
                                rightWafer = RightWaferAction.Wafers[0];
                                RightWaferAction.Wafers.RemoveAt(0);
                            }
                        }
                        else//不一致，按waferNo取
                        {
                            leftWafer = LeftWaferAction.Wafers.FirstOrDefault(t => t.WaferNo == waferNo);
                            rightWafer = RightWaferAction.Wafers.FirstOrDefault(t => t.WaferNo == waferNo);

                            if (leftWafer != null)
                            {
                                LeftWaferAction.Wafers.Remove(leftWafer);
                            }
                            if (rightWafer != null)
                            {
                                RightWaferAction.Wafers.Remove(rightWafer);
                            }
                        }
                        break;

                    case EnuWaferRemoveMode.Rear:
                        if (waferNo == -1)//一致，正常成对处理
                        {
                            if (LeftWaferAction.Wafers.Count > 0)
                            {
                                leftWafer = LeftWaferAction.Wafers[^1];
                                LeftWaferAction.Wafers.RemoveAt(LeftWaferAction.Wafers.Count - 1);
                            }
                            if (RightWaferAction.Wafers.Count > 0)
                            {
                                rightWafer = RightWaferAction.Wafers[^1];
                                RightWaferAction.Wafers.RemoveAt(RightWaferAction.Wafers.Count - 1);
                            }
                        }
                        else//不一致，按waferNo取
                        {
                            leftWafer = LeftWaferAction.Wafers.FirstOrDefault(t => t.WaferNo == waferNo);
                            rightWafer = RightWaferAction.Wafers.FirstOrDefault(t => t.WaferNo == waferNo);

                            if (leftWafer != null)
                            {
                                LeftWaferAction.Wafers.Remove(leftWafer);
                            }
                            if (rightWafer != null)
                            {
                                RightWaferAction.Wafers.Remove(rightWafer);
                            }
                        }
                        break;

                    case EnuWaferRemoveMode.specified:
                        leftWafer = LeftWaferAction.Wafers.FirstOrDefault(t => t.WaferNo == waferNo);
                        rightWafer = RightWaferAction.Wafers.FirstOrDefault(t => t.WaferNo == waferNo);

                        if (leftWafer != null)
                        {
                            LeftWaferAction.Wafers.Remove(leftWafer);
                        }
                        if (rightWafer != null)
                        {
                            RightWaferAction.Wafers.Remove(rightWafer);
                        }
                        break;
                }

                if (leftWafer != null)
                {
                    removewafers.Add(leftWafer);
                    if (leftWafer.WaferNo == Golbal.CurLeftWafersId)//判断删除末尾的才可以
                    {
                        Golbal.CurLeftWafersId--;
                    }
                }
                if (rightWafer != null)
                {
                    removewafers.Add(rightWafer);
                    if (rightWafer.WaferNo == Golbal.CurRightWafersId)//判断删除末尾的才可以
                    {
                        Golbal.CurRightWafersId--;
                    }
                }
            }
            CalWaferAction();
            return removewafers;
        }

        /// <summary>
        /// 处理晶圆
        /// </summary>
        /// <returns></returns>
        public virtual bool ProcessWafers()
        {
            if (ProcessingTime > 0)
            {
                Console.WriteLine($"正在等待{ChamberName}处理......");

                Thread.Sleep(ProcessingTime);  // 模拟加工时间
                IsProcessed = true;

                Console.WriteLine($"完成处理{ChamberName}");
            }

            return IsProcessed;
        }

        /// <summary>
        /// 清理腔体中的晶圆
        /// </summary>
        public virtual void ClearWafer()
        {
            Application.Current.Dispatcher.Invoke(() =>
           {
               LeftWaferAction.Wafers.Clear();
               RightWaferAction.Wafers.Clear();
               CalWaferAction();
           });
        }

        /// <summary>
        /// 获取腔体中未完成的晶圆编号1~25：左边晶圆编号+右边晶圆编号
        /// </summary>
        /// <returns></returns>
        public List<int> GetUnFinishededWaferNos()
        {
            var waferNos = new List<int>();
            waferNos.AddRange(LeftWaferAction.Wafers.Where(t => !t.IsFinisheded).Select(t => t.WaferNo));
            waferNos.AddRange(RightWaferAction.Wafers.Where(t => !t.IsFinisheded).Select(t => t.WaferNo));
            waferNos = waferNos.OrderBy(t => t).Distinct().ToList();
            return waferNos;
        }

        /// <summary>
        /// 腔信息
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return $"{ChamberName}【Left={LeftWaferAction.ToString()}；Right={RightWaferAction.ToString()}】";
        }

        #endregion 方法
    }
}