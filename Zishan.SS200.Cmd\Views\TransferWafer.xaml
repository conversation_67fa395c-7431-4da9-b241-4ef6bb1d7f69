<UserControl
    x:Class="Zishan.SS200.Cmd.Views.TransferWafer"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:behavior="clr-namespace:Zishan.SS200.Cmd.Behaviors"
    xmlns:common="clr-namespace:Zishan.SS200.Cmd.Common"
    xmlns:conv="clr-namespace:Zishan.SS200.Cmd.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dvm="clr-namespace:Zishan.SS200.Cmd.ViewModels.DesignViewModels"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:local="clr-namespace:Zishan.SS200.Cmd.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    xmlns:userControls="clr-namespace:Zishan.SS200.Cmd.UserControls"
    xmlns:viewModels="clr-namespace:Zishan.SS200.Cmd.ViewModels"
    xmlns:visualBasic="clr-namespace:Microsoft.VisualBasic;assembly=Microsoft.VisualBasic.Core"
    xmlns:wuext="https://github.com/Monika1313/Wu"
    d:Background="LightBlue"
    d:DataContext="{x:Static dvm:TransferWaferrDesignViewModel.Instance}"
    d:DesignHeight="1290"
    d:DesignWidth="2146"
    prism:ViewModelLocator.AutoWireViewModel="True"
    mc:Ignorable="d">
    <UserControl.Resources>
        <conv:DescriptionConverter x:Key="DescriptionConverter" />
        <conv:AndMultiValueConverter x:Key="AndMultiValueConverter" />
        <conv:RecipeNameToDetailConverter x:Key="RecipeNameToDetailConverter" />
    </UserControl.Resources>

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition />
            <ColumnDefinition Width="5" />
        </Grid.ColumnDefinitions>

        <!--  之前主界面展示隐藏掉，用于手动功能站位，正常使用  -->
        <Grid Background="LightBlue" Visibility="Visible">
            <Grid.RowDefinitions>
                <RowDefinition Height="5*" />
                <RowDefinition Height="5" />
                <RowDefinition Height="auto" />
            </Grid.RowDefinitions>
            <UniformGrid Columns="3" Rows="2">
                <userControls:UContainer CurCharber="{Binding ChamberA}" MaxWafers="{Binding WaferTotalCount}" />
                <userControls:UContainer CurCharber="{Binding ChamberB}" MaxWafers="{Binding WaferTotalCount}" />
                <userControls:UContainer CurCharber="{Binding ChamberC}" MaxWafers="{Binding WaferTotalCount}" />
                <userControls:UContainer CurCharber="{Binding Cassette}" MaxWafers="{Binding WaferTotalCount}" />
                <userControls:UContainer CurCharber="{Binding Cooling}" MaxWafers="{Binding WaferTotalCount}" />

                <!--<Setter Property="Command" Value="{Binding DataContext.DoubleClickCommand, RelativeSource={RelativeSource AncestorType={x:Type ListBox}}}" />
            <Setter Property="CommandParameter" Value="{Binding}" />-->

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="1*" />
                        <RowDefinition Height="5" />
                        <RowDefinition Height="1*" />
                    </Grid.RowDefinitions>
                    <Border
                        Margin="0,5"
                        CornerRadius="5"
                        ToolTip="机械臂Nose端">
                        <userControls:UContainer CurCharber="{Binding LeftRobotIRArm}" MaxWafers="{Binding WaferTotalCount}" />
                    </Border>
                    <GridSplitter
                        Grid.Row="1"
                        Grid.Column="0"
                        Height="5"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Center" />
                    <Border
                        Grid.Row="2"
                        CornerRadius="5"
                        ToolTip="机械臂Smooth端">
                        <userControls:UContainer CurCharber="{Binding RightRobotIRArm}" MaxWafers="{Binding WaferTotalCount}" />
                    </Border>
                </Grid>
            </UniformGrid>
            <GridSplitter
                Grid.Row="1"
                Grid.Column="0"
                Grid.ColumnSpan="2"
                Height="5"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Center" />
            <Grid Grid.Row="2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Border CornerRadius="5">
                    <GroupBox
                        Margin="5,0"
                        Background="Transparent"
                        Header="PLC信号仿真">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <WrapPanel>
                                    <CheckBox
                                        Margin="0,5,0,0"
                                        HorizontalAlignment="Left"
                                        Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=RobotNorthHaseWafer}"
                                        IsChecked="{Binding CurPLcsignalSimulation.RobotNorthHaseWafer}"
                                        IsEnabled="{Binding CurPLcsignalSimulation.CarryStatusCheckBoxEnable}" />

                                    <TextBlock Margin="30,5,0,0" Text="{Binding CurPLcsignalSimulation.SlotNorth, StringFormat='SLOT：{0}'}" />
                                </WrapPanel>
                                <WrapPanel>
                                    <CheckBox
                                        Margin="0,5,0,0"
                                        HorizontalAlignment="Left"
                                        Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=RobotSmothHaseWafer}"
                                        IsChecked="{Binding CurPLcsignalSimulation.RobotSmothHaseWafer}"
                                        IsEnabled="{Binding CurPLcsignalSimulation.CarryStatusCheckBoxEnable}" />

                                    <TextBlock Margin="18,5,0,0" Text="{Binding CurPLcsignalSimulation.SlotSmooth, StringFormat='SLOT：{0}'}" />
                                </WrapPanel>
                            </StackPanel>

                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <WrapPanel>
                                    <CheckBox
                                        Margin="0,5,0,0"
                                        HorizontalAlignment="Left"
                                        Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=ChaHasWafer}"
                                        IsChecked="{Binding CurPLcsignalSimulation.ChaHasWafer}"
                                        IsEnabled="{Binding CurPLcsignalSimulation.CarryStatusCheckBoxEnable}" />
                                    <TextBlock Margin="50,5,0,0" Text="{Binding CurPLcsignalSimulation.SlotCha, StringFormat='SLOT：{0}'}" />
                                </WrapPanel>

                                <WrapPanel>
                                    <CheckBox
                                        Margin="0,5,0,0"
                                        HorizontalAlignment="Left"
                                        Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=ChbHasWafer}"
                                        IsChecked="{Binding CurPLcsignalSimulation.ChbHasWafer}"
                                        IsEnabled="{Binding CurPLcsignalSimulation.CarryStatusCheckBoxEnable}" />
                                    <TextBlock Margin="50,5,0,0" Text="{Binding CurPLcsignalSimulation.SlotChb, StringFormat='SLOT：{0}'}" />
                                </WrapPanel>

                                <WrapPanel>
                                    <CheckBox
                                        Margin="0,5,0,0"
                                        HorizontalAlignment="Left"
                                        Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=ChcHasWafer}"
                                        IsChecked="{Binding CurPLcsignalSimulation.ChcHasWafer}"
                                        IsEnabled="{Binding CurPLcsignalSimulation.CarryStatusCheckBoxEnable}" />
                                    <TextBlock Margin="50,5,0,0" Text="{Binding CurPLcsignalSimulation.SlotChc, StringFormat='SLOT：{0}'}" />
                                </WrapPanel>

                                <WrapPanel>
                                    <CheckBox
                                        Margin="0,5,0,0"
                                        HorizontalAlignment="Left"
                                        Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=CoolingHasWafer}"
                                        IsChecked="{Binding CurPLcsignalSimulation.CoolingHasWafer}"
                                        IsEnabled="{Binding CurPLcsignalSimulation.CarryStatusCheckBoxEnable}" />
                                    <TextBlock Margin="30,5,0,0" Text="{Binding CurPLcsignalSimulation.SlotCooling, StringFormat='SLOT：{0}'}" />
                                </WrapPanel>
                            </StackPanel>
                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                <CheckBox
                                    Margin="30,5,0,0"
                                    HorizontalAlignment="Left"
                                    Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=ChaProcessFinished}"
                                    IsChecked="{Binding CurPLcsignalSimulation.ChaProcessFinished}"
                                    IsEnabled="{Binding CurPLcsignalSimulation.EnableChaProcessFinished}" />
                                <CheckBox
                                    Margin="30,5,0,0"
                                    HorizontalAlignment="Left"
                                    Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=ChbProcessFinished}"
                                    IsChecked="{Binding CurPLcsignalSimulation.ChbProcessFinished}"
                                    IsEnabled="{Binding CurPLcsignalSimulation.EnableChbProcessFinished}" />
                                <CheckBox
                                    Margin="30,5,0,0"
                                    HorizontalAlignment="Left"
                                    Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=ChcProcessFinished}"
                                    IsChecked="{Binding CurPLcsignalSimulation.ChcProcessFinished}"
                                    IsEnabled="{Binding CurPLcsignalSimulation.EnableChcProcessFinished}" />
                                <CheckBox
                                    Margin="30,5,0,0"
                                    HorizontalAlignment="Left"
                                    Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=CoolingProcessFinished}"
                                    IsChecked="{Binding CurPLcsignalSimulation.CoolingProcessFinished, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />

                                <!--  是否启用启用自动点击  -->
                                <CheckBox
                                    Margin="30,5,0,0"
                                    HorizontalAlignment="Left"
                                    Content="{Binding CurPLcsignalSimulation, Converter={StaticResource DescriptionConverter}, ConverterParameter=IsAutoClick}"
                                    IsChecked="{Binding CurPLcsignalSimulation.IsAutoClick}" />
                            </StackPanel>
                        </Grid>
                    </GroupBox>
                </Border>
                <Border Grid.Column="1" CornerRadius="5">
                    <GroupBox Background="Transparent" Header="{Binding Title}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="auto" />
                            </Grid.ColumnDefinitions>
                            <StackPanel HorizontalAlignment="Left">
                                <TextBox
                                    Width="140"
                                    Margin="5,0,0,0"
                                    VerticalAlignment="Center"
                                    hc:InfoElement.Placeholder="定义Wafer数量"
                                    hc:InfoElement.Title="定义Wafer数量:"
                                    hc:InfoElement.TitlePlacement="Left"
                                    hc:TitleElement.HorizontalAlignment="Left"
                                    hc:TitleElement.TitleWidth="100"
                                    wuext:TextBoxExtensions.SelectAllWhenGotFocus="True"
                                    IsEnabled="{Binding IsStopLoopRunning}"
                                    Style="{StaticResource TextBoxExtend}"
                                    Text="{Binding WaferTotalCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                    ToolTip="定义Wafer数量" />

                                <Button
                                    Margin="5,0,0,0"
                                    HorizontalAlignment="Left"
                                    Command="{Binding RunRecipeCommand}"
                                    Content="RunRecipe"
                                    Style="{StaticResource ButtonPrimary.Small}" />
                            </StackPanel>

                            <Grid Grid.Column="1" Margin="10,0,0,0">
                                <Grid.RowDefinitions>
                                    <RowDefinition />
                                    <RowDefinition />
                                    <RowDefinition />
                                    <RowDefinition />
                                    <RowDefinition />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.ColumnSpan="3" Orientation="Horizontal">
                                    <TextBox
                                        Width="190"
                                        VerticalAlignment="Center"
                                        hc:InfoElement.Title="左边条码输入"
                                        hc:InfoElement.TitlePlacement="Left"
                                        hc:TitleElement.HorizontalAlignment="Left"
                                        wuext:TextBoxExtensions.SelectAllWhenGotFocus="True"
                                        Style="{StaticResource TextBoxExtend.Small}"
                                        Text="{Binding CurRunRecipeInfo.LeftBarcode, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                    <TextBox
                                        Width="190"
                                        Margin="5,0,0,0"
                                        VerticalAlignment="Center"
                                        hc:InfoElement.Title="右边条码输入"
                                        hc:InfoElement.TitlePlacement="Left"
                                        hc:TitleElement.HorizontalAlignment="Left"
                                        wuext:TextBoxExtensions.SelectAllWhenGotFocus="True"
                                        Style="{StaticResource TextBoxExtend.Small}"
                                        Text="{Binding CurRunRecipeInfo.RightBarcode, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                </StackPanel>

                                <StackPanel
                                    Grid.Row="1"
                                    Grid.Column="0"
                                    Grid.ColumnSpan="3"
                                    Orientation="Horizontal">
                                    <TextBlock
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Center"
                                        Text="制定配方" />

                                    <ComboBox
                                        MinWidth="110"
                                        Margin="30,3,3,3"
                                        IsEnabled="{Binding IsRunning, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource Boolean2BooleanReConverter}}"
                                        ItemsSource="{Binding RecipeList}"
                                        SelectedItem="{Binding CurSelectedRecipe}"
                                        Style="{StaticResource ComboBox.Small}">
                                        <ComboBox.ItemTemplate>
                                            <DataTemplate>
                                                <StackPanel>
                                                    <TextBlock FontWeight="Bold" Text="{Binding}" />
                                                    <!--<TextBlock FontStyle="Italic" Text="{Binding Description}" />-->
                                                </StackPanel>
                                            </DataTemplate>
                                        </ComboBox.ItemTemplate>
                                    </ComboBox>

                                    <!--  显示选中项的详细信息  -->
                                    <ContentControl Content="{Binding CurSelectedRecipe, Converter={StaticResource RecipeNameToDetailConverter}}">
                                        <ContentControl.ContentTemplate>
                                            <DataTemplate>
                                                <WrapPanel>
                                                    <StackPanel>
                                                        <TextBlock FontWeight="Bold" Text="{Binding ChRecipeName, StringFormat={}CH:{0}, TargetNullValue='腔体配方'}" />
                                                        <TextBlock FontWeight="Bold" Text="{Binding CoolingRecipeName, StringFormat={}CP:{0}, TargetNullValue='Cooling配方'}" />
                                                    </StackPanel>
                                                    <!--  机限多值绑定，小屏幕会挡住，暂时注释掉  -->
                                                    <!--<TextBlock
                                                    VerticalAlignment="Center"
                                                    FontSize="12"
                                                    FontWeight="Light">
                                                    <TextBlock.Text>
                                                            <MultiBinding StringFormat="{}[机限 ChamberA:{0}{1},ChamberB:{2}{3},CHC:{4}{5}]" TargetNullValue="机限">
                                                            <Binding Path="ChaEnable" />
                                                            <Binding Path="ChaOrder" />
                                                            <Binding Path="ChbEnable" />
                                                            <Binding Path="ChbOrder" />
                                                            <Binding Path="ChcEnable" />
                                                            <Binding Path="ChcOrder" />
                                                        </MultiBinding>
                                                    </TextBlock.Text>
                                                </TextBlock>-->
                                                </WrapPanel>
                                            </DataTemplate>
                                        </ContentControl.ContentTemplate>
                                    </ContentControl>
                                    <!--<ComboBox
                                    MinWidth="100"
                                    Margin="30,3,3,3"
                                    ItemsSource="{Binding RecipeList}"
                                    SelectedItem="{Binding CurSelectedRecipe}"
                                    Style="{StaticResource ComboBox.Small}" />-->

                                    <TextBlock
                                        Margin="25,0,0,0"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Center"
                                        Text="Top:" />
                                    <ComboBox
                                        MinWidth="30"
                                        Margin="10,3,3,3"
                                        ItemsSource="{Binding WaferCountList}"
                                        SelectedItem="{Binding CurRunRecipeInfo.Top}"
                                        Style="{StaticResource ComboBox.Small}" />
                                    <TextBlock
                                        Margin="25,0,0,0"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Center"
                                        Text="Bottom:" />
                                    <ComboBox
                                        MinWidth="30"
                                        Margin="10,3,3,3"
                                        ItemsSource="{Binding WaferCountList}"
                                        SelectedItem="{Binding CurRunRecipeInfo.Bottom}"
                                        Style="{StaticResource ComboBox.Small}" />
                                </StackPanel>

                                <StackPanel
                                    Grid.Row="2"
                                    Grid.Column="0"
                                    Grid.ColumnSpan="3"
                                    Margin="0,5"
                                    HorizontalAlignment="Left"
                                    Orientation="Horizontal">

                                    <Button
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Content="Run"
                                        Style="{StaticResource ButtonPrimary.Small}" />
                                    <Button
                                        Margin="20,0,0,0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Content="Stop"
                                        Style="{StaticResource ButtonWarning.Small}" />
                                    <TextBlock Text="{Binding AppConfigInfo}" />
                                </StackPanel>
                            </Grid>

                            <Grid Grid.Column="2">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition />
                                    <ColumnDefinition />
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0">
                                    <CheckBox
                                        Margin="5"
                                        HorizontalAlignment="Left"
                                        Content="是否读取PLC日志"
                                        IsChecked="{Binding IsReadPlcLog, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                    <ToggleButton
                                        HorizontalAlignment="Left"
                                        Command="{Binding ReadPLCCurRunStatusCommand}"
                                        Content="读取PLC运行状态"
                                        Style="{StaticResource ToggleButtonPrimary.Small}" />

                                    <Grid ToolTip="安全提醒：在非运行状态下才可以执行同步PLC运行状态">
                                        <ToggleButton
                                            HorizontalAlignment="Left"
                                            Command="{Binding ReadAndSetPLCCurRunStatusCommand}"
                                            Content="同步PLC运行状态"
                                            IsEnabled="{Binding IsRunning, Converter={StaticResource Boolean2BooleanReConverter}}"
                                            Style="{StaticResource ToggleButtonDefault.Small}" />
                                    </Grid>

                                    <ToggleButton
                                        Margin="0,5,0,0"
                                        HorizontalAlignment="Left"
                                        Command="{Binding DevTestCommand}"
                                        Content="DevTest"
                                        Style="{StaticResource ToggleButtonInfo.Small}" />
                                </StackPanel>
                                <StackPanel Grid.Column="1" Margin="5,0,0,0">
                                    <CheckBox
                                        Margin="5"
                                        HorizontalAlignment="Left"
                                        Content="IsRunning"
                                        IsChecked="{Binding IsRunning, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        IsEnabled="False" />

                                    <CheckBox
                                        Margin="5,10"
                                        HorizontalAlignment="Left"
                                        Content="UI同步PLC运行状态"
                                        IsChecked="{Binding IsSyncPlcCurRunStatus, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                </StackPanel>
                            </Grid>
                        </Grid>
                    </GroupBox>
                </Border>
            </Grid>
        </Grid>

        <!--  主界面展示  -->
        <Grid
            Grid.Row="0"
            Grid.Column="0"
            d:Visibility="Hidden"
            Background="LightBlue"
            Visibility="{Binding IsChecked, Converter={StaticResource Boolean2VisibilityConverter}, ElementName=UseRobotUi}" />

        <GridSplitter
            Grid.Row="0"
            Grid.Column="1"
            Width="5"
            HorizontalAlignment="Center"
            VerticalAlignment="Stretch" />

        <!--  手动模式命令布局  -->
        <GroupBox
            Grid.Row="1"
            Grid.Column="0"
            Grid.ColumnSpan="3"
            Margin="5,10,5,10"
            Background="LightBlue"
            Header="手动模式命令">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="310" />
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition Width="auto" />
                    <ColumnDefinition Width="3*" />
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition Width="1.6*" />
                </Grid.ColumnDefinitions>

                <WrapPanel Grid.Column="0" VerticalAlignment="Center">
                    <ToggleButton
                        Width="60"
                        Command="{Binding ProcessResetCommand}"
                        Content="重置"
                        IsEnabled="{Binding IsReset}"
                        Tag="GVL.sPcControlWord" />

                    <TextBox
                        Width="60"
                        Margin="5,0,0,0"
                        hc:InfoElement.Placeholder="次数"
                        hc:TitleElement.HorizontalAlignment="Right"
                        hc:TitleElement.TitleWidth="40"
                        wuext:TextBoxExtensions.SelectAllWhenGotFocus="True"
                        IsEnabled="{Binding IsStopLoopRunning}"
                        Style="{StaticResource TextBoxExtend}"
                        Text="{Binding LoopCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                        ToolTip="循环次数" />

                    <ToggleButton
                        Margin="5,0,0,0"
                        Command="{Binding ExecuteProcessLoopCommand}"
                        Content="循环"
                        IsEnabled="{Binding IsStopLoopRunning}"
                        Tag="GVL.sPcControlWord"
                        Visibility="{Binding Source={x:Static common:Golbal.IsDevDebug}, Converter={StaticResource BooleanToVisibilityConverter}}" />
                    <ToggleButton
                        Margin="5,0,0,0"
                        Command="{Binding ProcessPauseNewCommand}"
                        Content="{Binding IsEnablePauseContent, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                        IsEnabled="{Binding IsEnablePauseCmd}" />
                    <ToggleButton
                        Margin="5,0,0,0"
                        Command="{Binding ProcessStopCommand}"
                        Content="停止"
                        IsEnabled="{Binding IsEnableStopCmd}"
                        Tag="GVL.sPcControlWord" />
                </WrapPanel>

                <ComboBox
                    Grid.Row="0"
                    Grid.Column="1"
                    hc:InfoElement.Necessary="True"
                    hc:InfoElement.Placeholder="请选择From单元位置："
                    hc:InfoElement.Symbol="*"
                    hc:InfoElement.Title="From单元位置："
                    Cursor="Hand"
                    DisplayMemberPath="ChamberName"
                    ItemsSource="{Binding FromChamber}"
                    SelectedItem="{Binding SelectedFromChamber}"
                    SelectedValuePath="ChamberName"
                    Style="{StaticResource ComboBoxExtend.Small}" />
                <ComboBox
                    Grid.Row="0"
                    Grid.Column="2"
                    d:ItemsSource="{d:SampleData ItemCount=5}"
                    hc:InfoElement.Necessary="True"
                    hc:InfoElement.Placeholder="请选择SLOT："
                    hc:InfoElement.Symbol="*"
                    hc:InfoElement.Title="SLOT："
                    DisplayMemberPath="WaferNo"
                    ItemsSource="{Binding FromAvailableWafers}"
                    SelectedValue="{Binding SelectedFromSlot, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged, FallbackValue=1, TargetNullValue=1}"
                    SelectedValuePath="WaferNo"
                    Style="{StaticResource ComboBoxExtend.Small}" />
                <ComboBox
                    Grid.Row="0"
                    Grid.Column="3"
                    hc:InfoElement.Necessary="True"
                    hc:InfoElement.Placeholder="请选择To单元位置："
                    hc:InfoElement.Symbol="*"
                    hc:InfoElement.Title="To单元位置："
                    Cursor="Hand"
                    DisplayMemberPath="ChamberName"
                    ItemsSource="{Binding ToChamber}"
                    SelectedItem="{Binding SelectedToChamber}"
                    SelectedValuePath="ChamberName"
                    Style="{StaticResource ComboBoxExtend.Small}" />
                <ComboBox
                    Grid.Row="0"
                    Grid.Column="4"
                    d:ItemsSource="{d:SampleData ItemCount=5}"
                    hc:InfoElement.Necessary="True"
                    hc:InfoElement.Placeholder="请选择SLOT："
                    hc:InfoElement.Symbol="*"
                    hc:InfoElement.Title="SLOT："
                    DisplayMemberPath="WaferNo"
                    ItemsSource="{Binding ToAvailableWafers}"
                    SelectedValue="{Binding SelectedToSlot, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged, FallbackValue=1, TargetNullValue=1}"
                    SelectedValuePath="WaferNo"
                    Style="{StaticResource ComboBoxExtend.Small}" />
                <ComboBox
                    Grid.Row="0"
                    Grid.Column="5"
                    hc:InfoElement.Necessary="True"
                    hc:InfoElement.Placeholder="请选择机械臂："
                    hc:InfoElement.Symbol="*"
                    hc:InfoElement.Title="机械臂位置："
                    Cursor="Hand"
                    ItemsSource="{Binding ByArmFetchSide}"
                    SelectedIndex="0"
                    SelectedItem="{Binding SelectedByArmFetchSide}"
                    Style="{StaticResource ComboBoxExtend.Small}" />
                <Grid
                    Grid.Row="0"
                    Grid.Column="6"
                    ToolTip="安全提醒：在手动模式非运行状态下才可以执行手动命令">
                    <!--<ToggleButton
                    Grid.Row="0"
                    Grid.Column="6"
                    Width="60"
                    Margin="10,0"
                    Command="{Binding ProcessCustomCommand}"
                        Content="执行"
                        Tag="GVL.sPcControlWord"
                        ToolTip="安全提醒：在手动模式非运行状态下才可以执行手动命令" />-->
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition />
                        <ColumnDefinition />
                    </Grid.ColumnDefinitions>
                    <Button
                        Margin="10,0"
                        d:Visibility="Visible"
                        Command="{Binding TrasferWaferCommand}"
                        Content="搬运"
                        Style="{StaticResource ButtonPrimary}"
                        Visibility="{Binding Source={x:Static common:Golbal.IsDevDebug}, Converter={StaticResource BooleanToVisibilityConverter}}" />
                </Grid>
                <TextBox
                    Grid.Row="0"
                    Grid.Column="7"
                    Grid.ColumnSpan="99"
                    VerticalAlignment="Stretch"
                    hc:InfoElement.Placeholder="PLC执行状态"
                    hc:InfoElement.Title="执行状态"
                    hc:InfoElement.TitlePlacement="Left"
                    hc:TitleElement.HorizontalAlignment="Right"
                    hc:TitleElement.TitleWidth="80"
                    Style="{StaticResource TextBoxExtend}"
                    Text="{Binding CommandResult}"
                    ToolTip="GVL.CMD_State.CMD_Busy" />

                <!--  上料自动跑过程模拟  -->
                <Grid
                    Grid.Row="2"
                    Grid.Column="0"
                    Grid.ColumnSpan="99"
                    HorizontalAlignment="Stretch">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="488*" />
                        <ColumnDefinition Width="433*" />
                    </Grid.ColumnDefinitions>

                    <StackPanel
                        Margin="0,10,0,0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Top"
                        Orientation="Horizontal">

                        <TextBox
                            Width="100"
                            Margin="5"
                            VerticalAlignment="Center"
                            hc:InfoElement.Placeholder="请输入Robot动画速度，单位：秒"
                            hc:InfoElement.Title="动画速度:"
                            hc:InfoElement.TitlePlacement="Left"
                            hc:TitleElement.HorizontalAlignment="Left"
                            hc:TitleElement.TitleWidth="65"
                            wuext:TextBoxExtensions.SelectAllWhenGotFocus="True"
                            Style="{StaticResource TextBoxExtend}"
                            Text="{Binding MoveSpeed, Mode=TwoWay}"
                            ToolTip="Robot动画速度，单位：秒，默认3秒" />

                        <ComboBox
                            Margin="5"
                            hc:InfoElement.Necessary="True"
                            hc:InfoElement.Placeholder="请选择配方"
                            hc:InfoElement.Symbol="*"
                            hc:InfoElement.Title="配方："
                            DisplayMemberPath="FileName"
                            ItemsSource="{Binding FileRecipeInfoList}"
                            SelectedItem="{Binding SelectFileRecipeInfo}"
                            SelectedValuePath="FileName"
                            Style="{StaticResource ComboBoxExtend.Small}" />

                        <ToggleButton CommandParameter="RecipeManager" Content="配方管理" />
                    </StackPanel>

                    <WrapPanel
                        Grid.Column="1"
                        Grid.ColumnSpan="2"
                        Margin="185,0,0,66"
                        HorizontalAlignment="Left">
                        <GroupBox
                            Margin="5"
                            Header="Robot图形/文字展示"
                            Style="{StaticResource GroupBoxTab}">
                            <StackPanel
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Orientation="Horizontal">

                                <RadioButton
                                    x:Name="UseRobotUi"
                                    Margin="15,0,0,0"
                                    Content="图形"
                                    Cursor="Hand"
                                    IsChecked="False" />

                                <RadioButton
                                    Margin="16,0,0,0"
                                    Content="文字"
                                    Cursor="Hand"
                                    IsChecked="True" />
                            </StackPanel>
                        </GroupBox>
                        <GroupBox
                            Margin="5"
                            Header="LeftCassetteSensor"
                            Style="{StaticResource GroupBoxTab}">
                            <StackPanel
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Orientation="Horizontal">
                                <RadioButton
                                    Margin="15,0,0,0"
                                    Content="None"
                                    Cursor="Hand"
                                    IsChecked="{Binding MyAspenProcess.LeftCassetteSensorStatus.None}" />
                                <RadioButton
                                    Margin="15,0,0,0"
                                    Content="Inserted"
                                    Cursor="Hand"
                                    IsChecked="{Binding MyAspenProcess.LeftCassetteSensorStatus.Inserted}" />

                                <RadioButton
                                    Margin="16,0,0,0"
                                    Content="Removed"
                                    Cursor="Hand"
                                    IsChecked="{Binding MyAspenProcess.LeftCassetteSensorStatus.Removed}" />
                            </StackPanel>
                        </GroupBox>
                        <GroupBox
                            Margin="5"
                            Padding="5"
                            Header="RightCassetteSensor"
                            Style="{StaticResource GroupBoxTab}">
                            <StackPanel
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Orientation="Horizontal">
                                <RadioButton
                                    Margin="15,0,0,0"
                                    Content="None"
                                    Cursor="Hand"
                                    IsChecked="{Binding MyAspenProcess.RightCassetteSensorStatus.None}" />
                                <RadioButton
                                    Margin="15,0,0,0"
                                    Content="Inserted"
                                    Cursor="Hand"
                                    IsChecked="{Binding MyAspenProcess.RightCassetteSensorStatus.Inserted}" />

                                <RadioButton
                                    Margin="16,0,0,0"
                                    Content="Removed"
                                    Cursor="Hand"
                                    IsChecked="{Binding MyAspenProcess.RightCassetteSensorStatus.Removed}" />
                            </StackPanel>
                        </GroupBox>
                        <GroupBox
                            Margin="5"
                            Padding="5"
                            Header="Up Process"
                            Style="{StaticResource GroupBoxTab}">
                            <StackPanel
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Orientation="Horizontal">
                                <RadioButton
                                    Margin="15,0,0,0"
                                    Content="None"
                                    Cursor="Hand"
                                    IsChecked="{Binding MyAspenProcess.ProcessUpCassetteCassette.None}" />
                                <RadioButton
                                    Margin="15,0,0,0"
                                    Content="ProcessStarted"
                                    Cursor="Hand"
                                    IsChecked="{Binding MyAspenProcess.ProcessUpCassetteCassette.ProcessStarted}" />

                                <RadioButton
                                    Margin="16,0,0,0"
                                    Content="UnloadMaterial"
                                    Cursor="Hand"
                                    IsChecked="{Binding MyAspenProcess.ProcessUpCassetteCassette.UnloadMaterial}" />
                            </StackPanel>
                        </GroupBox>
                        <GroupBox
                            Margin="5"
                            Padding="5"
                            Header="Down Process"
                            Style="{StaticResource GroupBoxTab}">
                            <StackPanel
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Orientation="Horizontal">
                                <RadioButton
                                    Margin="15,0,0,0"
                                    Content="None"
                                    Cursor="Hand"
                                    IsChecked="{Binding MyAspenProcess.ProcessDownCassetteCassette.None}" />
                                <RadioButton
                                    Margin="15,0,0,0"
                                    Content="ProcessStarted"
                                    Cursor="Hand"
                                    IsChecked="{Binding MyAspenProcess.ProcessDownCassetteCassette.ProcessStarted}" />

                                <RadioButton
                                    Margin="16,0,0,0"
                                    Content="UnloadMaterial"
                                    Cursor="Hand"
                                    IsChecked="{Binding MyAspenProcess.ProcessDownCassetteCassette.UnloadMaterial}" />
                            </StackPanel>
                        </GroupBox>
                        <Button
                            Margin="15,0"
                            hc:IconElement.Geometry="{StaticResource DropperGeometry}"
                            Command="{Binding SendPrimarySensorCommand}"
                            Content="Execute"
                            Cursor="Hand"
                            Style="{StaticResource ButtonPrimary}"
                            ToolTip="快捷键CTRL+W" />
                    </WrapPanel>
                </Grid>
            </Grid>
        </GroupBox>
    </Grid>
</UserControl>