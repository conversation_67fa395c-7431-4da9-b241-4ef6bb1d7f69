using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using log4net;
using Zishan.SS200.Cmd.Commands.CommandSpec.Tools;

namespace Zishan.SS200.Cmd.Commands.CommandSpec.Tools
{
    /// <summary>
    /// 机器人命令生成器扩展，用于生成命令类文件
    /// </summary>
    public static class RobotCommandsGeneratorEx
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(RobotCommandsGeneratorEx));

        /// <summary>
        /// 生成机器人命令类文件
        /// </summary>
        /// <param name="model">机器人命令模型</param>
        /// <param name="outputDirectory">输出目录</param>
        /// <returns>生成的文件路径</returns>
        public static string GenerateRobotCommandClass(RobotCommandsGenerator.RobotCommandModel model, string outputDirectory)
        {
            try
            {
                if (model == null)
                {
                    throw new ArgumentNullException(nameof(model));
                }

                if (string.IsNullOrWhiteSpace(outputDirectory))
                {
                    throw new ArgumentException("输出目录不能为空", nameof(outputDirectory));
                }

                // 准备目录路径
                string baseDir = Path.Combine(outputDirectory, "Robot");
                string categoryDir = GetCategoryDirectory(baseDir, model.Category);

                // 确保目录存在
                Directory.CreateDirectory(categoryDir);

                // 生成文件名
                string safeCmdCode = model.CommandCode.Replace(" ", "_");
                string fileName = $"{safeCmdCode}Command.cs";
                string filePath = Path.Combine(categoryDir, fileName);

                // 生成类文件内容
                string classContent = GenerateRobotCommandClassContent(model);

                // 写入文件
                File.WriteAllText(filePath, classContent, Encoding.UTF8);

                _logger.Info($"已生成机器人命令类文件: {filePath}");
                return filePath;
            }
            catch (Exception ex)
            {
                _logger.Error($"生成机器人命令类文件失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 批量生成机器人命令类文件
        /// </summary>
        /// <param name="models">机器人命令模型列表</param>
        /// <param name="outputDirectory">输出目录</param>
        /// <returns>生成的文件路径列表</returns>
        public static List<string> GenerateRobotCommandClasses(IEnumerable<RobotCommandsGenerator.RobotCommandModel> models, string outputDirectory)
        {
            var filePaths = new List<string>();

            foreach (var model in models)
            {
                try
                {
                    string filePath = GenerateRobotCommandClass(model, outputDirectory);
                    filePaths.Add(filePath);
                }
                catch (Exception ex)
                {
                    _logger.Error($"生成机器人命令类文件 {model.CommandCode} 失败: {ex.Message}", ex);
                }
            }

            return filePaths;
        }

        /// <summary>
        /// 获取类别对应的目录路径
        /// </summary>
        /// <param name="baseDir">基础目录</param>
        /// <param name="category">命令类别</param>
        /// <returns>类别目录路径</returns>
        private static string GetCategoryDirectory(string baseDir, RobotCommandsGenerator.RobotCommandCategory category)
        {
            switch (category)
            {
                case RobotCommandsGenerator.RobotCommandCategory.TAxis:
                    return Path.Combine(baseDir, "TAxis");
                case RobotCommandsGenerator.RobotCommandCategory.RAxis:
                    return Path.Combine(baseDir, "RAxis");
                case RobotCommandsGenerator.RobotCommandCategory.ZAxis:
                    return Path.Combine(baseDir, "ZAxis");
                case RobotCommandsGenerator.RobotCommandCategory.WaferStatus:
                    return Path.Combine(baseDir, "WaferStatus");
                case RobotCommandsGenerator.RobotCommandCategory.WaferTransfer:
                    return Path.Combine(baseDir, "WaferTransfer");
                default:
                    return baseDir;
            }
        }

        /// <summary>
        /// 获取命令类的基类名称
        /// </summary>
        /// <param name="category">命令类别</param>
        /// <returns>基类名称</returns>
        private static string GetBaseClassName(RobotCommandsGenerator.RobotCommandCategory category)
        {
            switch (category)
            {
                case RobotCommandsGenerator.RobotCommandCategory.TAxis:
                    return "BaseTAxisCommand";
                case RobotCommandsGenerator.RobotCommandCategory.RAxis:
                    return "BaseRAxisCommand";
                case RobotCommandsGenerator.RobotCommandCategory.ZAxis:
                    return "BaseZAxisCommand";
                case RobotCommandsGenerator.RobotCommandCategory.WaferStatus:
                    return "BaseWaferStatusCommand";
                case RobotCommandsGenerator.RobotCommandCategory.WaferTransfer:
                    return "BaseWaferTransferCommand";
                default:
                    return "BaseRobotCommand";
            }
        }

        /// <summary>
        /// 生成机器人命令类文件内容
        /// </summary>
        /// <param name="model">机器人命令模型</param>
        /// <returns>类文件内容</returns>
        private static string GenerateRobotCommandClassContent(RobotCommandsGenerator.RobotCommandModel model)
        {
            string baseClassName = GetBaseClassName(model.Category);
            string className = model.CommandCode + "Command";
            string baseNamespace = $"Zishan.SS200.Cmd.Commands.CommandSpec.Robot";
            string namespace_ = GetNamespaceByCategory(baseNamespace, model.Category);
            string imports = GenerateImports(model);
            string classHeader = GenerateClassHeader(model);
            string constructor = GenerateConstructor(className, model);
            string validateParameters = GenerateValidateParameters(model);
            string beforeExecute = GenerateBeforeExecuteMethod(model);
            string afterExecute = GenerateAfterExecuteMethod(model);

            StringBuilder sb = new StringBuilder();
            sb.AppendLine(imports);
            sb.AppendLine();
            sb.AppendLine($"namespace {namespace_}");
            sb.AppendLine("{");
            sb.AppendLine(classHeader);
            sb.AppendLine($"    public class {className} : {baseClassName}");
            sb.AppendLine("    {");
            sb.AppendLine(constructor);
            sb.AppendLine(validateParameters);
            sb.AppendLine(beforeExecute);
            sb.AppendLine(afterExecute);
            sb.AppendLine("    }");
            sb.AppendLine("}");

            return sb.ToString();
        }

        /// <summary>
        /// 生成命名空间
        /// </summary>
        /// <param name="baseNamespace">基础命名空间</param>
        /// <param name="category">命令类别</param>
        /// <returns>完整命名空间</returns>
        private static string GetNamespaceByCategory(string baseNamespace, RobotCommandsGenerator.RobotCommandCategory category)
        {
            switch (category)
            {
                case RobotCommandsGenerator.RobotCommandCategory.TAxis:
                    return $"{baseNamespace}.TAxis";
                case RobotCommandsGenerator.RobotCommandCategory.RAxis:
                    return $"{baseNamespace}.RAxis";
                case RobotCommandsGenerator.RobotCommandCategory.ZAxis:
                    return $"{baseNamespace}.ZAxis";
                case RobotCommandsGenerator.RobotCommandCategory.WaferStatus:
                    return $"{baseNamespace}.WaferStatus";
                case RobotCommandsGenerator.RobotCommandCategory.WaferTransfer:
                    return $"{baseNamespace}.WaferTransfer";
                default:
                    return baseNamespace;
            }
        }

        /// <summary>
        /// 生成导入语句
        /// </summary>
        /// <param name="model">命令模型</param>
        /// <returns>导入语句</returns>
        private static string GenerateImports(RobotCommandsGenerator.RobotCommandModel model)
        {
            return @"using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using log4net;
using Zishan.SS200.Cmd.Enums.Command;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.Extensions;";
        }

        /// <summary>
        /// 生成类头注释
        /// </summary>
        /// <param name="model">命令模型</param>
        /// <returns>类头注释</returns>
        private static string GenerateClassHeader(RobotCommandsGenerator.RobotCommandModel model)
        {
            return $@"    /// <summary>
    /// {model.CommandCode} - {model.DescriptionCn}
    /// </summary>";
        }

        /// <summary>
        /// 生成构造函数
        /// </summary>
        /// <param name="className">类名</param>
        /// <param name="model">命令模型</param>
        /// <returns>构造函数代码</returns>
        private static string GenerateConstructor(string className, RobotCommandsGenerator.RobotCommandModel model)
        {
            return $@"        /// <summary>
        /// 构造函数
        /// </summary>
        public {className}() : base(
            EnuRobotCmd.{model.EnumValue},
            ""{model.CommandCode}"",
            ""{model.Description}"",
            ""{model.DescriptionCn}"",
            {model.DefaultTimeout})
        {{
        }}";
        }

        /// <summary>
        /// 生成参数验证方法
        /// </summary>
        /// <param name="model">命令模型</param>
        /// <returns>参数验证方法代码</returns>
        private static string GenerateValidateParameters(RobotCommandsGenerator.RobotCommandModel model)
        {
            return @"        /// <summary>
        /// 参数验证
        /// </summary>
        /// <param name=""parameters"">参数列表</param>
        /// <returns>是否有效</returns>
        public override bool ValidateParameters(List<ushort> parameters)
        {
            // 当前命令不需要参数
            return true;
        }";
        }

        /// <summary>
        /// 生成执行前方法
        /// </summary>
        /// <param name="model">命令模型</param>
        /// <returns>执行前方法代码</returns>
        private static string GenerateBeforeExecuteMethod(RobotCommandsGenerator.RobotCommandModel model)
        {
            // 根据模型的不同类别生成不同的前置检查代码
            string positionCheck = "";
            string targetPosition = "";

            // 针对T轴命令添加特定检查
            if (model.Category == RobotCommandsGenerator.RobotCommandCategory.TAxis &&
                model.CommandCode.StartsWith("AR") &&
                int.TryParse(model.CommandCode.Substring(2), out int cmdNum) &&
                cmdNum <= 9)
            {
                // 提取目标位置
                switch (cmdNum)
                {
                    case 1: targetPosition = "RS1"; break;
                    case 2: targetPosition = "RS2"; break;
                    case 3: targetPosition = "RS3"; break;
                    case 4: targetPosition = "RS4"; break;
                    case 5: targetPosition = "RS5"; break;
                    case 6: targetPosition = "RS6"; break;
                    case 7: targetPosition = "RS7"; break;
                    case 8: targetPosition = "RS8"; break;
                    case 9: targetPosition = "RS9"; break;
                    default: targetPosition = "RS1"; break;
                }

                // 使用基类的检查方法
                positionCheck = $@"
            _logger.Info(""开始执行{model.CommandCode}命令前置检查"");
            return await CheckTAxisMovePrerequisitesAsync(cmdService, ""{targetPosition}"");";
            }
            else
            {
                // 默认的前置检查
                positionCheck = $@"
            _logger.Info(""开始执行{model.CommandCode}命令前置检查"");

            // 1. 检查机器人状态
            if (!await CheckRobotStatusAsync(cmdService))
            {{
                return false;
            }}

            // 通过所有前置检查，可以执行命令
            _logger.Info(""{model.CommandCode}命令前置检查通过，准备执行"");
            return await base.BeforeExecuteAsync(cmdService, parameters);";
            }

            return $@"        /// <summary>
        /// 执行前处理
        /// </summary>
        /// <param name=""cmdService"">命令服务</param>
        /// <param name=""parameters"">参数列表</param>
        /// <returns>是否继续执行命令</returns>
        public override async Task<bool> BeforeExecuteAsync(IS200McuCmdService cmdService, List<ushort> parameters)
        {{{positionCheck}
        }}";
        }

        /// <summary>
        /// 生成执行后方法
        /// </summary>
        /// <param name="model">命令模型</param>
        /// <returns>执行后方法代码</returns>
        private static string GenerateAfterExecuteMethod(RobotCommandsGenerator.RobotCommandModel model)
        {
            // 构建依赖命令代码
            StringBuilder dependentCmds = new StringBuilder();
            
            if (model.DependentCommands.Any())
            {
                dependentCmds.AppendLine("                _logger.Info($\"开始执行{model.CommandCode}命令后续流程\");");
                
                foreach (var cmd in model.DependentCommands)
                {
                    dependentCmds.AppendLine($@"
                // 执行{cmd}命令
                _logger.Info(""执行{cmd}命令"");
                var {cmd.ToLower()}Result = await cmdService.ExecuteRobotCommandAsync(""{cmd}"");
                if ({cmd.ToLower()}Result.ReturnInfo != 0)
                {{
                    _logger.Error($""执行{cmd}命令失败: {{{cmd.ToLower()}Result.Response}}"");
                    return (result.Response + "" - {cmd}命令执行失败"", result.RunInfo, 0x{model.CommandCode.Substring(2)}01);
                }}");
                }
                
                dependentCmds.AppendLine($@"
                _logger.Info(""{model.CommandCode}命令流程执行完成"");
                return (result.Response + "" - 命令流程执行完成"", result.RunInfo, 0);");
            }
            else
            {
                dependentCmds.AppendLine($@"                _logger.Info(""{model.CommandCode}命令执行完成"");
                return (result.Response + "" - 命令执行完成"", result.RunInfo, 0);");
            }

            return $@"        /// <summary>
        /// 执行后处理
        /// </summary>
        /// <param name=""cmdService"">命令服务</param>
        /// <param name=""result"">命令执行结果</param>
        /// <param name=""parameters"">参数列表</param>
        /// <returns>处理后的结果</returns>
        public override async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> AfterExecuteAsync(
            IS200McuCmdService cmdService,
            (string Response, ushort RunInfo, ushort ReturnInfo) result,
            List<ushort> parameters)
        {{
            if (result.ReturnInfo == 0)
            {{
{dependentCmds}
            }}
            else
            {{
                _logger.Error($""{model.CommandCode}命令执行失败，错误代码: 0x{{result.ReturnInfo:X4}}"");
            }}

            return await base.AfterExecuteAsync(cmdService, result, parameters);
        }}";
        }
    }
} 