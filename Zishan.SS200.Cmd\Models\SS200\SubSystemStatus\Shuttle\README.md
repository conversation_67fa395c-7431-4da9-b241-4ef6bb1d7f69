# Shuttle子系统状态模型

本目录包含基于SS200规格的Shuttle子系统状态实体类和枚举类型。

## 结构概览

### 主要实体类
- **ShuttleSubsystemStatus.cs**: 封装所有Shuttle状态属性的主要实体类
- **ShuttleSlotStatusManager.cs**: 槽位状态管理器，管理所有槽位的晶圆状态

### 枚举类型

1. **EnuSSC6Config.cs**: SSC6配置模式枚举
   - SMIF (0): SMIF配置模式
   - FIXED (1): FIXED配置模式

2. **EnuShuttleStatus.cs**: Shuttle状态枚举 (MSD1-MSD3)
   - None (0): 未知状态
   - Idle (1): MSD1 - 空闲状态
   - Busy (2): MSD2 - 忙碌状态 (shuttle motion)
   - Alarm (3): MSD3 - 报警状态 (shuttle alarm)

3. **EnuShuttlePositionStatus.cs**: Shuttle位置状态枚举 (SSD1-SSD7)
   - None (0): 未知状态
   - ShuttleUpShuttle1Outer (1): SSD1 - Shuttle上升/Shuttle1外部位置
   - ShuttleUpShuttle2Outer (2): SSD2 - Shuttle上升/Shuttle2外部位置
   - ShuttleDownShuttle1Outer (3): SSD3 - Shuttle下降/Shuttle1外部位置
   - ShuttleDownShuttle2Outer (4): SSD4 - Shuttle下降/Shuttle2外部位置
   - ShuttleUpDownBetweenShuttle1Outer (5): SSD5 - Shuttle上下之间/Shuttle1外部位置
   - ShuttleUpDownBetweenShuttle2Outer (6): SSD6 - Shuttle上下之间/Shuttle2外部位置
   - ShuttleDownRotationBetween (7): SSD7 - Shuttle下降且旋转之间位置

4. **EnuCassetteDoorNestStatus.cs**: 晶圆盒门和巢状态枚举 (SSD8-SSD13)
   - None (0): 未知状态
   - CassetteDoorOpen (1): SSD8 - 晶圆盒门打开
   - CassetteDoorClose (2): SSD9 - 晶圆盒门关闭
   - CassetteDoorBetween (3): SSD10 - 晶圆盒门在上下位置之间
   - CassetteDoorOpenNestRetract (4): SSD11 - 晶圆盒门打开/晶圆盒巢收回 (仅SSC6=SMIF)
   - CassetteNestBetween1 (5): SSD12 - 晶圆盒巢在伸出收回位置之间1 (仅SSC6=SMIF)
   - CassetteNestBetween2 (6): SSD13 - 晶圆盒巢在伸出收回位置之间2 (仅SSC6=SMIF)

5. **EnuShuttleValveStatus.cs**: Shuttle阀门状态枚举 (SSD14-SSD23)
   - None (0): 未知状态
   - Open (1): 阀门打开
   - Close (2): 阀门关闭

6. **EnuLotStatus.cs**: 批次状态枚举 (LSD1-LSD4)
   - NoLot (0): 无批次
   - HasLot (1): 有批次

7. **EnuSlotWaferStatus.cs**: 槽位晶圆状态枚举 (LSS1-LSS16, LSSW1-LSSW4)
   - NoWafer (0): 无晶圆
   - HasWafer (1): 有晶圆
   - WaferUnknown (2): 晶圆未知 (仅适用于LSS1-LSS8)

## 状态码映射

### Shuttle状态 (MSD1-MSD3)
- MSD1: 空闲 → EnuShuttleStatus.Idle
- MSD2: 忙碌 → EnuShuttleStatus.Busy
- MSD3: 报警 → EnuShuttleStatus.Alarm

### Shuttle位置状态 (SSD1-SSD7)
- SSD1-SSD7: 各种位置状态 → EnuShuttlePositionStatus
- 注意：SSD1~SSD7状态不能共存

### 晶圆盒门和巢状态 (SSD8-SSD13)
- SSD8-SSD13: 门和巢的组合状态 → EnuCassetteDoorNestStatus
- 注意：SSD8~SSD13状态不能共存，且根据SSC6配置有所不同

### 阀门状态 (SSD14-SSD23)
- SSD14-SSD15: ShuttleISO阀门 → EnuShuttleValveStatus (ShuttleIsoValveStatus)
- SSD16-SSD17: ShuttleXV阀门 → EnuShuttleValveStatus (ShuttleXvValveStatus)
- SSD18-SSD19: Shuttle回填阀门 → EnuShuttleValveStatus (ShuttleBackfillValveStatus)
- SSD20-SSD21: 负载锁排气阀门 → EnuShuttleValveStatus (LoadlockBleedValveStatus)
- SSD22-SSD23: 负载锁回填阀门 → EnuShuttleValveStatus (LoadlockBackfillValveStatus)

### 批次状态 (LSD1-LSD4)
- LSD1: Shuttle1晶圆盒1 → EnuLotStatus (Shuttle1Cassette1LotStatus)
- LSD2: Shuttle1晶圆盒2 → EnuLotStatus (Shuttle1Cassette2LotStatus)
- LSD3: Shuttle2晶圆盒1 → EnuLotStatus (Shuttle2Cassette1LotStatus)
- LSD4: Shuttle2晶圆盒2 → EnuLotStatus (Shuttle2Cassette2LotStatus)

### 槽位状态 (LSS1-LSS16)
- LSS1-LSS4: Shuttle晶圆盒槽位 → Dictionary<int, EnuSlotWaferStatus>
- LSS5-LSS8: Robot端槽位 → Dictionary<int, EnuSlotWaferStatus>
- LSS9-LSS16: 腔体槽位 → EnuSlotWaferStatus

### 晶圆状态 (LSSW1-LSSW4)
- LSSW1-LSSW4: Shuttle晶圆盒实际晶圆状态 → Dictionary<int, EnuSlotWaferStatus>

## 特殊配置说明

### SSC6配置影响

#### SSC6=SMIF模式
- **位置状态 (SSD3-SSD6)**: 包含cassette nest相关的I/O条件（DI4, DI5）
- **门和巢状态 (SSD8-SSD13)**: 全部6个状态都有效，包含晶圆盒巢的状态信息

#### SSC6=FIXED模式
- **位置状态 (SSD3-SSD6)**: 不包含cassette nest相关条件
- **门状态 (SSD8-SSD10)**: 仅3个门状态有效，只包含晶圆盒门状态
- **SSD11-SSD13**: 标记为N/A（不适用）

### 状态互斥性
- **SSD1~SSD7**: Shuttle位置状态不能共存
- **SSD8~SSD13**: 晶圆盒门和巢状态不能共存

## 使用方法

```csharp
var shuttleStatus = new ShuttleSubsystemStatus();

// 设置SSC6配置模式
shuttleStatus.Ssc6Config = EnuSSC6Config.SMIF; // 或 EnuSSC6Config.FIXED

// 设置基本状态
shuttleStatus.ShuttleStatus = EnuShuttleStatus.Idle;
shuttleStatus.ShuttlePositionStatus = EnuShuttlePositionStatus.ShuttleUpShuttle1Outer;
shuttleStatus.Shuttle1Cassette1LotStatus = EnuLotStatus.HasLot;

// 设置门和巢状态（根据SSC6配置）
if (shuttleStatus.Ssc6Config == EnuSSC6Config.SMIF)
{
    // SMIF模式下可以使用所有SSD8-SSD13状态
    shuttleStatus.CassetteDoorNestStatus = EnuCassetteDoorNestStatus.CassetteNestBetween1;
}
else
{
    // FIXED模式下只能使用SSD8-SSD10状态
    shuttleStatus.CassetteDoorNestStatus = EnuCassetteDoorNestStatus.CassetteDoorOpen;
}

// 访问槽位状态
shuttleStatus.SlotStatusManager.Shuttle1Cassette1SlotStatus[1] = EnuSlotWaferStatus.HasWafer;
shuttleStatus.SlotStatusManager.ChalWaferStatus = EnuSlotWaferStatus.NoWafer;
```
