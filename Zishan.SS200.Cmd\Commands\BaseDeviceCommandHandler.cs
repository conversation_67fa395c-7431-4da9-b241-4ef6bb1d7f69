using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.Models;
using log4net;
using NModbus;
using Zishan.SS200.Cmd.Enums.McuCmdIndex;

namespace Zishan.SS200.Cmd.Commands
{
    /// <summary>
    /// 设备命令处理器基类
    /// </summary>
    /// <typeparam name="TDevice">设备类型</typeparam>
    /// <typeparam name="TCommand">命令枚举类型</typeparam>
    public abstract class BaseDeviceCommandHandler<TDevice, TCommand> : IDeviceCommandHandler<TDevice, TCommand>
        where TDevice : Services.McuDevice
        where TCommand : Enum
    {
        protected readonly Services.IModbusClientService _modbusClientService;
        protected readonly ILog _logger;

        protected BaseDeviceCommandHandler(Services.IModbusClientService modbusClientService, ILog logger)
        {
            _modbusClientService = modbusClientService;
            _logger = logger;
        }

        public virtual async Task<TaskHandleResult> ExecuteCommandAsync(TDevice device, TCommand command, ushort[] parameters = null)
        {
            try
            {
                if (!device.IsConnected)
                {
                    _logger.Error($"设备 {device.DeviceType} 未连接");
                    return TaskHandleResult.Failed;
                }

                if (!ValidateParameters(command, parameters))
                {
                    _logger.Error($"命令参数验证失败: {command}");
                    return TaskHandleResult.Failed;
                }

                var master = _modbusClientService.Master;
                if (master == null)
                {
                    _logger.Error($"获取Modbus主站失败: {device.DeviceType}");
                    return TaskHandleResult.Failed;
                }

                var taskHandler = new Models.CmdTaskHandlel(
                    new List<ushort> { 0x0030 }, // CMD Start Address
                    new List<ushort> { 0x0031 }  // CMD Parameter Start Address
                );

                var cmdParameters = parameters != null ? new List<ushort>(parameters) : new List<ushort>();
                var result = await taskHandler.ExecuteCommandAsync(
                    master,
                    device.SlaveId,
                    (EnuShuttleCmdIndex)(object)command,
                    cmdParameters
                );

                return (TaskHandleResult)(int)result;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行命令失败: {ex.Message}");
                return TaskHandleResult.Failed;
            }
        }

        public virtual bool ValidateParameters(TCommand command, ushort[] parameters)
        {
            // 默认实现，子类可以重写此方法添加特定的参数验证逻辑
            return true;
        }
    }
}