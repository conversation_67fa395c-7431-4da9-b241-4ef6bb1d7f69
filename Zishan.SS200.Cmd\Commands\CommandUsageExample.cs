using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.Services;
using log4net;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Enums.McuCmdIndex;

namespace Zishan.SS200.Cmd.Commands
{
    /// <summary>
    /// 命令使用示例
    /// </summary>
    public class CommandUsageExample
    {
        private readonly DeviceCommandFactory _commandFactory;
        private readonly S200McuCmdService _s200McuCmdService;
        private readonly ILog _logger;

        public CommandUsageExample(IModbusClientService modbusClientService, ILog logger)
        {
            _logger = logger;
            _commandFactory = new DeviceCommandFactory(modbusClientService, logger);
            _s200McuCmdService = new S200McuCmdService(); // 使用无参构造函数
        }

        /// <summary>
        /// 获取所有设备
        /// </summary>
        /// <returns>设备字典</returns>
        private Dictionary<EnuMcuDeviceType, McuDevice> GetAllDevices()
        {
            return new Dictionary<EnuMcuDeviceType, McuDevice>
            {
                { EnuMcuDeviceType.Shuttle, _s200McuCmdService.Shuttle },
                { EnuMcuDeviceType.Robot, _s200McuCmdService.Robot },
                { EnuMcuDeviceType.ChamberA, _s200McuCmdService.ChamberA },
                { EnuMcuDeviceType.ChamberB, _s200McuCmdService.ChamberB }
            };
        }

        /// <summary>
        /// 执行Shuttle命令示例
        /// </summary>
        /// <returns>执行结果</returns>
        public async Task<TaskHandleResult> RunShuttleCommandExample()
        {
            try
            {
                // 获取设备
                var devices = GetAllDevices();
                if (!devices.TryGetValue(EnuMcuDeviceType.Shuttle, out var shuttleDevice) || !shuttleDevice.IsConnected)
                {
                    _logger.Error("Shuttle设备未连接");
                    return TaskHandleResult.Failed;
                }

                // 创建命令处理器
                var shuttleCommandHandler = _commandFactory.CreateCommandHandler<EnuShuttleCmdIndex>(EnuMcuDeviceType.Shuttle);

                // 执行命令
                var result = await shuttleCommandHandler.ExecuteCommandAsync(shuttleDevice, EnuShuttleCmdIndex.S1_SD, null);
                _logger.Info($"执行Shuttle命令结果: {result}");

                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行Shuttle命令失败: {ex.Message}");
                return TaskHandleResult.Failed;
            }
        }

        /// <summary>
        /// 执行Robot命令示例
        /// </summary>
        /// <returns>执行结果</returns>
        public async Task<TaskHandleResult> RunRobotCommandExample()
        {
            try
            {
                // 获取设备
                var devices = GetAllDevices();
                if (!devices.TryGetValue(EnuMcuDeviceType.Robot, out var robotDevice) || !robotDevice.IsConnected)
                {
                    _logger.Error("Robot设备未连接");
                    return TaskHandleResult.Failed;
                }

                // 创建命令处理器
                var robotCommandHandler = _commandFactory.CreateCommandHandler<EnuRobotCmdIndex>(EnuMcuDeviceType.Robot);

                // 执行命令
                var result = await robotCommandHandler.ExecuteCommandAsync(robotDevice, EnuRobotCmdIndex.MoveMotor, null);
                _logger.Info($"执行Robot命令结果: {result}");

                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行Robot命令失败: {ex.Message}");
                return TaskHandleResult.Failed;
            }
        }

        /// <summary>
        /// 执行Cha命令示例
        /// </summary>
        /// <returns>执行结果</returns>
        public async Task<TaskHandleResult> RunChaCommandExample()
        {
            try
            {
                // 获取设备
                var devices = GetAllDevices();
                if (!devices.TryGetValue(EnuMcuDeviceType.ChamberA, out var chaDevice) || !chaDevice.IsConnected)
                {
                    _logger.Error("Cha设备未连接");
                    return TaskHandleResult.Failed;
                }

                // 创建命令处理器
                var chaCommandHandler = _commandFactory.CreateCommandHandler<EnuChaCmdIndex>(EnuMcuDeviceType.ChamberA);

                // 执行命令
                var result = await chaCommandHandler.ExecuteCommandAsync(chaDevice, EnuChaCmdIndex.OD_SD, null);
                _logger.Info($"执行Cha命令结果: {result}");

                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行Cha命令失败: {ex.Message}");
                return TaskHandleResult.Failed;
            }
        }
    }
}