using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using log4net;

namespace Zishan.SS200.Cmd.Commands.CommandSpec.Tools
{
    /// <summary>
    /// 机器人命令生成器主程序
    /// </summary>
    public static class RobotCommandsGeneratorProgram
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(RobotCommandsGeneratorProgram));

        /// <summary>
        /// 程序入口
        /// </summary>
        /// <param name="args">命令行参数</param>
        private static void GenerateRobotCommandsMain(string[] args)
        {
            try
            {
                // 获取逻辑文件目录和输出目录
                string logicFolderPath = DetermineLogicFolderPath(args);
                string outputDirectory = DetermineOutputDirectory(args);

                _logger.Info($"开始生成机器人命令类，逻辑文件目录: {logicFolderPath}，输出目录: {outputDirectory}");

                // 1. 分析所有命令逻辑文件
                var commandModels = new List<RobotCommandsGenerator.RobotCommandModel>();
                foreach (var file in Directory.GetFiles(logicFolderPath, "AR*.txt"))
                {
                    try
                    {
                        var model = RobotCommandsGenerator.AnalyzeCommandLogicFile(file);
                        commandModels.Add(model);
                        _logger.Info($"成功分析命令: {model.CommandCode} - {model.Description}");
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"分析文件 {file} 时出错: {ex.Message}");
                    }
                }

                _logger.Info($"共分析 {commandModels.Count} 个命令逻辑文件");

                // 2. 构建命令依赖图
                var dependencyGraph = BuildCommandDependencyGraph(commandModels);
                _logger.Info($"构建命令依赖图完成，共 {dependencyGraph.Count} 个命令有依赖关系");

                // 3. 生成命令类文件
                var filePaths = RobotCommandsGeneratorEx.GenerateRobotCommandClasses(commandModels, outputDirectory);
                _logger.Info($"成功生成 {filePaths.Count} 个机器人命令类文件");

                Console.WriteLine($"机器人命令生成完成，共生成 {filePaths.Count} 个命令类文件");
            }
            catch (Exception ex)
            {
                _logger.Error($"生成机器人命令类时出错: {ex.Message}", ex);
                Console.WriteLine($"生成机器人命令类时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 确定逻辑文件目录
        /// </summary>
        /// <param name="args">命令行参数</param>
        /// <returns>逻辑文件目录</returns>
        private static string DetermineLogicFolderPath(string[] args)
        {
            if (args != null && args.Length > 0 && !string.IsNullOrWhiteSpace(args[0]))
            {
                return args[0];
            }

            // 默认逻辑文件目录
            return Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory,
                "Docs",
                "logic",
                "robot system logic SS-200"
            );
        }

        /// <summary>
        /// 确定输出目录
        /// </summary>
        /// <param name="args">命令行参数</param>
        /// <returns>输出目录</returns>
        private static string DetermineOutputDirectory(string[] args)
        {
            if (args != null && args.Length > 1 && !string.IsNullOrWhiteSpace(args[1]))
            {
                return args[1];
            }

            // 默认输出目录
            return Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory,
                "Commands",
                "CommandSpec"
            );
        }

        /// <summary>
        /// 构建命令依赖图
        /// </summary>
        /// <param name="models">命令模型列表</param>
        /// <returns>命令依赖图</returns>
        private static Dictionary<string, List<string>> BuildCommandDependencyGraph(
            List<RobotCommandsGenerator.RobotCommandModel> models)
        {
            var graph = new Dictionary<string, List<string>>();

            foreach (var model in models)
            {
                if (model.DependentCommands.Any())
                {
                    graph[model.CommandCode] = model.DependentCommands;
                }
            }

            return graph;
        }

        /// <summary>
        /// 执行命令生成
        /// </summary>
        /// <param name="logicFolderPath">逻辑文件目录</param>
        /// <param name="outputDirectory">输出目录</param>
        public static void GenerateRobotCommands(string logicFolderPath, string outputDirectory)
        {
            string[] args = new[] { logicFolderPath, outputDirectory };
            GenerateRobotCommandsMain(args);
        }
    }
} 