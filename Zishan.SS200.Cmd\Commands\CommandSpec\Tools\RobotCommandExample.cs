using System;
using System.IO;

namespace Zishan.SS200.Cmd.Commands.CommandSpec.Tools
{
    /// <summary>
    /// 机器人命令生成器使用示例
    /// </summary>
    public static class RobotCommandExample
    {
        /// <summary>
        /// 演示如何生成机器人命令
        /// </summary>
        public static void GenerateRobotCommandsExample()
        {
            Console.WriteLine("开始生成机器人命令示例...");

            // 1. 设置逻辑文件目录和输出目录
            string logicFolderPath = Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory,
                "Docs",
                "logic",
                "robot system logic SS-200"
            );

            string outputDirectory = Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory,
                "Commands",
                "CommandSpec"
            );

            // 2. 调用生成器程序
            RobotCommandsGeneratorProgram.GenerateRobotCommands(logicFolderPath, outputDirectory);

            Console.WriteLine("机器人命令示例生成完成！");
        }

        /// <summary>
        /// 演示如何手动分析单个命令
        /// </summary>
        public static void AnalyzeSingleCommandExample()
        {
            Console.WriteLine("开始分析单个命令示例...");

            // 1. 设置逻辑文件路径
            string logicFilePath = Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory,
                "Docs",
                "logic",
                "robot system logic SS-200",
                "AR1  T-axis smooth to ChamberA.txt"
            );

            // 2. 分析命令逻辑文件
            var model = RobotCommandsGenerator.AnalyzeCommandLogicFile(logicFilePath);

            // 3. 显示分析结果
            Console.WriteLine($"命令代码: {model.CommandCode}");
            Console.WriteLine($"命令描述: {model.Description}");
            Console.WriteLine($"命令类别: {model.Category}");
            Console.WriteLine($"依赖命令: {string.Join(", ", model.DependentCommands)}");
            Console.WriteLine($"位置传感器: {string.Join(", ", model.PositionSensors)}");
            Console.WriteLine($"报警代码: {string.Join(", ", model.AlarmCodes.Keys)}");

            // 4. 生成单个命令类文件
            string outputDirectory = Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory,
                "Commands",
                "CommandSpec"
            );

            string filePath = RobotCommandsGeneratorEx.GenerateRobotCommandClass(model, outputDirectory);
            Console.WriteLine($"命令类文件已生成: {filePath}");

            Console.WriteLine("单个命令分析示例完成！");
        }

        /// <summary>
        /// 将R轴命令基类和Z轴命令基类添加到示例中
        /// </summary>
        public static void CreateAxisCommandBaseClasses()
        {
            Console.WriteLine("创建轴命令基类示例...");

            string outputDirectory = Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory,
                "Commands",
                "CommandSpec",
                "Robot"
            );

            // 确保目录存在
            Directory.CreateDirectory(Path.Combine(outputDirectory, "RAxis"));
            Directory.CreateDirectory(Path.Combine(outputDirectory, "ZAxis"));

            // 创建R轴命令基类
            string rAxisBaseClassPath = Path.Combine(outputDirectory, "RAxis", "BaseRAxisCommand.cs");
            File.WriteAllText(rAxisBaseClassPath, GenerateRAxisBaseClass());
            Console.WriteLine($"R轴命令基类已创建: {rAxisBaseClassPath}");

            // 创建Z轴命令基类
            string zAxisBaseClassPath = Path.Combine(outputDirectory, "ZAxis", "BaseZAxisCommand.cs");
            File.WriteAllText(zAxisBaseClassPath, GenerateZAxisBaseClass());
            Console.WriteLine($"Z轴命令基类已创建: {zAxisBaseClassPath}");

            Console.WriteLine("轴命令基类创建完成！");
        }

        /// <summary>
        /// 生成R轴命令基类代码
        /// </summary>
        /// <returns>R轴命令基类代码</returns>
        private static string GenerateRAxisBaseClass()
        {
            return @"using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums.Command;
using Zishan.SS200.Cmd.Services.Interfaces;

namespace Zishan.SS200.Cmd.Commands.CommandSpec.Robot.RAxis
{
    /// <summary>
    /// R轴命令基类，提供R轴相关的通用方法和属性
    /// </summary>
    public abstract class BaseRAxisCommand : BaseRobotCommand
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name=""cmdEnum"">命令枚举值</param>
        /// <param name=""commandName"">命令名称</param>
        /// <param name=""description"">命令英文描述</param>
        /// <param name=""descriptionCn"">命令中文描述</param>
        /// <param name=""defaultTimeout"">默认超时时间</param>
        protected BaseRAxisCommand(
            EnuRobotCmd cmdEnum,
            string commandName,
            string description,
            string descriptionCn,
            int defaultTimeout = 30000)
            : base(cmdEnum, commandName, description, descriptionCn, defaultTimeout)
        {
        }

        /// <summary>
        /// 检查R轴位置
        /// </summary>
        /// <param name=""cmdService"">命令服务</param>
        /// <param name=""targetPosition"">目标位置代码</param>
        /// <returns>检查结果：是否已在目标位置</returns>
        protected virtual async Task<bool> CheckRAxisPositionAsync(IS200McuCmdService cmdService, string targetPosition)
        {
            _logger.Info($""检查R轴是否在位置 {targetPosition}"");
            bool isAtPosition = await CheckPositionAsync(cmdService, targetPosition);

            if (isAtPosition)
            {
                _logger.Info($""R轴当前已在{targetPosition}位置，命令完成"");
                return true; // 已经在目标位置
            }

            return false; // 不在目标位置，需要继续执行
        }

        /// <summary>
        /// 检查R轴移动的前置条件
        /// </summary>
        /// <param name=""cmdService"">命令服务</param>
        /// <param name=""targetPosition"">目标位置代码</param>
        /// <returns>是否满足前置条件</returns>
        protected virtual async Task<bool> CheckRAxisMovePrerequisitesAsync(
            IS200McuCmdService cmdService,
            string targetPosition)
        {
            // 1. 检查机器人状态
            if (!await CheckRobotStatusAsync(cmdService))
            {
                return false;
            }

            // 2. 检查是否已在目标位置
            if (await CheckRAxisPositionAsync(cmdService, targetPosition))
            {
                return false; // 已在目标位置，不需要执行
            }

            // 3. 其他通用检查...

            return true; // 满足所有前置条件
        }

        /// <summary>
        /// 执行R轴移动到指定位置
        /// </summary>
        /// <param name=""cmdService"">命令服务</param>
        /// <param name=""positionCode"">位置代码</param>
        /// <param name=""steps"">步进值</param>
        /// <param name=""startSlope"">起始斜率</param>
        /// <param name=""endSlope"">终止斜率</param>
        /// <param name=""current"">运行电流</param>
        /// <returns>执行结果</returns>
        protected virtual async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> MoveRAxisToPositionAsync(
            IS200McuCmdService cmdService,
            string positionCode,
            int steps,
            int? startSlope = null,
            int? endSlope = null,
            int? current = null)
        {
            _logger.Info($""执行R轴移动到{positionCode}位置"");

            // 使用基类方法，直接传递参数
            if (startSlope.HasValue && endSlope.HasValue && current.HasValue)
            {
                return await ExecuteRobotCommandAsync(
                    cmdService,
                    ""Move_R_Axis"",
                    steps,
                    (ushort)startSlope.Value,
                    (ushort)endSlope.Value,
                    (ushort)current.Value);
            }
            else if (startSlope.HasValue && endSlope.HasValue)
            {
                return await ExecuteRobotCommandAsync(
                    cmdService,
                    ""Move_R_Axis"",
                    steps,
                    (ushort)startSlope.Value,
                    (ushort)endSlope.Value);
            }
            else if (startSlope.HasValue)
            {
                return await ExecuteRobotCommandAsync(
                    cmdService,
                    ""Move_R_Axis"",
                    steps,
                    (ushort)startSlope.Value);
            }
            else
            {
                return await ExecuteRobotCommandAsync(cmdService, ""Move_R_Axis"", steps);
            }
        }
    }
}";
        }

        /// <summary>
        /// 生成Z轴命令基类代码
        /// </summary>
        /// <returns>Z轴命令基类代码</returns>
        private static string GenerateZAxisBaseClass()
        {
            return @"using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums.Command;
using Zishan.SS200.Cmd.Services.Interfaces;

namespace Zishan.SS200.Cmd.Commands.CommandSpec.Robot.ZAxis
{
    /// <summary>
    /// Z轴命令基类，提供Z轴相关的通用方法和属性
    /// </summary>
    public abstract class BaseZAxisCommand : BaseRobotCommand
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name=""cmdEnum"">命令枚举值</param>
        /// <param name=""commandName"">命令名称</param>
        /// <param name=""description"">命令英文描述</param>
        /// <param name=""descriptionCn"">命令中文描述</param>
        /// <param name=""defaultTimeout"">默认超时时间</param>
        protected BaseZAxisCommand(
            EnuRobotCmd cmdEnum,
            string commandName,
            string description,
            string descriptionCn,
            int defaultTimeout = 30000)
            : base(cmdEnum, commandName, description, descriptionCn, defaultTimeout)
        {
        }

        /// <summary>
        /// 检查Z轴位置
        /// </summary>
        /// <param name=""cmdService"">命令服务</param>
        /// <param name=""targetPosition"">目标位置代码</param>
        /// <returns>检查结果：是否已在目标位置</returns>
        protected virtual async Task<bool> CheckZAxisPositionAsync(IS200McuCmdService cmdService, string targetPosition)
        {
            _logger.Info($""检查Z轴是否在位置 {targetPosition}"");
            bool isAtPosition = await CheckPositionAsync(cmdService, targetPosition);

            if (isAtPosition)
            {
                _logger.Info($""Z轴当前已在{targetPosition}位置，命令完成"");
                return true; // 已经在目标位置
            }

            return false; // 不在目标位置，需要继续执行
        }

        /// <summary>
        /// 检查Z轴移动的前置条件
        /// </summary>
        /// <param name=""cmdService"">命令服务</param>
        /// <param name=""targetPosition"">目标位置代码</param>
        /// <returns>是否满足前置条件</returns>
        protected virtual async Task<bool> CheckZAxisMovePrerequisitesAsync(
            IS200McuCmdService cmdService,
            string targetPosition)
        {
            // 1. 检查机器人状态
            if (!await CheckRobotStatusAsync(cmdService))
            {
                return false;
            }

            // 2. 检查是否已在目标位置
            if (await CheckZAxisPositionAsync(cmdService, targetPosition))
            {
                return false; // 已在目标位置，不需要执行
            }

            // 3. 其他通用检查...

            return true; // 满足所有前置条件
        }

        /// <summary>
        /// 执行Z轴移动到指定位置
        /// </summary>
        /// <param name=""cmdService"">命令服务</param>
        /// <param name=""positionCode"">位置代码</param>
        /// <param name=""steps"">步进值</param>
        /// <param name=""startSlope"">起始斜率</param>
        /// <param name=""endSlope"">终止斜率</param>
        /// <param name=""current"">运行电流</param>
        /// <returns>执行结果</returns>
        protected virtual async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> MoveZAxisToPositionAsync(
            IS200McuCmdService cmdService,
            string positionCode,
            int steps,
            int? startSlope = null,
            int? endSlope = null,
            int? current = null)
        {
            _logger.Info($""执行Z轴移动到{positionCode}位置"");

            // 使用基类方法，直接传递参数
            if (startSlope.HasValue && endSlope.HasValue && current.HasValue)
            {
                return await ExecuteRobotCommandAsync(
                    cmdService,
                    ""Move_Z_Axis"",
                    steps,
                    (ushort)startSlope.Value,
                    (ushort)endSlope.Value,
                    (ushort)current.Value);
            }
            else if (startSlope.HasValue && endSlope.HasValue)
            {
                return await ExecuteRobotCommandAsync(
                    cmdService,
                    ""Move_Z_Axis"",
                    steps,
                    (ushort)startSlope.Value,
                    (ushort)endSlope.Value);
            }
            else if (startSlope.HasValue)
            {
                return await ExecuteRobotCommandAsync(
                    cmdService,
                    ""Move_Z_Axis"",
                    steps,
                    (ushort)startSlope.Value);
            }
            else
            {
                return await ExecuteRobotCommandAsync(cmdService, ""Move_Z_Axis"", steps);
            }
        }
    }
}";
        }
    }
}