using System;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.Services;
using log4net;
using Zishan.SS200.Cmd.Enums.McuCmdIndex;

namespace Zishan.SS200.Cmd.Commands
{
    /// <summary>
    /// Robot设备命令处理器
    /// </summary>
    public class RobotCommandHandler : BaseDeviceCommandHandler<McuDevice, EnuRobotCmdIndex>
    {
        public RobotCommandHandler(IModbusClientService modbusClientService, ILog logger)
            : base(modbusClientService, logger)
        {
        }

        public override bool ValidateParameters(EnuRobotCmdIndex command, ushort[] parameters)
        {
            // 根据具体命令添加参数验证逻辑
            switch (command)
            {
                case EnuRobotCmdIndex.MoveMotor:
                case EnuRobotCmdIndex.AlarmReset:
                case EnuRobotCmdIndex.MotorStop:
                case EnuRobotCmdIndex.Move_T_Axis:
                case EnuRobotCmdIndex.Move_R_Axis:
                case EnuRobotCmdIndex.Move_Z_Axis:
                case EnuRobotCmdIndex.PinSearch:

                case EnuRobotCmdIndex.AR1:
                case EnuRobotCmdIndex.AR2:
                case EnuRobotCmdIndex.AR3:
                case EnuRobotCmdIndex.AR4:
                case EnuRobotCmdIndex.AR5:
                case EnuRobotCmdIndex.AR6:
                case EnuRobotCmdIndex.AR8:
                case EnuRobotCmdIndex.AR9:
                case EnuRobotCmdIndex.AR11:
                case EnuRobotCmdIndex.AR12:
                case EnuRobotCmdIndex.AR13:
                case EnuRobotCmdIndex.AR14:
                    // 这些命令不需要参数
                    return (parameters != null || parameters.Length > 0);

                default:
                    return false;
            }
        }
    }
}