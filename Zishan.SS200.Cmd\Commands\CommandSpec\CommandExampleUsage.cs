using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Services.Interfaces;

namespace Zishan.SS200.Cmd.Commands.CommandSpec
{
    /// <summary>
    /// 命令规格使用示例类，展示如何在代码中使用命令规格
    /// </summary>
    public class CommandExampleUsage
    {
        private readonly IS200McuCmdService _mcuCmdService;
        private readonly ILog _logger;
        private readonly CommandManager _commandManager;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="mcuCmdService">命令服务</param>
        /// <param name="logger">日志记录器</param>
        public CommandExampleUsage(IS200McuCmdService mcuCmdService, ILog logger)
        {
            _mcuCmdService = mcuCmdService ?? throw new ArgumentNullException(nameof(mcuCmdService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _commandManager = new CommandManager(_mcuCmdService);
        }

        /// <summary>
        /// 示例1：使用命令代码直接执行命令
        /// </summary>
        /// <returns>执行结果</returns>
        public async Task<string> Example1_ExecuteCommandByCode()
        {
            try
            {
                _logger.Info("示例1：使用命令代码直接执行命令");

                // 使用扩展方法执行命令
                var result = await _mcuCmdService.ExecuteCommandAsync("AR1");

                _logger.Info($"AR1命令执行结果：{result.Response}");
                return result.Response;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行示例1时发生错误：{ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 示例2：使用命令代码和参数执行命令
        /// </summary>
        /// <returns>执行结果</returns>
        public async Task<string> Example2_ExecuteCommandWithParameters()
        {
            try
            {
                _logger.Info("示例2：使用命令代码和参数执行命令");

                // 准备参数
                var dynamicParamList = new List<ushort> { 10, 20, 30 };

                // 使用扩展方法执行命令，并传递参数
                var result = await _mcuCmdService.ExecuteCommandAsync("S7 TS1", dynamicParamList);

                _logger.Info($"S7 TS1命令执行结果：{result.Response}");
                return result.Response;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行示例2时发生错误：{ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 示例3：使用命令代码执行命令，并返回JSON格式结果
        /// </summary>
        /// <returns>JSON格式结果</returns>
        public async Task<string> Example3_ExecuteCommandWithJsonResult()
        {
            try
            {
                _logger.Info("示例3：使用命令代码执行命令，并返回JSON格式结果");

                // 使用扩展方法执行命令，并返回JSON格式结果
                var json = await _mcuCmdService.ExecuteCommandJsonAsync("AR1");

                _logger.Info($"AR1命令的JSON结果：{json}");
                return json;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行示例3时发生错误：{ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 示例4：手动查找并使用命令规格
        /// </summary>
        /// <returns>执行结果</returns>
        public async Task<string> Example4_ManuallyFindAndUseCommandSpec()
        {
            try
            {
                _logger.Info("示例4：手动查找并使用命令规格");

                // 手动查找命令规格
                if (CommandSpecManager.Instance.TryGetCommandSpec("AR1", out var commandSpec))
                {
                    // 使用命令规格执行命令
                    var result = await commandSpec.ExecuteAsync(_mcuCmdService);

                    _logger.Info($"AR1命令执行结果：{result.Response}");
                    return result.Response;
                }
                else
                {
                    string errorMsg = "找不到AR1命令规格";
                    _logger.Error(errorMsg);
                    return errorMsg;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"执行示例4时发生错误：{ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 示例5：获取所有机器人命令并执行
        /// </summary>
        /// <returns>执行结果</returns>
        public async Task<string> Example5_GetAllRobotCommandsAndExecute()
        {
            try
            {
                _logger.Info("示例5：获取所有机器人命令并执行");

                // 获取所有机器人命令规格
                var robotCommands = _mcuCmdService.GetDeviceCommandSpecs("Robot").ToList();

                // 记录机器人命令数量
                _logger.Info($"找到 {robotCommands.Count} 个机器人命令");

                // 找到AR1命令并执行
                foreach (var cmd in robotCommands)
                {
                    if (cmd.CommandCode == "AR1")
                    {
                        var result = await cmd.ExecuteAsync(_mcuCmdService);

                        _logger.Info($"AR1命令执行结果：{result.Response}");
                        return result.Response;
                    }
                }

                string errorMsg = "找不到AR1命令规格";
                _logger.Error(errorMsg);
                return errorMsg;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行示例5时发生错误：{ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 示例6：创建特定任务的命令序列
        /// </summary>
        /// <returns>执行结果</returns>
        public async Task<List<string>> Example6_CreateCommandSequence()
        {
            try
            {
                _logger.Info("示例6：创建特定任务的命令序列");

                var results = new List<string>();

                // 模拟一个完整的工艺流程：打开工艺腔室A、机器人移动到工艺腔室A、Shuttle下降

                // 1. 执行ASP1命令（打开工艺腔室A）
                var asp1Result = await _mcuCmdService.ExecuteCommandAsync("ASP1");
                results.Add($"ASP1: {asp1Result.Response}");
                _logger.Info($"ASP1命令执行结果：{asp1Result.Response}");

                // 2. 执行AR1命令（机器人移动到工艺腔室A）
                var ar1Result = await _mcuCmdService.ExecuteCommandAsync("AR1");
                results.Add($"AR1: {ar1Result.Response}");
                _logger.Info($"AR1命令执行结果：{ar1Result.Response}");

                // 3. 执行S1 SD命令（Shuttle下降）
                var s1Result = await _mcuCmdService.ExecuteCommandAsync("S1 SD");
                results.Add($"S1 SD: {s1Result.Response}");
                _logger.Info($"S1 SD命令执行结果：{s1Result.Response}");

                return results;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行示例6时发生错误：{ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 示例8：比较不同参数执行相同命令的结果
        /// </summary>
        /// <returns>执行结果</returns>
        public async Task<Dictionary<string, string>> Example8_CompareParameterExecutionResults()
        {
            try
            {
                _logger.Info("示例8：比较不同参数执行相同命令的结果");

                var results = new Dictionary<string, string>();

                // 1. 使用原始静态参数执行命令
                var result1 = await _mcuCmdService.ExecuteCommandAsync("AR1");
                results.Add("仅使用静态参数", result1.Response);
                _logger.Info($"使用静态参数执行结果: {result1.Response}");

                // 2. 使用动态参数覆盖部分静态参数
                var dynamicParams1 = new List<ushort> { 2, 80 }; // 覆盖位置和速度参数
                var result2 = await _mcuCmdService.ExecuteCommandAsync("AR1", dynamicParams1);
                results.Add($"动态参数[{string.Join(",", dynamicParams1)}]", result2.Response);
                _logger.Info($"使用动态参数[{string.Join(",", dynamicParams1)}]执行结果: {result2.Response}");

                // 3. 使用完全不同的动态参数
                var dynamicParams2 = new List<ushort> { 3, 150, 10 }; // 完全覆盖静态参数
                var result3 = await _mcuCmdService.ExecuteCommandAsync("AR1", dynamicParams2);
                results.Add($"动态参数[{string.Join(",", dynamicParams2)}]", result3.Response);
                _logger.Info($"使用动态参数[{string.Join(",", dynamicParams2)}]执行结果: {result3.Response}");

                // 记录比较结果
                _logger.Info("参数比较执行完成");
                return results;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行示例8时发生错误：{ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 示例10：创建多个设备命令的工艺流程
        /// </summary>
        /// <returns>执行结果</returns>
        public async Task<List<string>> Example10_CreateMultiDeviceProcess()
        {
            try
            {
                _logger.Info("示例10：创建多个设备命令的工艺流程");

                var results = new List<string>();

                // 定义工艺流程中使用的命令及其动态参数
                var processSteps = new List<(string CommandCode, List<ushort> DynamicParams)>
                {
                    // 1. 打开工艺腔室A门
                    ("ASP1", null),

                    // 2. 机器人T轴移动到工艺腔室A - 使用动态参数覆盖部分静态参数
                    ("AR1", new List<ushort> { 1, 150 }),  // 位置参数保持1，速度参数改为150

                    // 3. Shuttle下降
                    ("S1 SD", null),

                    // 4. 获取晶圆（假设命令，根据实际情况调整）
                    ("AR44", new List<ushort> { 1 }),  // 假设1表示从工艺腔室A获取

                    // 5. 关闭工艺腔室A门
                    ("ASP2", null)
                };

                // 执行每个步骤
                for (int i = 0; i < processSteps.Count; i++)
                {
                    var (commandCode, dynamicParams) = processSteps[i];

                    try
                    {
                        _logger.Info($"执行步骤 {i + 1}/{processSteps.Count}: {commandCode}");

                        // 执行命令
                        var result = await _mcuCmdService.ExecuteCommandAsync(commandCode, dynamicParams);

                        // 记录结果
                        string paramInfo = dynamicParams != null ? $" 动态参数[{string.Join(",", dynamicParams)}]" : "";
                        string resultMsg = $"步骤 {i + 1}: {commandCode}{paramInfo} - {result.Response}";
                        results.Add(resultMsg);
                        _logger.Info(resultMsg);

                        // 如果执行失败，停止流程
                        if (result.ReturnInfo != 0)
                        {
                            _logger.Error($"工艺流程在步骤 {i + 1} 失败，终止后续步骤");
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        string errorMsg = $"步骤 {i + 1}: {commandCode} 执行出错 - {ex.Message}";
                        results.Add(errorMsg);
                        _logger.Error(errorMsg, ex);
                        break;
                    }
                }

                return results;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行示例10时发生错误：{ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 示例10：使用CommandManager执行命令
        /// </summary>
        /// <returns>执行结果</returns>
        public async Task<Dictionary<string, object>> Example10_UseCommandManager()
        {
            try
            {
                _logger.Info("示例10：使用CommandManager执行命令");

                var results = new Dictionary<string, object>();

                // 示例1：使用CommandManager执行Robot命令
                _logger.Info("1. 使用CommandManager执行Robot命令");
                var result1 = await _commandManager.ExecuteRobotCommandAsync("AR1");
                results.Add("Robot AR1", new
                {
                    Response = result1.Response,
                    RunInfo = $"0x{result1.RunInfo:X4}",
                    ReturnInfo = $"0x{result1.ReturnInfo:X4}",
                    ElapsedMs = result1.ElapsedMs
                });

                // 示例2：使用CommandManager执行带动态参数的命令
                _logger.Info("2. 使用CommandManager执行带动态参数的命令");
                var dynamicParams = new List<ushort> { 200, 50 }; // 速度200，加速度50
                var result2 = await _commandManager.ExecuteRobotCommandAsync("AR1", dynamicParams);
                results.Add("Robot AR1 with params", new
                {
                    Response = result2.Response,
                    RunInfo = $"0x{result2.RunInfo:X4}",
                    ReturnInfo = $"0x{result2.ReturnInfo:X4}",
                    ElapsedMs = result2.ElapsedMs,
                    Parameters = string.Join(",", dynamicParams)
                });

                // 示例3：使用CommandManager执行命令序列
                _logger.Info("3. 使用CommandManager执行命令序列");
                var commandSequence = new List<(EnuMcuDeviceType DeviceType, string CommandCode, List<ushort> DynamicParameters)>
                {
                    (EnuMcuDeviceType.Robot, "AR1", null),
                    (EnuMcuDeviceType.Robot, "Move_T_Axis", new List<ushort> { 150 })
                };
                var result3 = await _commandManager.ExecuteCommandSequenceAsync(commandSequence);
                results.Add("Command Sequence", result3.Select(r => new
                {
                    CommandInfo = r.CommandInfo,
                    Response = r.Response,
                    RunInfo = $"0x{r.RunInfo:X4}",
                    ReturnInfo = $"0x{r.ReturnInfo:X4}",
                    ElapsedMs = r.ElapsedMs
                }).ToList());

                return results;
            }
            catch (Exception ex)
            {
                _logger.Error($"示例10执行失败: {ex.Message}", ex);
                return new Dictionary<string, object>
                {
                    { "Error", ex.Message }
                };
            }
        }

        /// <summary>
        /// 示例11：使用CommandServiceExtensions扩展方法执行命令
        /// </summary>
        /// <returns>执行结果</returns>
        public async Task<Dictionary<string, object>> Example11_UseExtensionMethods()
        {
            try
            {
                _logger.Info("示例11：使用CommandServiceExtensions扩展方法执行命令");

                var results = new Dictionary<string, object>();

                // 示例1：使用扩展方法直接执行命令
                _logger.Info("1. 使用扩展方法直接执行命令");
                var result1 = await CommandServiceExtensions.ExecuteCommandAsync(_mcuCmdService, "AR1");
                results.Add("Direct AR1", new
                {
                    Response = result1.Response,
                    RunInfo = $"0x{result1.RunInfo:X4}",
                    ReturnInfo = $"0x{result1.ReturnInfo:X4}"
                });

                // 示例2：使用特定设备扩展方法执行命令
                _logger.Info("2. 使用特定设备扩展方法执行命令");
                var result2 = await CommandServiceExtensions.ExecuteRobotCommandAsync(_mcuCmdService, "AR1");
                results.Add("Robot AR1", new
                {
                    Response = result2.Response,
                    RunInfo = $"0x{result2.RunInfo:X4}",
                    ReturnInfo = $"0x{result2.ReturnInfo:X4}"
                });

                // 示例3：使用JSON格式获取执行结果
                _logger.Info("3. 使用JSON格式获取执行结果");
                var result3 = await CommandServiceExtensions.ExecuteCommandJsonAsync(_mcuCmdService, "AR1");
                results.Add("JSON Result", result3);

                // 示例4：获取设备所有命令规格
                _logger.Info("4. 获取设备所有命令规格");
                var robotCommands = CommandServiceExtensions.GetDeviceCommandSpecs(_mcuCmdService, "Robot")
                    .Select(spec => new
                    {
                        CommandCode = spec.CommandCode,
                        CommandName = spec.CommandName,
                        Description = spec.Description
                    })
                    .ToList();
                results.Add("Robot Commands", robotCommands);

                return results;
            }
            catch (Exception ex)
            {
                _logger.Error($"示例11执行失败: {ex.Message}", ex);
                return new Dictionary<string, object>
                {
                    { "Error", ex.Message }
                };
            }
        }
    }
}