using System;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.Services;
using log4net;
using Zishan.SS200.Cmd.Enums.McuCmdIndex;

namespace Zishan.SS200.Cmd.Commands
{
    /// <summary>
    /// Chb设备命令处理器
    /// </summary>
    public class ChbCommandHandler : BaseDeviceCommandHandler<McuDevice, EnuChbCmdIndex>
    {
        public ChbCommandHandler(IModbusClientService modbusClientService, ILog logger)
            : base(modbusClientService, logger)
        {
        }

        public override bool ValidateParameters(EnuChbCmdIndex command, ushort[] parameters)
        {
            // 根据具体命令添加参数验证逻辑
            switch (command)
            {
                case EnuChbCmdIndex.OD_SD:
                case EnuChbCmdIndex.CD_SD:
                case EnuChbCmdIndex.PU:
                case EnuChbCmdIndex.PD:
                case EnuChbCmdIndex.OV_TV:
                case EnuChbCmdIndex.CV_TV:
                case EnuChbCmdIndex.OC:
                case EnuChbCmdIndex.CC:
                case EnuChbCmdIndex.OV_CM:
                case EnuChbCmdIndex.CV_CM:
                case EnuChbCmdIndex.OV_C1:
                case EnuChbCmdIndex.CV_C1:
                    // 这些命令不需要参数
                    return parameters == null || parameters.Length == 0;

                default:
                    return false;
            }
        }
    }
}