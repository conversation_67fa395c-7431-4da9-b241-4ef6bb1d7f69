# Zishan.SS200.Cmd

[![基于.NET 8.0](https://img.shields.io/badge/.NET-8.0-blue.svg)](https://dotnet.microsoft.com/download)
[![使用NModbus](https://img.shields.io/badge/NModbus-v3.0.81-green.svg)](https://github.com/NModbus/NModbus)
[![MVVM架构](https://img.shields.io/badge/Architecture-MVVM-orange.svg)](https://docs.microsoft.com/en-us/archive/msdn-magazine/2009/february/patterns-wpf-apps-with-the-model-view-viewmodel-design-pattern)

## 项目简介

一个基于WPF的Modbus设备命令控制程序，用于与SS200系统的多个MCU设备进行通信。该应用程序提供了友好的用户界面，便于对Modbus设备进行配置、监控和控制。

## 技术栈

- **UI框架**: WPF + XAML
- **架构模式**: MVVM (使用CommunityToolkit.Mvvm)
- **依赖注入**: Prism.DryIoc
- **通信协议**: Modbus TCP (基于NModbus库)
- **UI组件**: HandyControl
- **JSON处理**: Newtonsoft.Json
- **日志记录**: log4net

## 项目结构

```
Zishan.SS200.Cmd/
├── Models/              # 数据模型
│   ├── ModbusCoil.cs    # Modbus线圈模型
│   ├── ModbusRegister.cs # Modbus寄存器模型
│   └── ...
├── ViewModels/          # 视图模型
│   ├── MainWindowViewModel.cs
│   ├── UiViewModel.cs
│   └── ...
├── Views/               # 视图
│   ├── MainWindow.xaml
│   ├── ModbusDICoilsPanel.xaml
│   └── ...
├── Services/            # 服务
│   ├── ModbusClientService.cs
│   ├── S200McuCmdService.cs
│   └── ...
├── Commands/            # 命令处理
├── Config/              # 配置
├── Extensions/          # 扩展方法
└── ...
```

---

# NModbus 简介

NModbus是一个C#实现的Modbus协议库，提供了与Modbus从属设备和应用程序的连接能力。支持串行ASCII、串行RTU、TCP和UDP协议。

## NModbus历史

NModbus是对NModbus4项目的一个分支，提供了更多特性：

- 将Modbus从设备添加到由`IModbusSlaveInstance`表示的网络
- 更广泛地使用接口
- 支持为从设备添加自定义功能码处理程序

## 安装NModbus

通过Package Manager Console安装NModbus：

```
PM> Install-Package NModbus
```

## NModbus文档

文档可在[这里](https://nmodbus.github.io/api/NModbus.html)获取。

## 许可证

NModbus基于[MIT许可证](LICENSE.txt)。

---

# S200McuCmdService 使用指南

## 服务简介

`S200McuCmdService`是本项目的核心服务，基于Modbus TCP协议与SS200系统的多个MCU设备进行通信。该服务作为底层设备通信的抽象层，为上层应用提供了简洁统一的接口。

### 支持的设备

服务支持以下设备的独立控制：

- **Shuttle**: Shuttle设备（负责晶圆传送）
- **Robot**: 机器人设备（执行取放操作）
- **Cha**: 腔体A设备（半导体工艺处理腔体）
- **Chb**: 腔体B设备（半导体工艺处理腔体）

## 功能特性

- **多设备管理**: 支持多设备并行连接管理，各设备独立控制
- **统一接口**: 提供统一的命令执行接口，简化上层应用开发
- **底层封装**: 自动处理Modbus通信细节，减少开发复杂度
- **异常处理**: 完善的错误处理和状态监控，提高系统稳定性
- **状态查询**: 设备状态和统计信息的实时查询功能
- **线圈监控**: 支持设备输入/输出线圈的实时监控
- **批量命令**: 支持批量命令序列执行，提高自动化程度

## 快速开始

### 1. 服务初始化

```csharp
// 通过依赖注入获取ModbusClientService
private readonly IModbusClientService _modbusClientService;

// 初始化S200McuCmdService
private readonly S200McuCmdService _mcuCmdService;

public YourClass(IModbusClientService modbusClientService)
{
    _modbusClientService = modbusClientService;
    _mcuCmdService = new S200McuCmdService(_modbusClientService);
}
```

### 2. 连接设备

```csharp
// 连接所有设备
await _mcuCmdService.ConnectAllAsync(
    "192.168.1.100", 502,  // Shuttle设备
    "192.168.1.101", 502,  // Robot设备
    "192.168.1.102", 502,  // Cha设备
    "192.168.1.103", 502   // Chb设备
);

// 或者单独连接某个设备
await _mcuCmdService.Shuttle.ConnectAsync("192.168.1.100", 502);
```

### 3. 执行命令

```csharp
// 准备命令参数
List<ushort> parameters = new List<ushort> { 1, 2, 3 };

// 执行命令
var (response, runInfo, returnInfo) = await _mcuCmdService.Shuttle.Run(
    EnuCmdIndexInfo.S1_SD,  // 命令类型（Shuttle下降）
    parameters,             // 命令参数
    5000                    // 超时时间(毫秒)
);

// 检查执行结果
if (response.StartsWith("Success"))
{
    Console.WriteLine($"命令执行成功，运行信息: {runInfo}, 返回信息: {returnInfo}");
}
else
{
    Console.WriteLine($"命令执行失败: {response}");
}
```

### 4. 获取设备状态

```csharp
// 获取所有设备状态
Dictionary<string, DeviceStatus> deviceStatuses = _mcuCmdService.GetAllDeviceStatus();

// 获取设备详细诊断信息
DeviceDiagnostics diagnostics = _mcuCmdService.Shuttle.GetDiagnostics();

// 输出诊断信息
Console.WriteLine($"设备: {diagnostics.DeviceName}");
Console.WriteLine($"状态: {diagnostics.Status}");
Console.WriteLine($"IP地址: {diagnostics.ConnectionInfo.IpAddress}");
Console.WriteLine($"总命令数: {diagnostics.Statistics.TotalCommands}");
Console.WriteLine($"成功数: {diagnostics.Statistics.SuccessfulCommands}");
Console.WriteLine($"失败数: {diagnostics.Statistics.FailedCommands}");
```

### 5. 断开连接

```csharp
// 断开所有设备连接
await _mcuCmdService.DisconnectAllAsync();

// 或者断开单个设备
await _mcuCmdService.Robot.DisconnectAsync();
```

## 监控设备IO状态

```csharp
// 启动线圈状态监控
_mcuCmdService.StartCoilsMonitoring();

// 可以通过以下集合访问线圈状态
var shuttleInputs = _mcuCmdService.ShuttleInputCoils;  // Shuttle设备的输入状态
var robotOutputs = _mcuCmdService.RobotCoils;         // Robot设备的输出控制

// 停止监控
_mcuCmdService.StopCoilsMonitoring();
```

## 完整示例

项目中包含了一个完整的示例类 `S200McuCmdServiceExample.cs`，展示了服务的各种用法。主要包括：

1. 初始化和连接设备
2. 执行各种设备命令
3. 错误处理示例
4. 获取设备状态和诊断信息
5. 断开设备连接

## 注意事项

- **配置验证**: 确保设备IP地址和端口配置正确
- **连接状态**: 命令执行前检查设备连接状态
- **超时设置**: 根据命令复杂度设置合理的命令超时时间
- **异常处理**: 正确处理可能的通信异常和设备故障
- **资源释放**: 不再使用服务时调用Dispose方法释放资源

## 支持的命令

服务支持以下命令类型（`EnuCmdIndexInfo`枚举）：

- `S1_SD`: Shuttle下降（控制Shuttle下降动作）
- `S2_SU`: Shuttle上升（控制Shuttle上升动作）
- `S3_SR1`: Shuttle旋转1（控制Shuttle旋转到位置1）
- `S4_SR2`: Shuttle旋转2（控制Shuttle旋转到位置2）
- `S7_TS1`: 移动到指定位置（控制设备移动到特定坐标）
- `S9_SC`: 状态检查（查询设备当前状态）
- `S10_RC`: 复位命令（重置设备状态）
- 其他更多命令...

# 项目架构和流程图

## 项目结构图

```mermaid
classDiagram
    class IModbusClientService {
        <<interface>>
        +IsConnected: bool
        +Master: IModbusMaster
        +ConnectAsync(string, int, int): Task<bool>
        +DisconnectAsync(): Task
        +ReadHoldingRegistersAsync(): Task<ushort[]>
        +WriteHoldingRegistersAsync(): Task
    }
    
    class ModbusClientService {
        -IModbusMaster _master
        -TcpClient _tcpClient
        +IsConnected: bool
        +ConnectAsync(string, int, int): Task<bool>
        +DisconnectAsync(): Task
    }
    
    class S200McuCmdService {
        +Shuttle: McuDevice
        +Robot: McuDevice
        +Cha: McuDevice
        +Chb: McuDevice
        +ConnectAllAsync(): Task
        +DisconnectAllAsync(): Task
    }
    
    class McuDevice {
        +string DeviceName
        +DeviceStatus Status
        +ConnectAsync(string, int): Task
        +Run(EnuCmdIndexInfo, List<ushort>, int): Task
    }
    
    IModbusClientService <|.. ModbusClientService
    S200McuCmdService --> ModbusClientService
    S200McuCmdService *-- McuDevice
```

## 核心模块依赖关系

```mermaid
flowchart TB
    App[App.xaml.cs] --> MainWindow[MainWindow.xaml]
    App --> MainWindowVM[MainWindowViewModel.cs]
    App --> S200McuCmdService
    App --> ModbusClientService
    
    MainWindow --> MainWindowVM
    MainWindow --> ModbusDICoilsPanel
    MainWindow --> ModbusDOCoilsPanel
    MainWindow --> RobotStatusPanel
    
    MainWindowVM --> UiViewModel
    MainWindowVM --> S200McuCmdService
    MainWindowVM --> BatchCommandParser
    
    ModbusDICoilsPanel --> UiViewModel
    ModbusDOCoilsPanel --> UiViewModel
    RobotStatusPanel --> UiViewModel
    
    UiViewModel --> S200McuCmdService
    
    S200McuCmdService --> ModbusClientService
    S200McuCmdService --> DeviceCommandFactory
    S200McuCmdService --> McuDevice
    S200McuCmdService --> ModbusCoil
    S200McuCmdService --> ModbusRegister
    
    ModbusClientService --> NModbus
    
    McuDevice --> CmdTaskHandler
    McuDevice --> ModbusClientService
    
    ModbusCoil --- BaseModbusRegister
    ModbusRegister --- BaseModbusRegister
```

## 设备和命令模型

```mermaid
classDiagram
    class McuDevice {
        -IModbusClientService _modbusClientService
        -CmdTaskHandler _cmdTaskHandler
        -string _ipAddress
        -int _port
        -byte _slaveId
        +string DeviceName
        +DeviceStatus Status
        +byte SlaveId
        +bool IsConnected
        +Dictionary<string, CommandConfig> CommandConfigDictionary
        +ConnectAsync(string, int): Task
        +DisconnectAsync(): Task
        +Run(EnuCmdIndexInfo, List<ushort>, int): Task<(string, ushort, ushort)>
        +ResetTaskAsync(ushort): Task<bool>
    }
    
    class CmdTaskHandler {
        -List<ushort> _taskRegisterAddresses
        -List<ushort> _cmdParamAddresses
        +ExecuteCommandAsync(IModbusMaster, byte, EnuCmdIndexInfo, List<ushort>, int): Task<TaskHandleResult>
        +MonitorStatusAsync(IModbusMaster, byte): Task<(TaskHandleStatus, ushort, ushort)>
        +ResetTaskRegistersAsync(IModbusMaster, byte, ushort): Task
    }
    
    class ModbusCoil {
        +DeviceType deviceType
        +bool? coilvalue
        +bool isDeviceConnected
        +UpdateConnectionStatus(): void
        +WriteValueManually(bool): Task
    }
    
    class ModbusRegister {
        +ushort Address
        +string Name
        +string Title
        +string Description
        +string Remark
        +ushort Value
        +bool IsWriteable
        +ModbusRegisterType Type
    }
    
    class BaseModbusRegister {
        +ushort Address
        +string Name
        +string Title
        +string Description
        +string Remark
        +ushort Value
        +bool IsWriteable
    }
    
    class BatchCommandSequence {
        +string Name
        +string Description
        +int RepeatCount
        +int IntervalMs
        +List<BatchCommand> Commands
    }
    
    class BatchCommand {
        +string DeviceName
        +string CommandName
        +List<ushort> Parameters
        +int TimeoutMs
    }
    
    McuDevice --> CmdTaskHandler
    S200McuCmdService --> McuDevice
    BaseModbusRegister <|-- ModbusCoil
    BaseModbusRegister <|-- ModbusRegister
    BatchCommandSequence --> BatchCommand
```

## MVVM 结构图

```mermaid
flowchart LR
    subgraph Models[数据模型层]
        ModbusCoil
        ModbusRegister
        BaseModbusRegister
        BatchCommand
        CmdTaskHandlel
    end
    
    subgraph ViewModels[视图模型层]
        MainWindowVM[MainWindowViewModel]
        UiVM[UiViewModel]
        IntegerConversionVM[IntegerConversionViewModel]
    end
    
    subgraph Views[视图层]
        MainWindow
        DICoilsPanel[ModbusDICoilsPanel]
        DOCoilsPanel[ModbusDOCoilsPanel]
        RobotPanel[RobotStatusPanel]
        ConversionView[IntegerConversionView]
    end
    
    subgraph Services[服务层]
        S200McuCmdService
        ModbusClientService
        ConfigService[ConfigurationService]
    end
    
    Models --> ViewModels
    Services --> ViewModels
    ViewModels --> Views
    
    MainWindow --绑定--> MainWindowVM
    DICoilsPanel --绑定--> UiVM
    DOCoilsPanel --绑定--> UiVM
    RobotPanel --绑定--> UiVM
    ConversionView --绑定--> IntegerConversionVM
    
    MainWindowVM --使用--> UiVM
    MainWindowVM --使用--> S200McuCmdService
    UiVM --使用--> S200McuCmdService
    S200McuCmdService --使用--> ModbusClientService
```

## 配置管理流程

```mermaid
sequenceDiagram
    App->>App: 初始化配置 (InitializeConfiguration)
    App->>ConfigPaths: 获取配置文件路径
    ConfigPaths->>Golbal.WorkRootPath: 获取工作目录
    App->>App.AppIniConfig: 创建配置实例
    App->>ConfigHelper: 获取配置文件路径
    ConfigHelper->>File: 检查工作目录配置文件是否存在
    File-->>ConfigHelper: 返回文件状态
    ConfigHelper-->>App: 返回实际配置文件路径
    App->>IniConfig: 加载配置文件
    App->>LogConfigurator: 配置日志 (ConfigureLogging)
    LogConfigurator->>log4net: 初始化日志配置
```

## 命令执行流程

```mermaid
sequenceDiagram
    MainWindow->>MainWindowViewModel: 执行命令 (ExecuteCommandAsync)
    MainWindowViewModel->>MainWindowViewModel: 获取选择的设备和命令
    MainWindowViewModel->>MainWindowViewModel: 解析命令参数
    MainWindowViewModel->>McuDevice: 执行命令 (Run)
    McuDevice->>CmdTaskHandler: 执行命令 (ExecuteCommandAsync)
    CmdTaskHandler->>ModbusClientService: 写入命令参数 (WriteHoldingRegistersAsync)
    ModbusClientService->>NModbus: 通过Modbus发送命令
    CmdTaskHandler->>CmdTaskHandler: 启动监控循环
    loop 监控命令执行状态
        CmdTaskHandler->>ModbusClientService: 读取状态寄存器 (ReadHoldingRegistersAsync)
        ModbusClientService->>NModbus: 读取寄存器值
        NModbus-->>ModbusClientService: 返回寄存器值
        ModbusClientService-->>CmdTaskHandler: 返回状态信息
        CmdTaskHandler->>CmdTaskHandler: 检查是否完成或超时
    end
    CmdTaskHandler-->>McuDevice: 返回命令执行结果
    McuDevice-->>MainWindowViewModel: 返回执行结果和状态信息
    MainWindowViewModel->>MainWindow: 更新UI显示执行结果
```
