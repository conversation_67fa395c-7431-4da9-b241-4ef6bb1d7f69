using System;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.Services;
using log4net;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Enums.McuCmdIndex;

namespace Zishan.SS200.Cmd.Commands
{
    /// <summary>
    /// 设备命令工厂
    /// </summary>
    public class DeviceCommandFactory
    {
        private readonly IModbusClientService _modbusClientService;
        private readonly ILog _logger;

        public DeviceCommandFactory(IModbusClientService modbusClientService, ILog logger)
        {
            _modbusClientService = modbusClientService;
            _logger = logger;
        }

        /// <summary>
        /// 创建设备命令处理器
        /// </summary>
        /// <typeparam name="TCommand">命令枚举类型</typeparam>
        /// <param name="deviceType">设备类型</param>
        /// <returns>命令处理器实例</returns>
        public dynamic CreateCommandHandler<TCommand>(EnuMcuDeviceType deviceType) where TCommand : Enum
        {
            if (typeof(TCommand) == typeof(EnuShuttleCmdIndex) && deviceType == EnuMcuDeviceType.Shuttle)
            {
                return new ShuttleCommandHandler(_modbusClientService, _logger);
            }
            else if (typeof(TCommand) == typeof(EnuRobotCmdIndex) && deviceType == EnuMcuDeviceType.Robot)
            {
                return new RobotCommandHandler(_modbusClientService, _logger);
            }
            else if (typeof(TCommand) == typeof(EnuChaCmdIndex) && deviceType == EnuMcuDeviceType.ChamberA)
            {
                return new ChaCommandHandler(_modbusClientService, _logger);
            }
            else if (typeof(TCommand) == typeof(EnuChbCmdIndex) && deviceType == EnuMcuDeviceType.ChamberB)
            {
                return new ChbCommandHandler(_modbusClientService, _logger);
            }

            throw new ArgumentException($"不支持的设备类型或命令类型: {deviceType}, {typeof(TCommand).Name}");
        }
    }
}