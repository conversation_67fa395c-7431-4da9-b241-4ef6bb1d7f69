# 腔体子系统状态模型

本目录包含基于SS200规格的腔体子系统状态实体类和枚举类型。

## 结构概览

### 主要实体类
- **ChamberSubsystemStatus.cs**: 封装所有腔体状态属性的主要实体类

### 枚举类型

1. **EnuTriggerStatus.cs**: 触发状态枚举
   - None (0): 未知状态
   - NoAlarm (1): MPS1 - 无报警
   - Alarm (2): MPS2 - 报警

2. **EnuRunStatus.cs**: 运行状态枚举
   - None (0): 未知状态
   - BusyA (1): MPS3A - 当前chamber slit door动作中，或者robot于chamber交互中
   - BusyB (2): MPS3B - lift pin动作中
   - Idle (3): MPS4 - slit door/lift pin不在动作中，或者不在与robot交互中
   - Processing (4): MPS5 - recipe running，RF on

3. **EnuSlitDoorStatus.cs**: Slit Door位置状态
   - None (0): 未知状态
   - Open (1): SP1 - PDI12=1 PDI13=0
   - Close (2): SP2 - PDI12=0 PDI13=1
   - BetweenOpenClose (3): SP3 - PDI12=0 PDI13=0

4. **EnuLiftPinStatus.cs**: Lift Pin位置状态
   - None (0): 未知状态
   - Up (1): SP4 - PDI14=1 PDI15=0
   - Down (2): SP5 - PDI14=0 PDI15=1
   - BetweenUpDown (3): SP6 - PDI14=0 PDI15=0

5. **EnuWaferReadyStatus.cs**: 晶圆准备状态
   - None (0): 未知状态
   - ReadyReceive (1): SP7 - SP1 SP5 (slit door打开 + lift pin下降)
   - ReadyOut (2): SP8 - SP1 SP4 (slit door打开 + lift pin上升)
   - Received (3): SP9 - SP2 SP4 (slit door关闭 + lift pin上升)
   - Out (4): SP10 - SP2 SP5 (slit door关闭 + lift pin下降)

6. **EnuChamberVacuumStatus.cs**: 腔体真空状态
   - None (0): 未知状态
   - ProcessVacuum (1): SP11 - PDI5=1 PDI6=1 PAI1≤PPS6
   - NoProcessVacuum (2): SP12 - PDI5=0 or PDI6=0 or PAI1>PPS6

7. **EnuLoadlockVacuumStatus.cs**: 负载锁真空状态
   - None (0): 未知状态
   - PressureVacuum (1): SP13 - PAI6≤PPS7
   - PressureNoVacuum (2): SP14 - PAI6>PPS7

8. **EnuValveStatus.cs**: 通用阀门状态（用于多个阀门）
   - None (0): 未知状态
   - Open (1): 阀门打开
   - Close (2): 阀门关闭
   - BetweenOpenClose (3): 阀门在打开与关闭之间

9. **EnuForlineVacuumStatus.cs**: 前级真空状态
   - None (0): 未知状态
   - NoVacuum (1): SP21 - PDI5=0
   - HasVacuum (2): SP22 - PDI5=1

## 状态码映射

### 触发状态 (MPS1-MPS2)
- MPS1: 无报警 → EnuTriggerStatus.NoAlarm
- MPS2: 报警 → EnuTriggerStatus.Alarm

### 运行状态 (MPS3A-MPS5)
- MPS3A: 忙碌A → EnuRunStatus.BusyA
- MPS3B: 忙碌B → EnuRunStatus.BusyB
- MPS4: 空闲 → EnuRunStatus.Idle
- MPS5: 处理中 → EnuRunStatus.Processing

### 位置状态 (SP1-SP10)
- SP1-SP3: Slit door位置 → EnuSlitDoorStatus
- SP4-SP6: Lift pin位置 → EnuLiftPinStatus
- SP7-SP10: 晶圆准备状态 → EnuWaferReadyStatus

### 压力状态 (SP11-SP22)
- SP11-SP12: 腔体真空状态 → EnuChamberVacuumStatus
- SP13-SP14: 负载锁真空状态 → EnuLoadlockVacuumStatus
- SP15-SP17: ISO阀门状态 → EnuValveStatus (IsoValveStatus)
- SP18-SP20: 节流阀状态 → EnuValveStatus (ThrottleValveStatus)
- SP21-SP22: 前级真空 → EnuForlineVacuumStatus

### 气体状态 (SP23-SP32)
- SP23-SP24: CM阀门 → EnuValveStatus (CmValveStatus)
- SP25-SP26: C1阀门 → EnuValveStatus (C1ValveStatus)
- SP27-SP28: C2阀门 → EnuValveStatus (C2ValveStatus)
- SP29-SP30: C3阀门 → EnuValveStatus (C3ValveStatus)
- SP31-SP32: C4阀门 → EnuValveStatus (C4ValveStatus)

## 使用方法

`ChamberSubsystemStatus` 是抽象基类，不能直接实例化。必须通过具体的子类实例化：

```csharp
// 正确的使用方式 - 通过具体子类实例化
var chamberAStatus = new ChamberASubsystemStatus();
chamberAStatus.TriggerStatus = EnuTriggerStatus.NoAlarm;
chamberAStatus.RunStatus = EnuRunStatus.Idle;
chamberAStatus.SlitDoorStatus = EnuSlitDoorStatus.Open;

var chamberBStatus = new ChamberBSubsystemStatus();
chamberBStatus.TriggerStatus = EnuTriggerStatus.Alarm;
chamberBStatus.RunStatus = EnuRunStatus.Processing;
chamberBStatus.SlitDoorStatus = EnuSlitDoorStatus.Close;

// 错误的使用方式 - 不能直接实例化抽象类
// var chamberStatus = new ChamberSubsystemStatus(); // 编译错误！
```

所有属性都初始化为各自的 `None` 值，可根据机器人运动条件判断的需要进行更新。
