using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using log4net;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Enums.Command;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Commands.CommandSpec.ChamberA.Door
{
    /// <summary>
    /// ASP1 - 工艺腔室A开门
    /// </summary>
    public class ASP1Command : BaseCommandSpec<EnuChaCmd>
    {
        /// <summary>
        /// 状态管理实例
        /// </summary>
        private readonly S200MockStatus _mockStatus = S200MockStatus.Instance;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ASP1Command() : base(
           EnuMcuDeviceType.ChamberA,
            EnuChaCmd.ASP1,  // 使用OD_SD作为命令索引
             EnuChaCmd.ASP1.ToString(),  // 添加commandCode参数
            "Open Door",
            "工艺腔室A开门",
            30000)  // 从配置文件中获取的超时时间
        {
        }

        /// <summary>
        /// 参数验证
        /// </summary>
        /// <param name="parameters">参数列表</param>
        /// <returns>是否有效</returns>
        public override bool ValidateParameters(List<ushort> parameters)
        {
            // ASP1命令不需要参数
            return parameters == null || parameters.Count == 0;
        }

        /// <summary>
        /// 执行前处理
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="parameters">参数列表</param>
        /// <returns>是否继续执行命令</returns>
        public override async Task<bool> BeforeExecuteAsync(IS200McuCmdService cmdService, List<ushort> parameters)
        {
            // 检查工艺腔室A是否处于空闲状态
            if (!_mockStatus.CheckChaStatus(cmdService))
            {
                return false;
            }

            // 检查工艺腔室A当前真空度
            // 需要进行真空度检查，确保腔室已经达到大气压力

            // 检查是否有机械臂在腔室中
            // 检查是否有晶圆在腔室中
            // 这里可以添加更多的安全检查逻辑

            // 执行前记录日志
            _logger.Info("准备执行工艺腔室A开门命令");
            return await base.BeforeExecuteAsync(cmdService, parameters);
        }

        /// <summary>
        /// 执行后处理
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="result">命令执行结果</param>
        /// <param name="parameters">参数列表</param>
        /// <returns>处理后的结果</returns>
        public override async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> AfterExecuteAsync(
            IS200McuCmdService cmdService,
            (string Response, ushort RunInfo, ushort ReturnInfo) result,
            List<ushort> parameters)
        {
            if (result.ReturnInfo == 0)
            {
                _logger.Info("工艺腔室A已成功开门");

                // 可以在这里添加后续操作，例如更新UI状态、触发下一步流程等
            }
            else
            {
                _logger.Error($"工艺腔室A开门失败，错误代码: 0x{result.ReturnInfo:X4}");

                // 根据错误代码提供更详细的错误信息
                string errorDetail = _mockStatus.GetChaErrorDetail(result.ReturnInfo);
                if (!string.IsNullOrEmpty(errorDetail))
                {
                    result.Response = $"{result.Response} - {errorDetail}";
                }
            }

            return await base.AfterExecuteAsync(cmdService, result, parameters);
        }
    }
}