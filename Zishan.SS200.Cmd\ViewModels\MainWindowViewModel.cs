﻿using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.Services;
using log4net;
using System.Threading;
using CommunityToolkit.Mvvm.Input;
using HandyControl.Controls;
using System.Windows;

// using Prism.Navigation.Regions;
using Zishan.SS200.Cmd.Extensions;
using MessageBox = HandyControl.Controls.MessageBox;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.Basic;
using System.Windows.Data;
using Prism.Regions;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.ViewModels.Dock;

namespace Zishan.SS200.Cmd.ViewModels
{
    /// <summary>
    /// 主窗口ViewModel
    /// </summary>
    public partial class MainWindowViewModel : ObservableObject, IDisposable, IConfigureService
    {
        #region 字段

        private readonly IS200McuCmdService _mcuCmdService;
        private readonly ILog _logger = LogManager.GetLogger(typeof(MainWindowViewModel));
        private readonly IRegionManager _regionManager;
        private readonly object _logListLock = new object(); // 集合同步锁对象

        #endregion 字段

        #region 属性

        /// <summary>
        /// 是否有任何设备连接
        /// </summary>
        [ObservableProperty]
        private bool hasAnyDeviceConnected;

        [ObservableProperty]
        private bool isTabItemtMainSelected = false;

        /// <summary>
        /// 版本
        /// </summary>
        [ObservableProperty]
        private string title = "Zishan.SS200.Cmd_2025-08-01-M1";

        /// <summary>
        /// 设备连接状态
        /// </summary>
        [ObservableProperty]
        private Dictionary<EnuMcuDeviceType, DeviceStatus> deviceStatuses = new Dictionary<EnuMcuDeviceType, DeviceStatus>();

        #endregion 属性

        #region 构造函数

        public MainWindowViewModel() : this(new S200McuCmdService(), null)
        {
            // 设计时数据绑定
            Title = "设计时数据绑定示例";
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public MainWindowViewModel(IS200McuCmdService mcuCmdService, IRegionManager regionManager)
        {
            _mcuCmdService = mcuCmdService ?? throw new ArgumentNullException(nameof(mcuCmdService));
            _regionManager = regionManager; // 允许为null，设计时可能不需要

            mcuCmdService.Name = $"S200McuCmdService，创建时间：{DateTime.Now}";

            InitializeViewModel();
        }

        /// <summary>
        /// 初始化ViewModel的共享代码
        /// </summary>
        private void InitializeViewModel()
        {
            _logger.Info("主窗口ViewModel已初始化");

            // 初始化设备状态
            UpdateDeviceStatus();

            // 添加设备状态变化的事件订阅
            _mcuCmdService.Shuttle.StatusChanged += Device_StatusChanged;
            _mcuCmdService.Robot.StatusChanged += Device_StatusChanged;
            _mcuCmdService.ChamberA.StatusChanged += Device_StatusChanged;
            _mcuCmdService.ChamberB.StatusChanged += Device_StatusChanged;
        }

        #endregion 构造函数

        #region 命令

        /// <summary>
        /// 初始化命令，视图加载完成后执行
        /// </summary>
        [RelayCommand]
        private void Initialize()
        {
            // 确保设备状态正确反映在UI上
            UpdateDeviceStatus();
            _logger.Info("主窗口初始化完成，已更新设备状态");
        }

        /// <summary>
        /// 连接所有设备
        /// </summary>
        [RelayCommand]
        private async Task ConnectDevicesAsync()
        {
            try
            {
                _logger.Info("正在连接所有设备...");

                // 使用配置文件中的设备IP和端口
                await _mcuCmdService.ConnectAllAsync(
                    App.AppIniConfig.ShuttleIp, App.AppIniConfig.ShuttlePort,
                    App.AppIniConfig.RobotIp, App.AppIniConfig.RobotPort,
                    App.AppIniConfig.ChaIp, App.AppIniConfig.ChaPort,
                    App.AppIniConfig.ChbIp, App.AppIniConfig.ChbPort
                );

                // 连接成功后立即更新设备状态
                UpdateDeviceStatus();

                bool isAllConnected = true;
                // 获取所有设备的状态
                foreach (var device in DeviceStatuses)
                {
                    _logger.Info($"设备 {device.Key} 状态: {device.Value}");
                    if (device.Value != DeviceStatus.Connected)
                    {
                        isAllConnected = false;
                    }
                }

                if (isAllConnected)
                {
                    HcGrowlExtensions.Success("所有设备连接成功", "", waitTime: 3, showDateTime: true, HcGrowlExtensions.GrowlDisplayMode.Desktop);
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"连接设备失败: {ex.Message}", ex);
                HcGrowlExtensions.Error($"连接设备失败: {ex.Message}");
                UILogService.AddLog($"连接设备时发生错误: {ex.Message}");
                // 出错时仍然尝试更新设备状态
                UpdateDeviceStatus();
            }
        }

        /// <summary>
        /// 断开所有设备连接
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanDisconnectDevices))]
        private async Task DisconnectDevicesAsync()
        {
            try
            {
                if (HandyControl.Controls.MessageBox.Show("确定要断开所有设备连接吗？", "确认", MessageBoxButton.YesNo) != MessageBoxResult.Yes)
                    return;

                _logger.Info("正在断开所有设备连接...");

                await _mcuCmdService.DisconnectAllAsync();

                // 断开连接后更新设备状态
                UpdateDeviceStatus();

                _logger.Info("所有设备已断开连接");
                HcGrowlExtensions.Success("所有设备已断开连接");
            }
            catch (Exception ex)
            {
                _logger.Error($"断开设备连接失败: {ex.Message}", ex);
                HcGrowlExtensions.Error($"断开设备连接失败: {ex.Message}");
                UILogService.AddErrorLog($"断开设备连接时发生错误: {ex.Message}");
                // 出错时仍然尝试更新设备状态
                UpdateDeviceStatus();
            }
        }

        /// <summary>
        /// 判断是否可以断开设备连接
        /// </summary>
        /// <returns>是否可以断开设备连接</returns>
        private bool CanDisconnectDevices() => HasAnyDeviceConnected;

        /// <summary>
        /// 重置任务区和命令参数区寄存器
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanDisconnectDevices))]
        private async Task ResetTaskAsync()
        {
            try
            {
                if (!Golbal.IsDevDebug)
                {
                    if (HandyControl.Controls.MessageBox.Show("确定要重置所有设备的任务区和命令参数区吗？", "确认", MessageBoxButton.YesNo) != MessageBoxResult.Yes)
                        return;
                }

                _logger.Info("开始重置所有设备的任务区和命令参数区");

                bool allSuccess = true;
                List<EnuMcuDeviceType> failedDevices = new List<EnuMcuDeviceType>();
                bool hasConnectedDevices = false;

                // 统一使用固定的重置值0
                ushort resetValue = 0;

                // 创建设备字典，便于统一处理
                var devices = new Dictionary<EnuMcuDeviceType, McuDevice>
                {
                    { EnuMcuDeviceType.Shuttle, _mcuCmdService.Shuttle },
                    { EnuMcuDeviceType.Robot, _mcuCmdService.Robot },
                    { EnuMcuDeviceType.ChamberA, _mcuCmdService.ChamberA },
                    { EnuMcuDeviceType.ChamberB, _mcuCmdService.ChamberB }
                };

                // 循环处理所有设备
                foreach (var device in devices)
                {
                    EnuMcuDeviceType deviceType = device.Key;
                    McuDevice mcuDevice = device.Value;

                    if (mcuDevice.IsConnected)
                    {
                        hasConnectedDevices = true;
                        bool result = await mcuDevice.ResetTaskAsync(resetValue: resetValue);

                        if (!result)
                        {
                            allSuccess = false;
                            failedDevices.Add(deviceType);
                            _logger.Warn($"{deviceType} 设备重置失败");
                        }
                        else
                        {
                            _logger.Info($"{deviceType} 设备重置成功");
                        }
                    }
                }

                // 显示结果
                if (!hasConnectedDevices)
                {
                    _logger.Warn("没有发现连接的设备，无法执行重置操作");
                    HcGrowlExtensions.Warning("没有连接的设备，请先连接设备");
                }
                else if (allSuccess)
                {
                    _logger.Info("所有设备的任务区和命令参数区重置成功");
                    HcGrowlExtensions.Success("所有设备重置成功");
                }
                else
                {
                    string failedList = string.Join(", ", failedDevices);
                    string message = $"部分设备重置失败: {failedList}";
                    _logger.Error(message);
                    HcGrowlExtensions.Error(message);
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"重置设备任务区和命令参数区时发生错误: {ex.Message}", ex);
                HcGrowlExtensions.Error($"重置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新设备状态
        /// </summary>
        [RelayCommand]
        private void RefreshDeviceStatus()
        {
            try
            {
                UpdateDeviceStatus();
                HcGrowlExtensions.Success("设备状态已刷新");
            }
            catch (Exception ex)
            {
                string errorMessage = $"刷新设备状态失败: {ex.Message}";
                _logger.Error(errorMessage, ex);
                HcGrowlExtensions.Error(errorMessage);
            }
        }

        #endregion 命令

        #region 方法

        /// <summary>
        /// 初始化配置，导航到首页
        /// </summary>
        public async Task Configure()
        {
            if (Golbal.IsDevDebug)
            {
                await ConnectDevicesAsync();
            }
        }

        #endregion 方法

        #region 释放资源

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                _logger.Info("正在释放资源");

                // 取消设备状态变化事件订阅
                if (_mcuCmdService != null)
                {
                    _mcuCmdService.Shuttle.StatusChanged -= Device_StatusChanged;
                    _mcuCmdService.Robot.StatusChanged -= Device_StatusChanged;
                    _mcuCmdService.ChamberA.StatusChanged -= Device_StatusChanged;
                    _mcuCmdService.ChamberB.StatusChanged -= Device_StatusChanged;
                }

                // ✅ 优化报告修复：避免在Dispose中使用.Wait()，使用ConfigureAwait(false)
                try
                {
                    _mcuCmdService?.DisconnectAllAsync().ConfigureAwait(false).GetAwaiter().GetResult();
                }
                catch (Exception disconnectEx)
                {
                    _logger.Warn($"断开连接时发生错误: {disconnectEx.Message}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"释放资源时发生错误: {ex.Message}", ex);
            }
        }

        #endregion 释放资源

        #region 私有方法

        /// <summary>
        /// 更新设备状态
        /// </summary>
        private void UpdateDeviceStatus()
        {
            try
            {
                // 获取新的状态
                var newStatuses = _mcuCmdService.GetAllDeviceStatus();

                // 检查状态是否有变化
                bool hasChanges = false;
                if (DeviceStatuses != null && DeviceStatuses.Count > 0)
                {
                    foreach (var key in newStatuses.Keys)
                    {
                        if (!DeviceStatuses.TryGetValue(key, out var status) || status != newStatuses[key])
                        {
                            hasChanges = true;
                            _logger.Debug($"设备 {key} 状态已变更: {(DeviceStatuses.ContainsKey(key) ? DeviceStatuses[key] : "Unknown")} -> {newStatuses[key]}");
                        }
                    }
                }
                else
                {
                    hasChanges = true;
                }

                // 更新状态字典
                DeviceStatuses = newStatuses;

                // 强制触发UI更新 - 使用手动调用OnPropertyChanged方法
                OnPropertyChanged(nameof(DeviceStatuses));

                // 为每个设备单独触发通知
                foreach (var key in DeviceStatuses.Keys)
                {
                    OnPropertyChanged($"DeviceStatuses[{key}]");
                }

                // 更新是否有任何设备连接的状态
                HasAnyDeviceConnected = DeviceStatuses.Any(kv => kv.Value == DeviceStatus.Connected || kv.Value == DeviceStatus.Busy);

                // 手动通知命令的可执行状态改变
                DisconnectDevicesCommand.NotifyCanExecuteChanged();
                ResetTaskCommand.NotifyCanExecuteChanged();

                if (hasChanges)
                {
                    _logger.Debug($"设备状态已更新: {string.Join(", ", DeviceStatuses.Select(kv => $"{kv.Key}={kv.Value}"))}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"更新设备状态时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 设备状态变化事件处理
        /// </summary>
        private void Device_StatusChanged(object sender, DeviceStatus e)
        {
            // 确保在UI线程上执行状态更新
            Application.Current.Dispatcher.BeginInvoke(new Action(() =>
            {
                if (sender is McuDevice device)
                {
                    _logger.Debug($"接收到设备状态变化事件: {device.DeviceType} -> {e}");
                    // 当任何设备状态变化时更新状态字典
                    UpdateDeviceStatus();
                }
            }));
        }

        #endregion 私有方法
    }
}