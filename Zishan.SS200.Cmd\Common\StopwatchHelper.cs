﻿using System;
using System.Diagnostics;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zishan.SS200.Cmd.Common
{
    public class StopwatchHelper
    {
        private readonly Stopwatch stopwatch = new();

        public void Start()
        {
            stopwatch.Start();
        }

        public TimeSpan Stop()
        {
            if (!stopwatch.IsRunning)
                throw new InvalidOperationException("计时器未启动。");

            stopwatch.Stop();
            return stopwatch.Elapsed;
        }

        public TimeSpan Restart()
        {
            var elapsed = stopwatch.Elapsed;
            stopwatch.Restart();
            return elapsed;
        }

        public void Reset()
        {
            stopwatch.Reset();
        }
    }
}