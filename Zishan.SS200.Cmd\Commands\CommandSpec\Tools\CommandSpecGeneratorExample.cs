using log4net;
using System;
using System.Collections.Generic;
using System.IO;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Commands.CommandSpec.Tools
{
    /// <summary>
    /// 命令规格生成器示例类，展示如何使用命令规格生成器工具
    /// </summary>
    public static class CommandSpecGeneratorExample
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(CommandSpecGeneratorExample));

        /// <summary>
        /// 生成机器人命令规格类
        /// </summary>
        /// <param name="outputDirectory">输出目录</param>
        public static void GenerateRobotCommands(string outputDirectory)
        {
            try
            {
                // 创建机器人命令列表
                var robotCommands = new List<CommandSpecGenerator.CommandInfo>
                {
                    new CommandSpecGenerator.CommandInfo
                    {
                        DeviceType = EnuMcuDeviceType.Robot,
                        CommandCode = "AR1",
                        CommandName = "T-axis Smooth end move to chamber A",
                        Description = "T-axis Smooth end move to chamber A",
                        DescriptionCn = "T轴Smooth端移动到工艺腔室A",
                        EnumType = "EnuRobotCmdIndex",
                        EnumValue = "MoveMotor",
                        DefaultTimeout = 5000
                    },
                    new CommandSpecGenerator.CommandInfo
                    {
                        DeviceType = EnuMcuDeviceType.Robot,
                        CommandCode = "AR2",
                        CommandName = "T-axis Smooth end move to chamber B",
                        Description = "T-axis Smooth end move to chamber B",
                        DescriptionCn = "T轴Smooth端移动到工艺腔室B",
                        EnumType = "EnuRobotCmdIndex",
                        EnumValue = "AlarmReset",
                        DefaultTimeout = 5000
                    },
                    new CommandSpecGenerator.CommandInfo
                    {
                        DeviceType = EnuMcuDeviceType.Robot,
                        CommandCode = "Move_T_Axis",
                        CommandName = "Home R-axis",
                        Description = "Home R-axis",
                        DescriptionCn = "R轴回原点",
                        EnumType = "EnuRobotCmdIndex",
                        EnumValue = "Move_T_Axis",
                        DefaultTimeout = 10000
                    }
                };

                // 生成命令规格类文件
                var filePaths = CommandSpecGenerator.GenerateCommandSpecClasses(robotCommands, outputDirectory);
                _logger.Info($"已生成 {filePaths.Count} 个机器人命令规格类文件");
            }
            catch (Exception ex)
            {
                _logger.Error($"生成机器人命令规格类文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 生成Shuttle命令规格类
        /// </summary>
        /// <param name="outputDirectory">输出目录</param>
        public static void GenerateShuttleCommands(string outputDirectory)
        {
            try
            {
                // 创建Shuttle命令列表
                var shuttleCommands = new List<CommandSpecGenerator.CommandInfo>
                {
                    new CommandSpecGenerator.CommandInfo
                    {
                        DeviceType = EnuMcuDeviceType.Shuttle,
                        CommandCode = "S1 SD",
                        CommandName = "Shuttle down",
                        Description = "Shuttle down",
                        DescriptionCn = "Shuttle下降",
                        EnumType = "EnuShuttleCmdIndex",
                        EnumValue = "S1_SD",
                        DefaultTimeout = 22000
                    },
                    new CommandSpecGenerator.CommandInfo
                    {
                        DeviceType = EnuMcuDeviceType.Shuttle,
                        CommandCode = "S2 SU",
                        CommandName = "Shuttle up",
                        Description = "Shuttle up",
                        DescriptionCn = "Shuttle上升",
                        EnumType = "EnuShuttleCmdIndex",
                        EnumValue = "S2_SU",
                        DefaultTimeout = 22000
                    },
                    new CommandSpecGenerator.CommandInfo
                    {
                        DeviceType = EnuMcuDeviceType.Shuttle,
                        CommandCode = "S5 CD CD",
                        CommandName = "Close cassette door",
                        Description = "Close cassette door",
                        DescriptionCn = "关闭晶圆盒门",
                        EnumType = "EnuShuttleCmdIndex",
                        EnumValue = "S5_CD_CD",
                        DefaultTimeout = 15000
                    }
                };

                // 生成命令规格类文件
                var filePaths = CommandSpecGenerator.GenerateCommandSpecClasses(shuttleCommands, outputDirectory);
                _logger.Info($"已生成 {filePaths.Count} 个Shuttle命令规格类文件");
            }
            catch (Exception ex)
            {
                _logger.Error($"生成Shuttle命令规格类文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 生成工艺腔室A命令规格类
        /// </summary>
        /// <param name="outputDirectory">输出目录</param>
        public static void GenerateChaCommands(string outputDirectory)
        {
            try
            {
                // 创建工艺腔室A命令列表
                var chaCommands = new List<CommandSpecGenerator.CommandInfo>
                {
                    new CommandSpecGenerator.CommandInfo
                    {
                        DeviceType = EnuMcuDeviceType.ChamberA,
                        CommandCode = "ASP1",
                        CommandName = "Open Door",
                        Description = "Open Door",
                        DescriptionCn = "工艺腔室A开门",
                        EnumType = "EnuChaCmdIndex",
                        EnumValue = "OD_SD",
                        DefaultTimeout = 30000
                    },
                    new CommandSpecGenerator.CommandInfo
                    {
                        DeviceType = EnuMcuDeviceType.ChamberA,
                        CommandCode = "ASP2",
                        CommandName = "Close Door",
                        Description = "Close Door",
                        DescriptionCn = "工艺腔室A关门",
                        EnumType = "EnuChaCmdIndex",
                        EnumValue = "CD_SD",
                        DefaultTimeout = 30000
                    },
                    new CommandSpecGenerator.CommandInfo
                    {
                        DeviceType = EnuMcuDeviceType.ChamberA,
                        CommandCode = "ASP7",
                        CommandName = "Pump up",
                        Description = "Pump up",
                        DescriptionCn = "工艺腔室A抽真空",
                        EnumType = "EnuChaCmdIndex",
                        EnumValue = "PU",
                        DefaultTimeout = 60000
                    }
                };

                // 生成命令规格类文件
                var filePaths = CommandSpecGenerator.GenerateCommandSpecClasses(chaCommands, outputDirectory);
                _logger.Info($"已生成 {filePaths.Count} 个工艺腔室A命令规格类文件");
            }
            catch (Exception ex)
            {
                _logger.Error($"生成工艺腔室A命令规格类文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 主方法，运行生成器示例
        /// </summary>
        /// <param name="args">命令行参数</param>
        public static void RunExample(string[] args)
        {
            try
            {
                // 获取输出目录
                string baseOutputDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "GeneratedCommands");
                if (args != null && args.Length > 0 && !string.IsNullOrWhiteSpace(args[0]))
                {
                    baseOutputDir = args[0];
                }

                // 确保目录存在
                Directory.CreateDirectory(baseOutputDir);

                _logger.Info($"开始生成命令规格类文件，输出目录: {baseOutputDir}");

                // 生成各类设备的命令规格
                GenerateRobotCommands(baseOutputDir);
                GenerateShuttleCommands(baseOutputDir);
                GenerateChaCommands(baseOutputDir);

                _logger.Info("命令规格类文件生成完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"运行命令规格生成器示例时发生错误: {ex.Message}", ex);
            }
        }
    }
}